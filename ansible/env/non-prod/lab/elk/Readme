#These ansible scrits are run using CICD pipeline.

#Control Node: Server where ansible is installed and where the script are fired from.
--------------
#Managed Nodes: The network devices (and/or servers) you manage with Ansible. Managed nodes are also sometimes called “hosts”. Ansible is not installed on managed nodes
--------------


#Before you can run the pipelines you need to perform some prep steps.
----------------------------------------------------------------------

#In this case Control Node is an ubuntu server ( in this case api-gateway server)
#Install ansible on that ubuntu server.
 $ sudo apt-get install ansible

#Set up Linux Managed Nodes.
-------------------------------
#Setup passwordless access from Control Nodes to Managed nodes.

#Create an ssh key pair using ssh-keygen. The keys will be created in ~/.ssh/ dir.

$ssh-keygen

#Copy the private key of the managed node to /tmp in Control node. (lets say ley name: managed-node-private-key )

#Copy the public key of control node to the managed nodes authourized_keys file. Do this for each node.

$cd /tmp
$cat ~/.ssh/id_rsa.pub | ssh -i <managed-node-private-key> ubuntu@<ipaddress-of-managed-node> "cat - >> ~/.ssh/authorized_keys2"


#Setup Windows Manages nodes
-------------------------------

#Install winrm on Control Node

$sudo apt install python3-pip
$pip install "pywinrm>=0.3.0"

#Check group_vars/win.yml. All the details will remain same except the password of managed widows server.
#When you are running the scripts from pipeline, the password will be provided in the pipeline. Leave all the parms unchanged in that case.
#group_vars/win.yml is referred to in elk.yml

#Winrm setup on Managed windows node.

#run the following in PowerShell:
#**********************************

$url = "https://raw.githubusercontent.com/ansible/ansible/devel/examples/scripts/ConfigureRemotingForAnsible.ps1"
$file = "$env:temp\ConfigureRemotingForAnsible.ps1"

(New-Object -TypeName System.Net.WebClient).DownloadFile($url, $file)

powershell.exe -ExecutionPolicy ByPass -File $file

#**********************************

#Check the running listeners on Windows PowerShell using command below.
$winrm enumerate winrm/config/Listener



####################################################
#Ansible variables
####################################################

#Change the hostnames in "hosts" file accordingly.

#Change the variables in group_vars/all.yml
