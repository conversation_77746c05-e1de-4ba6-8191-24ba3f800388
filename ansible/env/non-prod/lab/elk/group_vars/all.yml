ansible_system_user: root

hostgroup_es: __eshosts__
hostgroup_beats_linux: __beathost_linux__
hostgroup_beats_win: __beathost_winapp__


elk_home_dir: /usr/share

#Security
elastic_user: elastic           #This is the es superuser you will use in kibana console. You can create more users from kibana console.
elastic_password: __elasticpasswd__  #Change this password accordingly
kibana_user: kibana_system      #This is the kibana_system user that k<PERSON><PERSON> uses to connect to elasticsearch
kibana_password: __kibanapasswd__     #Change this password accordingly


#elasticsearch.yml.j2 variables
es_cluster_name: wapol_lab_es_cluster
es_local_port: 9200
es_listen_external: false
es_public_ip:
es_datapath: /es/data
es_logpath: /es/logs
es_host_private_ip: **************

#Kibana.yml.j2 variables
kibana_server_name: wapol_lab_kibana
kibana_listen_external: false
kibana_public_ip:
