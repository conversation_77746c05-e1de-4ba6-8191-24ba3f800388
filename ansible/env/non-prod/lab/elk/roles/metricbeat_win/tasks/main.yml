---


- name: Download metricbeat on windows to specified path
  win_get_url:
    url: https://artifacts.elastic.co/downloads/beats/metricbeat/metricbeat-7.8.0-windows-x86_64.zip
    dest: C:\Users\<USER>\Downloads
  ignore_errors: true

- name: Unzip a bz2 (BZip) file
  win_unzip:
    src: C:\Users\<USER>\Downloads\metricbeat-7.8.0-windows-x86_64.zip
    dest: C:\Program Files (x86)\
  #  creates: C:\Program Files (x86)\Metricbeat
  ignore_errors: true

- name: Rename metricbeat-7.8.0-windows-x86_64 to Metricbeat using cmd command
  win_command: cmd.exe /c ren "C:\Program Files (x86)\metricbeat-7.8.0-windows-x86_64" "Metricbeat"
  ignore_errors: true

# - name: Remove metricbeat-7.8.0-windows-x86_64 directory structure if rename fails
#   win_file:
#     path: C:\Program Files (x86)\metricbeat-7.8.0-windows-x86_64
#     state: absent

- name: Install-service-metricbeat.ps1 in Metricbeat directory using cmd command
  win_command: cmd.exe /c powershell -file "C:\Program Files (x86)\Metricbeat\install-service-metricbeat.ps1"
  ignore_errors: true


- name: Create metricbeat.yml file from a Jinja2 template
  win_template:
    src: metricbeat.yml.j2
    dest: C:\Program Files (x86)\Metricbeat\metricbeat.yml
    force: yes
  ignore_errors: true

- name: Start service metricbeat
  win_service:
     name: metricbeat
     start_mode: auto
     state: restarted
  ignore_errors: true
