---


#Download the public signing key:
- name: Install elasticsearch/kibana GPG key
  become: true
  command: wget https://artifacts.elastic.co/GPG-KEY-elasticsearch
  args:
    chdir: "/tmp"
    warn: no
  register: GPGKEY
#- debug:
#    var: GPGKEY

#Install the public signing key:
- name: Add elasticsearch/kibana GPG KEY
  become: true
  command: apt-key add /tmp/GPG-KEY-elasticsearch
  ignore_errors: true


#Install apt-transport-https package as a pre-requisite
# - name: Install apt-transport-https pkg
#   become: true
#   apt: name=apt-transport-https state=present update_cache=no


#Save the repository definition to /etc/apt/sources.list.d/elastic-7.x.list:
- name: Add repo for Elasticsearch/Kibana
  become: true
  command: echo "deb https://artifacts.elastic.co/packages/7.x/apt stable main" > /etc/apt/sources.list.d/elastic-7.x.list
  args:
    _uses_shell: true

#Create Kibana group
- name: Ensure group "kibana" exists
  become: true
  group:
    name: kibana
    state: present

#Create user <PERSON><PERSON><PERSON> in root group
- name: Add the user kibana in kibana group
  become: true
  user:
    name: kibana
    shell: /bin/bash
    groups: kibana
    append: yes

#Check err log exists
# - name: check err log and directory for kibana
#   become: true
#   stat:
#     path: /var/log/kibana/kibana.log
#   register: kibana_log_exists

- name: Create /var/log/kibana directory if it does not exist
  become: true
  file:
    path: /var/log/kibana
    state: directory
    mode: '0755'
    owner: kibana
    group: kibana

#Create error log dir for kibana
- name: create err log  for kibana
  become: true
  file:
    path: /var/log/kibana/kibana.log
    state: touch
    owner: kibana
    group: kibana
    mode: '0755'
  #ignore_errors: true

#Install kibana
- name: Install kibana
  become: true
  apt: name=kibana state=present update_cache=yes
  ignore_errors: true


#Update the kibana Configuration
- name: Update kibana config using template
  become: true
  template:
    src=kibana.yml.j2
    dest=/etc/kibana/kibana.yml
    owner=kibana
    group=kibana
    mode=0660
  ignore_errors: true

#Include security tasks
- name: Include security setup tasks for Kibana
  include: kibana_security.yml
  tags:
    - kibana_security

#Start/Restart kibana
- name: Restart Kibana
  become: true
  command: systemctl restart kibana
  ignore_errors: true

#Enable Kibana service
- name: Enable kibana service
  systemd: name=kibana state=started enabled=yes
  become: true
  ignore_errors: true
