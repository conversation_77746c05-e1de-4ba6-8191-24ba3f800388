---


#Kibana keystore tasks
- name: keystore tasks
  block:
    - name: move keystore to keystore.old
      shell: 
        cmd: "mv /etc/kibana/kibana.keystore /etc/kibana/kibana.keystore.old"

    - name: create new keystore
      shell:
        cmd: "./kibana-keystore create "
        chdir: "{{elk_home_dir}}/kibana/bin"

    - name: keystore - add elasticsearch.password
      shell:
        cmd: "{{elk_home_dir}}/kibana/bin/kibana-keystore add elasticsearch.password --stdin --force"
        stdin: "{{ kibana_password }}"
    - name: keystore - add elasticsearch.username
      shell:
        cmd: "{{elk_home_dir}}/kibana/bin/kibana-keystore add elasticsearch.username --stdin --force"
        stdin: "{{ kibana_user }}"
  become_user: kibana
  become: true
  ignore_errors: true
  #no_log: true
