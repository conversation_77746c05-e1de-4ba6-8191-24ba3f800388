---

#Download the public signing key:

- name: Install ELK GPG key
  become: true
  command: wget https://artifacts.elastic.co/GPG-KEY-elasticsearch
  args:
    chdir: "/tmp"
    warn: no
 #register: GPGKEY
#- debug:
#    var: GPGKEY

#Install the public signing key:
- name: Add ELK GPG KEY
  become: true
  command: apt-key add /tmp/GPG-KEY-elasticsearch


#Install apt-transport-https package as a pre-requisite
- name: Install apt-transport-https pkg
  become: true
  apt: name=apt-transport-https state=present update_cache=yes


#Save the repository definition to /etc/apt/sources.list.d/elastic-7.x.list:
- name: ELK Repo definition
  become: true
  command: echo "deb https://artifacts.elastic.co/packages/7.x/apt stable main" > /etc/apt/sources.list.d/elastic-7.x.list
  args:
    _uses_shell: true



- name: Install metricbeat ubuntu
  apt: name=metricbeat state=present update_cache=yes
  become: true
  ignore_errors: true

- name: Generate metricbeat configuration template
  template:
    src=metricbeat.yml.j2
    dest=/etc/metricbeat/metricbeat.yml
    owner=root
    group=root
    mode=0644
  become: true
  register: metricbeat_needs_restart
  ignore_errors: true

- name: Include metricbeat security tasks
  include: metricbeat_security.yml
  tags:
    - metricbeat_security
  

# - name: Start metricbeat service
#   command: systemctl restart metricbeat.service
#   ignore_errors: true
#   become: true
#   #when: (metricbeat_needs_restart != 0)

#Pause playbook for a minute otherwise metricbeat fails to restart too early
- name: Pause playbook for 30 sec otherwise metricbeat fails to restart too early
  # pause:
  #   minutes: 1
  wait_for:
    timeout: 30


- name: Restart and Enable metricbeat service
  systemd: name=metricbeat state=restarted enabled=yes
  become: true
  ignore_errors: true
