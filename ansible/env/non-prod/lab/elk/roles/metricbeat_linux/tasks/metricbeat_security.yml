---


- name: keystore tasks
  block:
    - name: create keystore if not existing yet
      shell:
        cmd: "metricbeat keystore create"
        #chdir: "{{elk_home_dir}}/metricbeat/bin"
        creates: /var/lib/metricbeat/metricbeat.keystore

    - name: keystore - add elasticsearch.password
      shell:
        cmd: "metricbeat keystore add met_es_password --stdin --force"
        stdin: "{{ elastic_password }}"
    - name: keystore - add elasticsearch.username
      shell:
        cmd: "metricbeat keystore add met_es_username --stdin --force"
        stdin: "{{ elastic_user }}"
  become: true
  #no_log: true
