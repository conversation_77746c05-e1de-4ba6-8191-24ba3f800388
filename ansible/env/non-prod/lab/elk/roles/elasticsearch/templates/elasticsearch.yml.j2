# ======================== Elasticsearch Configuration =========================
#
# NOTE: Elasticsearch comes with reasonable defaults for most settings.
#       Before you set out to tweak and tune the configuration, make sure you
#       understand what are you trying to accomplish and the consequences.
#
# The primary way of configuring a node is via this file. This template lists
# the most important settings you may want to configure for a production cluster.
#
# ---------------------------------- Cluster -----------------------------------
#
# Use a descriptive name for your cluster:

cluster.name: {{ es_cluster_name }}

#
#If its a single node cluster use below otherwise comment it.

discovery.type: single-node

# ------------------------------------ Node ------------------------------------
#
# Use a descriptive name for the node:
#
#node.name: node-1
#
# Add custom attributes to the node:
#
#node.attr.rack: r1
#


# --------------------------------- Security ----------------------------------

xpack.security.enabled: true

# ----------------------------------- Paths ------------------------------------
#
# Path to directory where to store the data (separate multiple locations by comma):

path.data: {{ es_datapath }}

# Path to log files:

path.logs: {{ es_logpath}}


# --------------------------------- Network ----------------------------------

# # Set the bind address to a specific IP (IPv4 or IPv6):

{% if es_listen_external %}
network.host: {{ es_public_ip }}
{% endif %}
{% if not es_listen_external %}
network.host: {{ ansible_default_ipv4.address }}
{% endif %}

#
# Set a custom port for HTTP:
#
http.port: 9200
#
# For more information, consult the network module documentation.
#
# --------------------------------- Memory ----------------------------------
# Lock the memory on startup:

#bootstrap.memory_lock: true

# Make sure that the heap size is set to about half the memory available
# on the system and that the owner of the process is allowed to use this
# limit. This is taken care by the script
#
# Elasticsearch performs poorly when the system is swapping the memory.

# --------------------------------- Discovery ----------------------------------
#
# Pass an initial list of hosts to perform discovery when new node is started:
# The default list of hosts is ["127.0.0.1", "[::1]"]
#
#discovery.zen.ping.unicast.hosts: ["host1", "host2"]
#
# Prevent the "split brain" by configuring the majority of nodes (total number of master-eligible nodes / 2 + 1):
#
#discovery.zen.minimum_master_nodes: 3
#
# For more information, consult the zen discovery module documentation.
#
# ---------------------------------- Gateway -----------------------------------
#
# Block initial recovery after a full cluster restart until N nodes are started:
#
#gateway.recover_after_nodes: 3
#
# For more information, consult the gateway module documentation.
#
# ---------------------------------- Various -----------------------------------
#
# Require explicit names when deleting indices:
#
#action.destructive_requires_name: true
