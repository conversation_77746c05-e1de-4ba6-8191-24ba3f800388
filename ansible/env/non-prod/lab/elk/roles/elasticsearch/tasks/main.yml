---

#Download the public signing key:

- name: Install elasticsearch GPG key
  become: true
  command: wget https://artifacts.elastic.co/GPG-KEY-elasticsearch
  args:
    chdir: "/tmp"
    warn: no
 #register: GPGKEY
#- debug:
#    var: GPGKEY

#Install the public signing key:
- name: Add GPG KEY
  become: true
  command: apt-key add /tmp/GPG-KEY-elasticsearch


#Install apt-transport-https package as a pre-requisite
- name: Install apt-transport-https pkg
  become: true
  apt: name=apt-transport-https state=present update_cache=yes


#Save the repository definition to /etc/apt/sources.list.d/elastic-7.x.list:
- name: Repo definition
  become: true
  command: echo "deb https://artifacts.elastic.co/packages/7.x/apt stable main" > /etc/apt/sources.list.d/elastic-7.x.list
  args:
    _uses_shell: true

#install default-jre
- name: Install default jre and jdk
  become: true
  apt: name={{item}} state=present update_cache=yes
  with_items:
    - default-jre
    - default-jdk

#Create Elasticsearch group
- name: Ensure group "elasticsearch" exists
  become: true
  group:
    name: elasticsearch
    state: present

#Create user elsaticsearch in root group
- name: Add the user elsaticsearch in elasticsearch group
  become: true
  user:
    name: elasticsearch
    shell: /bin/bash
    groups: elasticsearch
    append: yes

#Create data dir
- name: make data directory
  become: true
  file:
    path: /es/data/
    state: directory
    owner: elasticsearch
    group: elasticsearch
    mode: 0755

#Create log dir
- name: make data directory
  become: true
  file:
    path: /es/logs/
    state: directory
    owner: elasticsearch
    group: elasticsearch
    mode: 0755

- name: Install elasticsearch
  become: true
  apt: name=elasticsearch state=present update_cache=yes
  ignore_errors: true


#Copy elasticsearch config vars_files
- name: Copy templated elasticsearch.yml
  become: true
  template:
    src=elasticsearch.yml.j2
    dest=/etc/elasticsearch/elasticsearch.yml
    owner=root
    group=elasticsearch
    mode=0660
  register: elasticsearch_yml_updated
  ignore_errors: true

# - debug:
#     var: elasticsearch_yml_updated

#Set minimum heapsize to half of the memory
- name: Set minimum heapsize in jvm.options to half of  total available memory
  become: true
  lineinfile:
    path=/etc/elasticsearch/jvm.options
    regexp='^-Xms1g'
    line='-Xms{{ (ansible_memory_mb.real.total / 2) | int }}m'
  when: ansible_memory_mb | int < 65536
  register: elasticsearch_heap_updated
  ignore_errors: true


#Set minimum heapsize to half of the memory
- name: Set maximum heapsize in jvm.otions to half of total available memory
  become: true
  lineinfile:
    path=/etc/elasticsearch/jvm.options
    regexp='^-Xmx1g'
    line='-Xmx{{ (ansible_memory_mb.real.total / 2) | int }}m'
  when: ansible_memory_mb | int < 65536
  register: elasticsearch_heap_updated
  ignore_errors: true


#Heapsize optimization
- name: Apply minimum heapsize tuning for systems with more than 60 gb of memory
  become: true
  lineinfile:
    path=/etc/elasticsearch/jvm.options
    regexp='^-Xms1g'
    line='-Xms30g'
  when: ansible_memory_mb.real.total|int > 60000
  register: elasticsearch_heap_updated
  ignore_errors: true

- name: Apply maximum heapsize tuning for systems with more than 60 gb memory
  become: true
  lineinfile:
    path=/etc/elasticsearch/jvm.options
    regexp='^-Xmx1g'
    line='-Xmx30g'
  when: ansible_memory_mb.real.total|int >= 60000
  register: elasticsearch_heap_updated
  ignore_errors: true


#Start elasticsearch
- name: Start or restart elasticsearch service
  command: systemctl restart elasticsearch.service
  ignore_errors: true
  when: elasticsearch_yml_updated.changed == true or elasticsearch_heap_updated == true
  become: true

#Enable elasticsearch service
- name: Enable elasticsearch service
  systemd: name=elasticsearch state=started enabled=yes
  become: true
  ignore_errors: true

#Security related tasks
- name: include security setup tasks
  include: es_security.yml
  tags:
    - es_security
