---


- name: Elastcisearch set passwords
  block:
    #set bootstrap password for native user: elastic
    - name: set password for elasticsearch native user elastic
      become: true
      #shell: printf "{{ elastic_password }}" | {{elk_home_dir}}/elasticsearch/bin/elasticsearch-keystore add -f "bootstrap.password" -x
      shell:
        cmd: "{{elk_home_dir}}/elasticsearch/bin/elasticsearch-keystore add bootstrap.password --stdin --force"
        stdin: "{{ elastic_password }}"

    #Start elasticsearch
    - name: Start or restart elasticsearch service
      command: systemctl restart elasticsearch.service
      ignore_errors: true
      become: true

    #Set password for native user: elastic using bootstrap password ( check if necessary)
    - name: set password for elastic_user elastic
      become: true
      shell: curl -u"{{ elastic_user }}":"{{ elastic_password }}" -XPUT -H 'Content-Type:application/json' 'http://{{es_host_private_ip}}:9200/_xpack/security/user/elastic/_password' -d '{ "password":"{{ elastic_password }}" }'


    #set password for native user: kibana_system
    - name: set password for kibana_user kibana_system
      become: true
      shell: curl -u"{{ elastic_user }}":"{{ elastic_password }}" -XPUT -H 'Content-Type:application/json' 'http://{{es_host_private_ip}}:9200/_xpack/security/user/kibana_system/_password' -d '{ "password":"{{ kibana_password }}" }'
  become: true
  #no_log: true
