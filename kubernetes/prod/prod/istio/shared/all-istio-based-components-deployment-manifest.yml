apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  annotations:
    "helm.sh/resource-policy": keep
  labels:
    app: istio-pilot
    chart: istio
    heritage: Tiller
    istio: security
    release: istio
  name: authorizationpolicies.security.istio.io
spec:
  group: security.istio.io
  names:
    categories:
    - istio-io
    - security-istio-io
    kind: AuthorizationPolicy
    listKind: AuthorizationPolicyList
    plural: authorizationpolicies
    singular: authorizationpolicy
  preserveUnknownFields: false
  scope: Namespaced
  subresources:
    status: {}
  validation:
    openAPIV3Schema:
      properties:
        spec:
          description: 'Configuration for access control on workloads. See more details
            at: https://istio.io/docs/reference/config/security/authorization-policy.html'
          oneOf:
          - not:
              anyOf:
              - required:
                - provider
          - required:
            - provider
          properties:
            action:
              description: Optional.
              enum:
              - ALLOW
              - DENY
              - AUDIT
              - CUSTOM
              type: string
            provider:
              description: Specifies detailed configuration of the CUSTOM action.
              properties:
                name:
                  description: Specifies the name of the extension provider.
                  format: string
                  type: string
              type: object
            rules:
              description: Optional.
              items:
                properties:
                  from:
                    description: Optional.
                    items:
                      properties:
                        source:
                          description: Source specifies the source of a request.
                          properties:
                            ipBlocks:
                              description: Optional.
                              items:
                                format: string
                                type: string
                              type: array
                            namespaces:
                              description: Optional.
                              items:
                                format: string
                                type: string
                              type: array
                            notIpBlocks:
                              description: Optional.
                              items:
                                format: string
                                type: string
                              type: array
                            notNamespaces:
                              description: Optional.
                              items:
                                format: string
                                type: string
                              type: array
                            notPrincipals:
                              description: Optional.
                              items:
                                format: string
                                type: string
                              type: array
                            notRemoteIpBlocks:
                              description: Optional.
                              items:
                                format: string
                                type: string
                              type: array
                            notRequestPrincipals:
                              description: Optional.
                              items:
                                format: string
                                type: string
                              type: array
                            principals:
                              description: Optional.
                              items:
                                format: string
                                type: string
                              type: array
                            remoteIpBlocks:
                              description: Optional.
                              items:
                                format: string
                                type: string
                              type: array
                            requestPrincipals:
                              description: Optional.
                              items:
                                format: string
                                type: string
                              type: array
                          type: object
                      type: object
                    type: array
                  to:
                    description: Optional.
                    items:
                      properties:
                        operation:
                          description: Operation specifies the operation of a request.
                          properties:
                            hosts:
                              description: Optional.
                              items:
                                format: string
                                type: string
                              type: array
                            methods:
                              description: Optional.
                              items:
                                format: string
                                type: string
                              type: array
                            notHosts:
                              description: Optional.
                              items:
                                format: string
                                type: string
                              type: array
                            notMethods:
                              description: Optional.
                              items:
                                format: string
                                type: string
                              type: array
                            notPaths:
                              description: Optional.
                              items:
                                format: string
                                type: string
                              type: array
                            notPorts:
                              description: Optional.
                              items:
                                format: string
                                type: string
                              type: array
                            paths:
                              description: Optional.
                              items:
                                format: string
                                type: string
                              type: array
                            ports:
                              description: Optional.
                              items:
                                format: string
                                type: string
                              type: array
                          type: object
                      type: object
                    type: array
                  when:
                    description: Optional.
                    items:
                      properties:
                        key:
                          description: The name of an Istio attribute.
                          format: string
                          type: string
                        notValues:
                          description: Optional.
                          items:
                            format: string
                            type: string
                          type: array
                        values:
                          description: Optional.
                          items:
                            format: string
                            type: string
                          type: array
                      type: object
                    type: array
                type: object
              type: array
            selector:
              description: Optional.
              properties:
                matchLabels:
                  additionalProperties:
                    format: string
                    type: string
                  type: object
              type: object
          type: object
        status:
          type: object
          x-kubernetes-preserve-unknown-fields: true
      type: object
  versions:
  - name: v1beta1
    served: true
    storage: true
---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  annotations:
    "helm.sh/resource-policy": keep
  labels:
    app: istio-pilot
    chart: istio
    heritage: Tiller
    release: istio
  name: destinationrules.networking.istio.io
spec:
  additionalPrinterColumns:
  - JSONPath: .spec.host
    description: The name of a service from the service registry
    name: Host
    type: string
  - JSONPath: .metadata.creationTimestamp
    description: 'CreationTimestamp is a timestamp representing the server time when
      this object was created. It is not guaranteed to be set in happens-before order
      across separate operations. Clients may not set this value. It is represented
      in RFC3339 form and is in UTC. Populated by the system. Read-only. Null for
      lists. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#metadata'
    name: Age
    type: date
  group: networking.istio.io
  names:
    categories:
    - istio-io
    - networking-istio-io
    kind: DestinationRule
    listKind: DestinationRuleList
    plural: destinationrules
    shortNames:
    - dr
    singular: destinationrule
  preserveUnknownFields: false
  scope: Namespaced
  subresources:
    status: {}
  validation:
    openAPIV3Schema:
      properties:
        spec:
          description: 'Configuration affecting load balancing, outlier detection,
            etc. See more details at: https://istio.io/docs/reference/config/networking/destination-rule.html'
          properties:
            exportTo:
              description: A list of namespaces to which this destination rule is
                exported.
              items:
                format: string
                type: string
              type: array
            host:
              description: The name of a service from the service registry.
              format: string
              type: string
            subsets:
              items:
                properties:
                  labels:
                    additionalProperties:
                      format: string
                      type: string
                    type: object
                  name:
                    description: Name of the subset.
                    format: string
                    type: string
                  trafficPolicy:
                    description: Traffic policies that apply to this subset.
                    properties:
                      connectionPool:
                        properties:
                          http:
                            description: HTTP connection pool settings.
                            properties:
                              h2UpgradePolicy:
                                description: Specify if http1.1 connection should
                                  be upgraded to http2 for the associated destination.
                                enum:
                                - DEFAULT
                                - DO_NOT_UPGRADE
                                - UPGRADE
                                type: string
                              http1MaxPendingRequests:
                                description: Maximum number of pending HTTP requests
                                  to a destination.
                                format: int32
                                type: integer
                              http2MaxRequests:
                                description: Maximum number of requests to a backend.
                                format: int32
                                type: integer
                              idleTimeout:
                                description: The idle timeout for upstream connection
                                  pool connections.
                                type: string
                              maxRequestsPerConnection:
                                description: Maximum number of requests per connection
                                  to a backend.
                                format: int32
                                type: integer
                              maxRetries:
                                format: int32
                                type: integer
                              useClientProtocol:
                                description: If set to true, client protocol will
                                  be preserved while initiating connection to backend.
                                type: boolean
                            type: object
                          tcp:
                            description: Settings common to both HTTP and TCP upstream
                              connections.
                            properties:
                              connectTimeout:
                                description: TCP connection timeout.
                                type: string
                              maxConnections:
                                description: Maximum number of HTTP1 /TCP connections
                                  to a destination host.
                                format: int32
                                type: integer
                              tcpKeepalive:
                                description: If set then set SO_KEEPALIVE on the socket
                                  to enable TCP Keepalives.
                                properties:
                                  interval:
                                    description: The time duration between keep-alive
                                      probes.
                                    type: string
                                  probes:
                                    type: integer
                                  time:
                                    type: string
                                type: object
                            type: object
                        type: object
                      loadBalancer:
                        description: Settings controlling the load balancer algorithms.
                        oneOf:
                        - not:
                            anyOf:
                            - required:
                              - simple
                            - properties:
                                consistentHash:
                                  oneOf:
                                  - not:
                                      anyOf:
                                      - required:
                                        - httpHeaderName
                                      - required:
                                        - httpCookie
                                      - required:
                                        - useSourceIp
                                      - required:
                                        - httpQueryParameterName
                                  - required:
                                    - httpHeaderName
                                  - required:
                                    - httpCookie
                                  - required:
                                    - useSourceIp
                                  - required:
                                    - httpQueryParameterName
                              required:
                              - consistentHash
                        - required:
                          - simple
                        - properties:
                            consistentHash:
                              oneOf:
                              - not:
                                  anyOf:
                                  - required:
                                    - httpHeaderName
                                  - required:
                                    - httpCookie
                                  - required:
                                    - useSourceIp
                                  - required:
                                    - httpQueryParameterName
                              - required:
                                - httpHeaderName
                              - required:
                                - httpCookie
                              - required:
                                - useSourceIp
                              - required:
                                - httpQueryParameterName
                          required:
                          - consistentHash
                        properties:
                          consistentHash:
                            properties:
                              httpCookie:
                                description: Hash based on HTTP cookie.
                                properties:
                                  name:
                                    description: Name of the cookie.
                                    format: string
                                    type: string
                                  path:
                                    description: Path to set for the cookie.
                                    format: string
                                    type: string
                                  ttl:
                                    description: Lifetime of the cookie.
                                    type: string
                                type: object
                              httpHeaderName:
                                description: Hash based on a specific HTTP header.
                                format: string
                                type: string
                              httpQueryParameterName:
                                description: Hash based on a specific HTTP query parameter.
                                format: string
                                type: string
                              minimumRingSize:
                                type: integer
                              useSourceIp:
                                description: Hash based on the source IP address.
                                type: boolean
                            type: object
                          localityLbSetting:
                            properties:
                              distribute:
                                description: 'Optional: only one of distribute or
                                  failover can be set.'
                                items:
                                  properties:
                                    from:
                                      description: Originating locality, '/' separated,
                                        e.g.
                                      format: string
                                      type: string
                                    to:
                                      additionalProperties:
                                        type: integer
                                      description: Map of upstream localities to traffic
                                        distribution weights.
                                      type: object
                                  type: object
                                type: array
                              enabled:
                                description: enable locality load balancing, this
                                  is DestinationRule-level and will override mesh
                                  wide settings in entirety.
                                nullable: true
                                type: boolean
                              failover:
                                description: 'Optional: only failover or distribute
                                  can be set.'
                                items:
                                  properties:
                                    from:
                                      description: Originating region.
                                      format: string
                                      type: string
                                    to:
                                      format: string
                                      type: string
                                  type: object
                                type: array
                            type: object
                          simple:
                            enum:
                            - ROUND_ROBIN
                            - LEAST_CONN
                            - RANDOM
                            - PASSTHROUGH
                            type: string
                        type: object
                      outlierDetection:
                        properties:
                          baseEjectionTime:
                            description: Minimum ejection duration.
                            type: string
                          consecutive5xxErrors:
                            description: Number of 5xx errors before a host is ejected
                              from the connection pool.
                            nullable: true
                            type: integer
                          consecutiveErrors:
                            format: int32
                            type: integer
                          consecutiveGatewayErrors:
                            description: Number of gateway errors before a host is
                              ejected from the connection pool.
                            nullable: true
                            type: integer
                          interval:
                            description: Time interval between ejection sweep analysis.
                            type: string
                          maxEjectionPercent:
                            format: int32
                            type: integer
                          minHealthPercent:
                            format: int32
                            type: integer
                        type: object
                      portLevelSettings:
                        description: Traffic policies specific to individual ports.
                        items:
                          properties:
                            connectionPool:
                              properties:
                                http:
                                  description: HTTP connection pool settings.
                                  properties:
                                    h2UpgradePolicy:
                                      description: Specify if http1.1 connection should
                                        be upgraded to http2 for the associated destination.
                                      enum:
                                      - DEFAULT
                                      - DO_NOT_UPGRADE
                                      - UPGRADE
                                      type: string
                                    http1MaxPendingRequests:
                                      description: Maximum number of pending HTTP
                                        requests to a destination.
                                      format: int32
                                      type: integer
                                    http2MaxRequests:
                                      description: Maximum number of requests to a
                                        backend.
                                      format: int32
                                      type: integer
                                    idleTimeout:
                                      description: The idle timeout for upstream connection
                                        pool connections.
                                      type: string
                                    maxRequestsPerConnection:
                                      description: Maximum number of requests per
                                        connection to a backend.
                                      format: int32
                                      type: integer
                                    maxRetries:
                                      format: int32
                                      type: integer
                                    useClientProtocol:
                                      description: If set to true, client protocol
                                        will be preserved while initiating connection
                                        to backend.
                                      type: boolean
                                  type: object
                                tcp:
                                  description: Settings common to both HTTP and TCP
                                    upstream connections.
                                  properties:
                                    connectTimeout:
                                      description: TCP connection timeout.
                                      type: string
                                    maxConnections:
                                      description: Maximum number of HTTP1 /TCP connections
                                        to a destination host.
                                      format: int32
                                      type: integer
                                    tcpKeepalive:
                                      description: If set then set SO_KEEPALIVE on
                                        the socket to enable TCP Keepalives.
                                      properties:
                                        interval:
                                          description: The time duration between keep-alive
                                            probes.
                                          type: string
                                        probes:
                                          type: integer
                                        time:
                                          type: string
                                      type: object
                                  type: object
                              type: object
                            loadBalancer:
                              description: Settings controlling the load balancer
                                algorithms.
                              oneOf:
                              - not:
                                  anyOf:
                                  - required:
                                    - simple
                                  - properties:
                                      consistentHash:
                                        oneOf:
                                        - not:
                                            anyOf:
                                            - required:
                                              - httpHeaderName
                                            - required:
                                              - httpCookie
                                            - required:
                                              - useSourceIp
                                            - required:
                                              - httpQueryParameterName
                                        - required:
                                          - httpHeaderName
                                        - required:
                                          - httpCookie
                                        - required:
                                          - useSourceIp
                                        - required:
                                          - httpQueryParameterName
                                    required:
                                    - consistentHash
                              - required:
                                - simple
                              - properties:
                                  consistentHash:
                                    oneOf:
                                    - not:
                                        anyOf:
                                        - required:
                                          - httpHeaderName
                                        - required:
                                          - httpCookie
                                        - required:
                                          - useSourceIp
                                        - required:
                                          - httpQueryParameterName
                                    - required:
                                      - httpHeaderName
                                    - required:
                                      - httpCookie
                                    - required:
                                      - useSourceIp
                                    - required:
                                      - httpQueryParameterName
                                required:
                                - consistentHash
                              properties:
                                consistentHash:
                                  properties:
                                    httpCookie:
                                      description: Hash based on HTTP cookie.
                                      properties:
                                        name:
                                          description: Name of the cookie.
                                          format: string
                                          type: string
                                        path:
                                          description: Path to set for the cookie.
                                          format: string
                                          type: string
                                        ttl:
                                          description: Lifetime of the cookie.
                                          type: string
                                      type: object
                                    httpHeaderName:
                                      description: Hash based on a specific HTTP header.
                                      format: string
                                      type: string
                                    httpQueryParameterName:
                                      description: Hash based on a specific HTTP query
                                        parameter.
                                      format: string
                                      type: string
                                    minimumRingSize:
                                      type: integer
                                    useSourceIp:
                                      description: Hash based on the source IP address.
                                      type: boolean
                                  type: object
                                localityLbSetting:
                                  properties:
                                    distribute:
                                      description: 'Optional: only one of distribute
                                        or failover can be set.'
                                      items:
                                        properties:
                                          from:
                                            description: Originating locality, '/'
                                              separated, e.g.
                                            format: string
                                            type: string
                                          to:
                                            additionalProperties:
                                              type: integer
                                            description: Map of upstream localities
                                              to traffic distribution weights.
                                            type: object
                                        type: object
                                      type: array
                                    enabled:
                                      description: enable locality load balancing,
                                        this is DestinationRule-level and will override
                                        mesh wide settings in entirety.
                                      nullable: true
                                      type: boolean
                                    failover:
                                      description: 'Optional: only failover or distribute
                                        can be set.'
                                      items:
                                        properties:
                                          from:
                                            description: Originating region.
                                            format: string
                                            type: string
                                          to:
                                            format: string
                                            type: string
                                        type: object
                                      type: array
                                  type: object
                                simple:
                                  enum:
                                  - ROUND_ROBIN
                                  - LEAST_CONN
                                  - RANDOM
                                  - PASSTHROUGH
                                  type: string
                              type: object
                            outlierDetection:
                              properties:
                                baseEjectionTime:
                                  description: Minimum ejection duration.
                                  type: string
                                consecutive5xxErrors:
                                  description: Number of 5xx errors before a host
                                    is ejected from the connection pool.
                                  nullable: true
                                  type: integer
                                consecutiveErrors:
                                  format: int32
                                  type: integer
                                consecutiveGatewayErrors:
                                  description: Number of gateway errors before a host
                                    is ejected from the connection pool.
                                  nullable: true
                                  type: integer
                                interval:
                                  description: Time interval between ejection sweep
                                    analysis.
                                  type: string
                                maxEjectionPercent:
                                  format: int32
                                  type: integer
                                minHealthPercent:
                                  format: int32
                                  type: integer
                              type: object
                            port:
                              properties:
                                number:
                                  type: integer
                              type: object
                            tls:
                              description: TLS related settings for connections to
                                the upstream service.
                              properties:
                                caCertificates:
                                  format: string
                                  type: string
                                clientCertificate:
                                  description: REQUIRED if mode is `MUTUAL`.
                                  format: string
                                  type: string
                                credentialName:
                                  format: string
                                  type: string
                                mode:
                                  enum:
                                  - DISABLE
                                  - SIMPLE
                                  - MUTUAL
                                  - ISTIO_MUTUAL
                                  type: string
                                privateKey:
                                  description: REQUIRED if mode is `MUTUAL`.
                                  format: string
                                  type: string
                                sni:
                                  description: SNI string to present to the server
                                    during TLS handshake.
                                  format: string
                                  type: string
                                subjectAltNames:
                                  items:
                                    format: string
                                    type: string
                                  type: array
                              type: object
                          type: object
                        type: array
                      tls:
                        description: TLS related settings for connections to the upstream
                          service.
                        properties:
                          caCertificates:
                            format: string
                            type: string
                          clientCertificate:
                            description: REQUIRED if mode is `MUTUAL`.
                            format: string
                            type: string
                          credentialName:
                            format: string
                            type: string
                          mode:
                            enum:
                            - DISABLE
                            - SIMPLE
                            - MUTUAL
                            - ISTIO_MUTUAL
                            type: string
                          privateKey:
                            description: REQUIRED if mode is `MUTUAL`.
                            format: string
                            type: string
                          sni:
                            description: SNI string to present to the server during
                              TLS handshake.
                            format: string
                            type: string
                          subjectAltNames:
                            items:
                              format: string
                              type: string
                            type: array
                        type: object
                    type: object
                type: object
              type: array
            trafficPolicy:
              properties:
                connectionPool:
                  properties:
                    http:
                      description: HTTP connection pool settings.
                      properties:
                        h2UpgradePolicy:
                          description: Specify if http1.1 connection should be upgraded
                            to http2 for the associated destination.
                          enum:
                          - DEFAULT
                          - DO_NOT_UPGRADE
                          - UPGRADE
                          type: string
                        http1MaxPendingRequests:
                          description: Maximum number of pending HTTP requests to
                            a destination.
                          format: int32
                          type: integer
                        http2MaxRequests:
                          description: Maximum number of requests to a backend.
                          format: int32
                          type: integer
                        idleTimeout:
                          description: The idle timeout for upstream connection pool
                            connections.
                          type: string
                        maxRequestsPerConnection:
                          description: Maximum number of requests per connection to
                            a backend.
                          format: int32
                          type: integer
                        maxRetries:
                          format: int32
                          type: integer
                        useClientProtocol:
                          description: If set to true, client protocol will be preserved
                            while initiating connection to backend.
                          type: boolean
                      type: object
                    tcp:
                      description: Settings common to both HTTP and TCP upstream connections.
                      properties:
                        connectTimeout:
                          description: TCP connection timeout.
                          type: string
                        maxConnections:
                          description: Maximum number of HTTP1 /TCP connections to
                            a destination host.
                          format: int32
                          type: integer
                        tcpKeepalive:
                          description: If set then set SO_KEEPALIVE on the socket
                            to enable TCP Keepalives.
                          properties:
                            interval:
                              description: The time duration between keep-alive probes.
                              type: string
                            probes:
                              type: integer
                            time:
                              type: string
                          type: object
                      type: object
                  type: object
                loadBalancer:
                  description: Settings controlling the load balancer algorithms.
                  oneOf:
                  - not:
                      anyOf:
                      - required:
                        - simple
                      - properties:
                          consistentHash:
                            oneOf:
                            - not:
                                anyOf:
                                - required:
                                  - httpHeaderName
                                - required:
                                  - httpCookie
                                - required:
                                  - useSourceIp
                                - required:
                                  - httpQueryParameterName
                            - required:
                              - httpHeaderName
                            - required:
                              - httpCookie
                            - required:
                              - useSourceIp
                            - required:
                              - httpQueryParameterName
                        required:
                        - consistentHash
                  - required:
                    - simple
                  - properties:
                      consistentHash:
                        oneOf:
                        - not:
                            anyOf:
                            - required:
                              - httpHeaderName
                            - required:
                              - httpCookie
                            - required:
                              - useSourceIp
                            - required:
                              - httpQueryParameterName
                        - required:
                          - httpHeaderName
                        - required:
                          - httpCookie
                        - required:
                          - useSourceIp
                        - required:
                          - httpQueryParameterName
                    required:
                    - consistentHash
                  properties:
                    consistentHash:
                      properties:
                        httpCookie:
                          description: Hash based on HTTP cookie.
                          properties:
                            name:
                              description: Name of the cookie.
                              format: string
                              type: string
                            path:
                              description: Path to set for the cookie.
                              format: string
                              type: string
                            ttl:
                              description: Lifetime of the cookie.
                              type: string
                          type: object
                        httpHeaderName:
                          description: Hash based on a specific HTTP header.
                          format: string
                          type: string
                        httpQueryParameterName:
                          description: Hash based on a specific HTTP query parameter.
                          format: string
                          type: string
                        minimumRingSize:
                          type: integer
                        useSourceIp:
                          description: Hash based on the source IP address.
                          type: boolean
                      type: object
                    localityLbSetting:
                      properties:
                        distribute:
                          description: 'Optional: only one of distribute or failover
                            can be set.'
                          items:
                            properties:
                              from:
                                description: Originating locality, '/' separated,
                                  e.g.
                                format: string
                                type: string
                              to:
                                additionalProperties:
                                  type: integer
                                description: Map of upstream localities to traffic
                                  distribution weights.
                                type: object
                            type: object
                          type: array
                        enabled:
                          description: enable locality load balancing, this is DestinationRule-level
                            and will override mesh wide settings in entirety.
                          nullable: true
                          type: boolean
                        failover:
                          description: 'Optional: only failover or distribute can
                            be set.'
                          items:
                            properties:
                              from:
                                description: Originating region.
                                format: string
                                type: string
                              to:
                                format: string
                                type: string
                            type: object
                          type: array
                      type: object
                    simple:
                      enum:
                      - ROUND_ROBIN
                      - LEAST_CONN
                      - RANDOM
                      - PASSTHROUGH
                      type: string
                  type: object
                outlierDetection:
                  properties:
                    baseEjectionTime:
                      description: Minimum ejection duration.
                      type: string
                    consecutive5xxErrors:
                      description: Number of 5xx errors before a host is ejected from
                        the connection pool.
                      nullable: true
                      type: integer
                    consecutiveErrors:
                      format: int32
                      type: integer
                    consecutiveGatewayErrors:
                      description: Number of gateway errors before a host is ejected
                        from the connection pool.
                      nullable: true
                      type: integer
                    interval:
                      description: Time interval between ejection sweep analysis.
                      type: string
                    maxEjectionPercent:
                      format: int32
                      type: integer
                    minHealthPercent:
                      format: int32
                      type: integer
                  type: object
                portLevelSettings:
                  description: Traffic policies specific to individual ports.
                  items:
                    properties:
                      connectionPool:
                        properties:
                          http:
                            description: HTTP connection pool settings.
                            properties:
                              h2UpgradePolicy:
                                description: Specify if http1.1 connection should
                                  be upgraded to http2 for the associated destination.
                                enum:
                                - DEFAULT
                                - DO_NOT_UPGRADE
                                - UPGRADE
                                type: string
                              http1MaxPendingRequests:
                                description: Maximum number of pending HTTP requests
                                  to a destination.
                                format: int32
                                type: integer
                              http2MaxRequests:
                                description: Maximum number of requests to a backend.
                                format: int32
                                type: integer
                              idleTimeout:
                                description: The idle timeout for upstream connection
                                  pool connections.
                                type: string
                              maxRequestsPerConnection:
                                description: Maximum number of requests per connection
                                  to a backend.
                                format: int32
                                type: integer
                              maxRetries:
                                format: int32
                                type: integer
                              useClientProtocol:
                                description: If set to true, client protocol will
                                  be preserved while initiating connection to backend.
                                type: boolean
                            type: object
                          tcp:
                            description: Settings common to both HTTP and TCP upstream
                              connections.
                            properties:
                              connectTimeout:
                                description: TCP connection timeout.
                                type: string
                              maxConnections:
                                description: Maximum number of HTTP1 /TCP connections
                                  to a destination host.
                                format: int32
                                type: integer
                              tcpKeepalive:
                                description: If set then set SO_KEEPALIVE on the socket
                                  to enable TCP Keepalives.
                                properties:
                                  interval:
                                    description: The time duration between keep-alive
                                      probes.
                                    type: string
                                  probes:
                                    type: integer
                                  time:
                                    type: string
                                type: object
                            type: object
                        type: object
                      loadBalancer:
                        description: Settings controlling the load balancer algorithms.
                        oneOf:
                        - not:
                            anyOf:
                            - required:
                              - simple
                            - properties:
                                consistentHash:
                                  oneOf:
                                  - not:
                                      anyOf:
                                      - required:
                                        - httpHeaderName
                                      - required:
                                        - httpCookie
                                      - required:
                                        - useSourceIp
                                      - required:
                                        - httpQueryParameterName
                                  - required:
                                    - httpHeaderName
                                  - required:
                                    - httpCookie
                                  - required:
                                    - useSourceIp
                                  - required:
                                    - httpQueryParameterName
                              required:
                              - consistentHash
                        - required:
                          - simple
                        - properties:
                            consistentHash:
                              oneOf:
                              - not:
                                  anyOf:
                                  - required:
                                    - httpHeaderName
                                  - required:
                                    - httpCookie
                                  - required:
                                    - useSourceIp
                                  - required:
                                    - httpQueryParameterName
                              - required:
                                - httpHeaderName
                              - required:
                                - httpCookie
                              - required:
                                - useSourceIp
                              - required:
                                - httpQueryParameterName
                          required:
                          - consistentHash
                        properties:
                          consistentHash:
                            properties:
                              httpCookie:
                                description: Hash based on HTTP cookie.
                                properties:
                                  name:
                                    description: Name of the cookie.
                                    format: string
                                    type: string
                                  path:
                                    description: Path to set for the cookie.
                                    format: string
                                    type: string
                                  ttl:
                                    description: Lifetime of the cookie.
                                    type: string
                                type: object
                              httpHeaderName:
                                description: Hash based on a specific HTTP header.
                                format: string
                                type: string
                              httpQueryParameterName:
                                description: Hash based on a specific HTTP query parameter.
                                format: string
                                type: string
                              minimumRingSize:
                                type: integer
                              useSourceIp:
                                description: Hash based on the source IP address.
                                type: boolean
                            type: object
                          localityLbSetting:
                            properties:
                              distribute:
                                description: 'Optional: only one of distribute or
                                  failover can be set.'
                                items:
                                  properties:
                                    from:
                                      description: Originating locality, '/' separated,
                                        e.g.
                                      format: string
                                      type: string
                                    to:
                                      additionalProperties:
                                        type: integer
                                      description: Map of upstream localities to traffic
                                        distribution weights.
                                      type: object
                                  type: object
                                type: array
                              enabled:
                                description: enable locality load balancing, this
                                  is DestinationRule-level and will override mesh
                                  wide settings in entirety.
                                nullable: true
                                type: boolean
                              failover:
                                description: 'Optional: only failover or distribute
                                  can be set.'
                                items:
                                  properties:
                                    from:
                                      description: Originating region.
                                      format: string
                                      type: string
                                    to:
                                      format: string
                                      type: string
                                  type: object
                                type: array
                            type: object
                          simple:
                            enum:
                            - ROUND_ROBIN
                            - LEAST_CONN
                            - RANDOM
                            - PASSTHROUGH
                            type: string
                        type: object
                      outlierDetection:
                        properties:
                          baseEjectionTime:
                            description: Minimum ejection duration.
                            type: string
                          consecutive5xxErrors:
                            description: Number of 5xx errors before a host is ejected
                              from the connection pool.
                            nullable: true
                            type: integer
                          consecutiveErrors:
                            format: int32
                            type: integer
                          consecutiveGatewayErrors:
                            description: Number of gateway errors before a host is
                              ejected from the connection pool.
                            nullable: true
                            type: integer
                          interval:
                            description: Time interval between ejection sweep analysis.
                            type: string
                          maxEjectionPercent:
                            format: int32
                            type: integer
                          minHealthPercent:
                            format: int32
                            type: integer
                        type: object
                      port:
                        properties:
                          number:
                            type: integer
                        type: object
                      tls:
                        description: TLS related settings for connections to the upstream
                          service.
                        properties:
                          caCertificates:
                            format: string
                            type: string
                          clientCertificate:
                            description: REQUIRED if mode is `MUTUAL`.
                            format: string
                            type: string
                          credentialName:
                            format: string
                            type: string
                          mode:
                            enum:
                            - DISABLE
                            - SIMPLE
                            - MUTUAL
                            - ISTIO_MUTUAL
                            type: string
                          privateKey:
                            description: REQUIRED if mode is `MUTUAL`.
                            format: string
                            type: string
                          sni:
                            description: SNI string to present to the server during
                              TLS handshake.
                            format: string
                            type: string
                          subjectAltNames:
                            items:
                              format: string
                              type: string
                            type: array
                        type: object
                    type: object
                  type: array
                tls:
                  description: TLS related settings for connections to the upstream
                    service.
                  properties:
                    caCertificates:
                      format: string
                      type: string
                    clientCertificate:
                      description: REQUIRED if mode is `MUTUAL`.
                      format: string
                      type: string
                    credentialName:
                      format: string
                      type: string
                    mode:
                      enum:
                      - DISABLE
                      - SIMPLE
                      - MUTUAL
                      - ISTIO_MUTUAL
                      type: string
                    privateKey:
                      description: REQUIRED if mode is `MUTUAL`.
                      format: string
                      type: string
                    sni:
                      description: SNI string to present to the server during TLS
                        handshake.
                      format: string
                      type: string
                    subjectAltNames:
                      items:
                        format: string
                        type: string
                      type: array
                  type: object
              type: object
          type: object
        status:
          type: object
          x-kubernetes-preserve-unknown-fields: true
      type: object
  versions:
  - name: v1alpha3
    served: true
    storage: true
  - name: v1beta1
    served: true
    storage: false
---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  annotations:
    "helm.sh/resource-policy": keep
  labels:
    app: istio-pilot
    chart: istio
    heritage: Tiller
    release: istio
  name: envoyfilters.networking.istio.io
spec:
  group: networking.istio.io
  names:
    categories:
    - istio-io
    - networking-istio-io
    kind: EnvoyFilter
    listKind: EnvoyFilterList
    plural: envoyfilters
    singular: envoyfilter
  preserveUnknownFields: true
  scope: Namespaced
  subresources:
    status: {}
  validation:
    openAPIV3Schema:
      properties:
        spec:
          description: 'Customizing Envoy configuration generated by Istio. See more
            details at: https://istio.io/docs/reference/config/networking/envoy-filter.html'
          properties:
            configPatches:
              description: One or more patches with match conditions.
              items:
                properties:
                  applyTo:
                    enum:
                    - INVALID
                    - LISTENER
                    - FILTER_CHAIN
                    - NETWORK_FILTER
                    - HTTP_FILTER
                    - ROUTE_CONFIGURATION
                    - VIRTUAL_HOST
                    - HTTP_ROUTE
                    - CLUSTER
                    - EXTENSION_CONFIG
                    type: string
                  match:
                    description: Match on listener/route configuration/cluster.
                    oneOf:
                    - not:
                        anyOf:
                        - required:
                          - listener
                        - required:
                          - routeConfiguration
                        - required:
                          - cluster
                    - required:
                      - listener
                    - required:
                      - routeConfiguration
                    - required:
                      - cluster
                    properties:
                      cluster:
                        description: Match on envoy cluster attributes.
                        properties:
                          name:
                            description: The exact name of the cluster to match.
                            format: string
                            type: string
                          portNumber:
                            description: The service port for which this cluster was
                              generated.
                            type: integer
                          service:
                            description: The fully qualified service name for this
                              cluster.
                            format: string
                            type: string
                          subset:
                            description: The subset associated with the service.
                            format: string
                            type: string
                        type: object
                      context:
                        description: The specific config generation context to match
                          on.
                        enum:
                        - ANY
                        - SIDECAR_INBOUND
                        - SIDECAR_OUTBOUND
                        - GATEWAY
                        type: string
                      listener:
                        description: Match on envoy listener attributes.
                        properties:
                          filterChain:
                            description: Match a specific filter chain in a listener.
                            properties:
                              applicationProtocols:
                                description: Applies only to sidecars.
                                format: string
                                type: string
                              destinationPort:
                                description: The destination_port value used by a
                                  filter chain's match condition.
                                type: integer
                              filter:
                                description: The name of a specific filter to apply
                                  the patch to.
                                properties:
                                  name:
                                    description: The filter name to match on.
                                    format: string
                                    type: string
                                  subFilter:
                                    properties:
                                      name:
                                        description: The filter name to match on.
                                        format: string
                                        type: string
                                    type: object
                                type: object
                              name:
                                description: The name assigned to the filter chain.
                                format: string
                                type: string
                              sni:
                                description: The SNI value used by a filter chain's
                                  match condition.
                                format: string
                                type: string
                              transportProtocol:
                                description: Applies only to `SIDECAR_INBOUND` context.
                                format: string
                                type: string
                            type: object
                          name:
                            description: Match a specific listener by its name.
                            format: string
                            type: string
                          portName:
                            format: string
                            type: string
                          portNumber:
                            type: integer
                        type: object
                      proxy:
                        description: Match on properties associated with a proxy.
                        properties:
                          metadata:
                            additionalProperties:
                              format: string
                              type: string
                            type: object
                          proxyVersion:
                            format: string
                            type: string
                        type: object
                      routeConfiguration:
                        description: Match on envoy HTTP route configuration attributes.
                        properties:
                          gateway:
                            format: string
                            type: string
                          name:
                            description: Route configuration name to match on.
                            format: string
                            type: string
                          portName:
                            description: Applicable only for GATEWAY context.
                            format: string
                            type: string
                          portNumber:
                            type: integer
                          vhost:
                            properties:
                              name:
                                format: string
                                type: string
                              route:
                                description: Match a specific route within the virtual
                                  host.
                                properties:
                                  action:
                                    description: Match a route with specific action
                                      type.
                                    enum:
                                    - ANY
                                    - ROUTE
                                    - REDIRECT
                                    - DIRECT_RESPONSE
                                    type: string
                                  name:
                                    format: string
                                    type: string
                                type: object
                            type: object
                        type: object
                    type: object
                  patch:
                    description: The patch to apply along with the operation.
                    properties:
                      filterClass:
                        description: Determines the filter insertion order.
                        enum:
                        - UNSPECIFIED
                        - AUTHN
                        - AUTHZ
                        - STATS
                        type: string
                      operation:
                        description: Determines how the patch should be applied.
                        enum:
                        - INVALID
                        - MERGE
                        - ADD
                        - REMOVE
                        - INSERT_BEFORE
                        - INSERT_AFTER
                        - INSERT_FIRST
                        - REPLACE
                        type: string
                      value:
                        description: The JSON config of the object being patched.
                        type: object
                    type: object
                type: object
              type: array
            workloadSelector:
              properties:
                labels:
                  additionalProperties:
                    format: string
                    type: string
                  type: object
              type: object
          type: object
        status:
          type: object
          x-kubernetes-preserve-unknown-fields: true
      type: object
  versions:
  - name: v1alpha3
    served: true
    storage: true
---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  annotations:
    "helm.sh/resource-policy": keep
  labels:
    app: istio-pilot
    chart: istio
    heritage: Tiller
    release: istio
  name: gateways.networking.istio.io
spec:
  group: networking.istio.io
  names:
    categories:
    - istio-io
    - networking-istio-io
    kind: Gateway
    listKind: GatewayList
    plural: gateways
    shortNames:
    - gw
    singular: gateway
  preserveUnknownFields: false
  scope: Namespaced
  subresources:
    status: {}
  validation:
    openAPIV3Schema:
      properties:
        spec:
          description: 'Configuration affecting edge load balancer. See more details
            at: https://istio.io/docs/reference/config/networking/gateway.html'
          properties:
            selector:
              additionalProperties:
                format: string
                type: string
              type: object
            servers:
              description: A list of server specifications.
              items:
                properties:
                  bind:
                    format: string
                    type: string
                  defaultEndpoint:
                    format: string
                    type: string
                  hosts:
                    description: One or more hosts exposed by this gateway.
                    items:
                      format: string
                      type: string
                    type: array
                  name:
                    description: An optional name of the server, when set must be
                      unique across all servers.
                    format: string
                    type: string
                  port:
                    properties:
                      name:
                        description: Label assigned to the port.
                        format: string
                        type: string
                      number:
                        description: A valid non-negative integer port number.
                        type: integer
                      protocol:
                        description: The protocol exposed on the port.
                        format: string
                        type: string
                      targetPort:
                        type: integer
                    type: object
                  tls:
                    description: Set of TLS related options that govern the server's
                      behavior.
                    properties:
                      caCertificates:
                        description: REQUIRED if mode is `MUTUAL`.
                        format: string
                        type: string
                      cipherSuites:
                        description: 'Optional: If specified, only support the specified
                          cipher list.'
                        items:
                          format: string
                          type: string
                        type: array
                      credentialName:
                        format: string
                        type: string
                      httpsRedirect:
                        type: boolean
                      maxProtocolVersion:
                        description: 'Optional: Maximum TLS protocol version.'
                        enum:
                        - TLS_AUTO
                        - TLSV1_0
                        - TLSV1_1
                        - TLSV1_2
                        - TLSV1_3
                        type: string
                      minProtocolVersion:
                        description: 'Optional: Minimum TLS protocol version.'
                        enum:
                        - TLS_AUTO
                        - TLSV1_0
                        - TLSV1_1
                        - TLSV1_2
                        - TLSV1_3
                        type: string
                      mode:
                        enum:
                        - PASSTHROUGH
                        - SIMPLE
                        - MUTUAL
                        - AUTO_PASSTHROUGH
                        - ISTIO_MUTUAL
                        type: string
                      privateKey:
                        description: REQUIRED if mode is `SIMPLE` or `MUTUAL`.
                        format: string
                        type: string
                      serverCertificate:
                        description: REQUIRED if mode is `SIMPLE` or `MUTUAL`.
                        format: string
                        type: string
                      subjectAltNames:
                        items:
                          format: string
                          type: string
                        type: array
                      verifyCertificateHash:
                        items:
                          format: string
                          type: string
                        type: array
                      verifyCertificateSpki:
                        items:
                          format: string
                          type: string
                        type: array
                    type: object
                type: object
              type: array
          type: object
        status:
          type: object
          x-kubernetes-preserve-unknown-fields: true
      type: object
  versions:
  - name: v1alpha3
    served: true
    storage: true
  - name: v1beta1
    served: true
    storage: false
---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  name: istiooperators.install.istio.io
  labels:
    release: istio
spec:
  additionalPrinterColumns:
  - JSONPath: .spec.revision
    description: Istio control plane revision
    name: Revision
    type: string
  - JSONPath: .status.status
    description: IOP current state
    type: string
    name: Status
  - JSONPath: .metadata.creationTimestamp
    description: 'CreationTimestamp is a timestamp representing the server time when
      this object was created. It is not guaranteed to be set in happens-before order
      across separate operations. Clients may not set this value. It is represented
      in RFC3339 form and is in UTC. Populated by the system. Read-only. Null for
      lists. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#metadata'
    name: Age
    type: date
  group: install.istio.io
  names:
    kind: IstioOperator
    plural: istiooperators
    singular: istiooperator
    shortNames:
    - iop
    - io
  scope: Namespaced
  subresources:
    status: {}
  validation:
    openAPIV3Schema:
      properties:
        apiVersion:
          description: 'APIVersion defines the versioned schema of this representation
            of an object. Servers should convert recognized schemas to the latest
            internal value, and may reject unrecognized values.
            More info: https://github.com/kubernetes/community/blob/master/contributors/devel/sig-architecture/api-conventions.md#resources'
          type: string
        kind:
          description: 'Kind is a string value representing the REST resource this
            object represents. Servers may infer this from the endpoint the client
            submits requests to. Cannot be updated. In CamelCase.
            More info: https://github.com/kubernetes/community/blob/master/contributors/devel/sig-architecture/api-conventions.md#types-kinds'
          type: string
        spec:
          description: 'Specification of the desired state of the istio control plane resource.
            More info: https://github.com/kubernetes/community/blob/master/contributors/devel/sig-architecture/api-conventions.md#spec-and-status'
          type: object
        status:
          description: 'Status describes each of istio control plane component status at the current time.
            0 means NONE, 1 means UPDATING, 2 means HEALTHY, 3 means ERROR, 4 means RECONCILING.
            More info: https://github.com/istio/api/blob/master/operator/v1alpha1/istio.operator.v1alpha1.pb.html &
            https://github.com/kubernetes/community/blob/master/contributors/devel/sig-architecture/api-conventions.md#spec-and-status'
          type: object
  versions:
  - name: v1alpha1
    served: true
    storage: true
---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  annotations:
    "helm.sh/resource-policy": keep
  labels:
    app: istio-pilot
    chart: istio
    heritage: Tiller
    istio: security
    release: istio
  name: peerauthentications.security.istio.io
spec:
  additionalPrinterColumns:
  - JSONPath: .spec.mtls.mode
    description: Defines the mTLS mode used for peer authentication.
    name: Mode
    type: string
  - JSONPath: .metadata.creationTimestamp
    description: 'CreationTimestamp is a timestamp representing the server time when
      this object was created. It is not guaranteed to be set in happens-before order
      across separate operations. Clients may not set this value. It is represented
      in RFC3339 form and is in UTC. Populated by the system. Read-only. Null for
      lists. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#metadata'
    name: Age
    type: date
  group: security.istio.io
  names:
    categories:
    - istio-io
    - security-istio-io
    kind: PeerAuthentication
    listKind: PeerAuthenticationList
    plural: peerauthentications
    shortNames:
    - pa
    singular: peerauthentication
  preserveUnknownFields: false
  scope: Namespaced
  subresources:
    status: {}
  validation:
    openAPIV3Schema:
      properties:
        spec:
          description: PeerAuthentication defines how traffic will be tunneled (or
            not) to the sidecar.
          properties:
            mtls:
              description: Mutual TLS settings for workload.
              properties:
                mode:
                  description: Defines the mTLS mode used for peer authentication.
                  enum:
                  - UNSET
                  - DISABLE
                  - PERMISSIVE
                  - STRICT
                  type: string
              type: object
            portLevelMtls:
              additionalProperties:
                properties:
                  mode:
                    description: Defines the mTLS mode used for peer authentication.
                    enum:
                    - UNSET
                    - DISABLE
                    - PERMISSIVE
                    - STRICT
                    type: string
                type: object
              description: Port specific mutual TLS settings.
              type: object
            selector:
              description: The selector determines the workloads to apply the ChannelAuthentication
                on.
              properties:
                matchLabels:
                  additionalProperties:
                    format: string
                    type: string
                  type: object
              type: object
          type: object
        status:
          type: object
          x-kubernetes-preserve-unknown-fields: true
      type: object
  versions:
  - name: v1beta1
    served: true
    storage: true
---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  annotations:
    "helm.sh/resource-policy": keep
  labels:
    app: istio-pilot
    chart: istio
    heritage: Tiller
    istio: security
    release: istio
  name: requestauthentications.security.istio.io
spec:
  group: security.istio.io
  names:
    categories:
    - istio-io
    - security-istio-io
    kind: RequestAuthentication
    listKind: RequestAuthenticationList
    plural: requestauthentications
    shortNames:
    - ra
    singular: requestauthentication
  preserveUnknownFields: false
  scope: Namespaced
  subresources:
    status: {}
  validation:
    openAPIV3Schema:
      properties:
        spec:
          description: RequestAuthentication defines what request authentication methods
            are supported by a workload.
          properties:
            jwtRules:
              description: Define the list of JWTs that can be validated at the selected
                workloads' proxy.
              items:
                properties:
                  audiences:
                    items:
                      format: string
                      type: string
                    type: array
                  forwardOriginalToken:
                    description: If set to true, the orginal token will be kept for
                      the ustream request.
                    type: boolean
                  fromHeaders:
                    description: List of header locations from which JWT is expected.
                    items:
                      properties:
                        name:
                          description: The HTTP header name.
                          format: string
                          type: string
                        prefix:
                          description: The prefix that should be stripped before decoding
                            the token.
                          format: string
                          type: string
                      type: object
                    type: array
                  fromParams:
                    description: List of query parameters from which JWT is expected.
                    items:
                      format: string
                      type: string
                    type: array
                  issuer:
                    description: Identifies the issuer that issued the JWT.
                    format: string
                    type: string
                  jwks:
                    description: JSON Web Key Set of public keys to validate signature
                      of the JWT.
                    format: string
                    type: string
                  jwks_uri:
                    format: string
                    type: string
                  jwksUri:
                    format: string
                    type: string
                  outputPayloadToHeader:
                    format: string
                    type: string
                type: object
              type: array
            selector:
              description: The selector determines the workloads to apply the RequestAuthentication
                on.
              properties:
                matchLabels:
                  additionalProperties:
                    format: string
                    type: string
                  type: object
              type: object
          type: object
        status:
          type: object
          x-kubernetes-preserve-unknown-fields: true
      type: object
  versions:
  - name: v1beta1
    served: true
    storage: true
---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  annotations:
    "helm.sh/resource-policy": keep
  labels:
    app: istio-pilot
    chart: istio
    heritage: Tiller
    release: istio
  name: serviceentries.networking.istio.io
spec:
  additionalPrinterColumns:
  - JSONPath: .spec.hosts
    description: The hosts associated with the ServiceEntry
    name: Hosts
    type: string
  - JSONPath: .spec.location
    description: Whether the service is external to the mesh or part of the mesh (MESH_EXTERNAL
      or MESH_INTERNAL)
    name: Location
    type: string
  - JSONPath: .spec.resolution
    description: Service discovery mode for the hosts (NONE, STATIC, or DNS)
    name: Resolution
    type: string
  - JSONPath: .metadata.creationTimestamp
    description: 'CreationTimestamp is a timestamp representing the server time when
      this object was created. It is not guaranteed to be set in happens-before order
      across separate operations. Clients may not set this value. It is represented
      in RFC3339 form and is in UTC. Populated by the system. Read-only. Null for
      lists. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#metadata'
    name: Age
    type: date
  group: networking.istio.io
  names:
    categories:
    - istio-io
    - networking-istio-io
    kind: ServiceEntry
    listKind: ServiceEntryList
    plural: serviceentries
    shortNames:
    - se
    singular: serviceentry
  preserveUnknownFields: false
  scope: Namespaced
  subresources:
    status: {}
  validation:
    openAPIV3Schema:
      properties:
        spec:
          description: 'Configuration affecting service registry. See more details
            at: https://istio.io/docs/reference/config/networking/service-entry.html'
          properties:
            addresses:
              description: The virtual IP addresses associated with the service.
              items:
                format: string
                type: string
              type: array
            endpoints:
              description: One or more endpoints associated with the service.
              items:
                properties:
                  address:
                    format: string
                    type: string
                  labels:
                    additionalProperties:
                      format: string
                      type: string
                    description: One or more labels associated with the endpoint.
                    type: object
                  locality:
                    description: The locality associated with the endpoint.
                    format: string
                    type: string
                  network:
                    format: string
                    type: string
                  ports:
                    additionalProperties:
                      type: integer
                    description: Set of ports associated with the endpoint.
                    type: object
                  serviceAccount:
                    format: string
                    type: string
                  weight:
                    description: The load balancing weight associated with the endpoint.
                    type: integer
                type: object
              type: array
            exportTo:
              description: A list of namespaces to which this service is exported.
              items:
                format: string
                type: string
              type: array
            hosts:
              description: The hosts associated with the ServiceEntry.
              items:
                format: string
                type: string
              type: array
            location:
              enum:
              - MESH_EXTERNAL
              - MESH_INTERNAL
              type: string
            ports:
              description: The ports associated with the external service.
              items:
                properties:
                  name:
                    description: Label assigned to the port.
                    format: string
                    type: string
                  number:
                    description: A valid non-negative integer port number.
                    type: integer
                  protocol:
                    description: The protocol exposed on the port.
                    format: string
                    type: string
                  targetPort:
                    type: integer
                type: object
              type: array
            resolution:
              description: Service discovery mode for the hosts.
              enum:
              - NONE
              - STATIC
              - DNS
              type: string
            subjectAltNames:
              items:
                format: string
                type: string
              type: array
            workloadSelector:
              description: Applicable only for MESH_INTERNAL services.
              properties:
                labels:
                  additionalProperties:
                    format: string
                    type: string
                  type: object
              type: object
          type: object
        status:
          type: object
          x-kubernetes-preserve-unknown-fields: true
      type: object
  versions:
  - name: v1alpha3
    served: true
    storage: true
  - name: v1beta1
    served: true
    storage: false
---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  annotations:
    "helm.sh/resource-policy": keep
  labels:
    app: istio-pilot
    chart: istio
    heritage: Tiller
    release: istio
  name: sidecars.networking.istio.io
spec:
  group: networking.istio.io
  names:
    categories:
    - istio-io
    - networking-istio-io
    kind: Sidecar
    listKind: SidecarList
    plural: sidecars
    singular: sidecar
  preserveUnknownFields: false
  scope: Namespaced
  subresources:
    status: {}
  validation:
    openAPIV3Schema:
      properties:
        spec:
          description: 'Configuration affecting network reachability of a sidecar.
            See more details at: https://istio.io/docs/reference/config/networking/sidecar.html'
          properties:
            egress:
              items:
                properties:
                  bind:
                    format: string
                    type: string
                  captureMode:
                    enum:
                    - DEFAULT
                    - IPTABLES
                    - NONE
                    type: string
                  hosts:
                    items:
                      format: string
                      type: string
                    type: array
                  port:
                    description: The port associated with the listener.
                    properties:
                      name:
                        description: Label assigned to the port.
                        format: string
                        type: string
                      number:
                        description: A valid non-negative integer port number.
                        type: integer
                      protocol:
                        description: The protocol exposed on the port.
                        format: string
                        type: string
                      targetPort:
                        type: integer
                    type: object
                type: object
              type: array
            ingress:
              items:
                properties:
                  bind:
                    description: The IP to which the listener should be bound.
                    format: string
                    type: string
                  captureMode:
                    enum:
                    - DEFAULT
                    - IPTABLES
                    - NONE
                    type: string
                  defaultEndpoint:
                    format: string
                    type: string
                  port:
                    description: The port associated with the listener.
                    properties:
                      name:
                        description: Label assigned to the port.
                        format: string
                        type: string
                      number:
                        description: A valid non-negative integer port number.
                        type: integer
                      protocol:
                        description: The protocol exposed on the port.
                        format: string
                        type: string
                      targetPort:
                        type: integer
                    type: object
                type: object
              type: array
            outboundTrafficPolicy:
              description: Configuration for the outbound traffic policy.
              properties:
                egressProxy:
                  properties:
                    host:
                      description: The name of a service from the service registry.
                      format: string
                      type: string
                    port:
                      description: Specifies the port on the host that is being addressed.
                      properties:
                        number:
                          type: integer
                      type: object
                    subset:
                      description: The name of a subset within the service.
                      format: string
                      type: string
                  type: object
                mode:
                  enum:
                  - REGISTRY_ONLY
                  - ALLOW_ANY
                  type: string
              type: object
            workloadSelector:
              properties:
                labels:
                  additionalProperties:
                    format: string
                    type: string
                  type: object
              type: object
          type: object
        status:
          type: object
          x-kubernetes-preserve-unknown-fields: true
      type: object
  versions:
  - name: v1alpha3
    served: true
    storage: true
  - name: v1beta1
    served: true
    storage: false
---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  annotations:
    "helm.sh/resource-policy": keep
  labels:
    app: istio-pilot
    chart: istio
    heritage: Tiller
    release: istio
  name: virtualservices.networking.istio.io
spec:
  additionalPrinterColumns:
  - JSONPath: .spec.gateways
    description: The names of gateways and sidecars that should apply these routes
    name: Gateways
    type: string
  - JSONPath: .spec.hosts
    description: The destination hosts to which traffic is being sent
    name: Hosts
    type: string
  - JSONPath: .metadata.creationTimestamp
    description: 'CreationTimestamp is a timestamp representing the server time when
      this object was created. It is not guaranteed to be set in happens-before order
      across separate operations. Clients may not set this value. It is represented
      in RFC3339 form and is in UTC. Populated by the system. Read-only. Null for
      lists. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#metadata'
    name: Age
    type: date
  group: networking.istio.io
  names:
    categories:
    - istio-io
    - networking-istio-io
    kind: VirtualService
    listKind: VirtualServiceList
    plural: virtualservices
    shortNames:
    - vs
    singular: virtualservice
  preserveUnknownFields: false
  scope: Namespaced
  subresources:
    status: {}
  validation:
    openAPIV3Schema:
      properties:
        spec:
          description: 'Configuration affecting label/content routing, sni routing,
            etc. See more details at: https://istio.io/docs/reference/config/networking/virtual-service.html'
          properties:
            exportTo:
              description: A list of namespaces to which this virtual service is exported.
              items:
                format: string
                type: string
              type: array
            gateways:
              description: The names of gateways and sidecars that should apply these
                routes.
              items:
                format: string
                type: string
              type: array
            hosts:
              description: The destination hosts to which traffic is being sent.
              items:
                format: string
                type: string
              type: array
            http:
              description: An ordered list of route rules for HTTP traffic.
              items:
                properties:
                  corsPolicy:
                    description: Cross-Origin Resource Sharing policy (CORS).
                    properties:
                      allowCredentials:
                        nullable: true
                        type: boolean
                      allowHeaders:
                        items:
                          format: string
                          type: string
                        type: array
                      allowMethods:
                        description: List of HTTP methods allowed to access the resource.
                        items:
                          format: string
                          type: string
                        type: array
                      allowOrigin:
                        description: The list of origins that are allowed to perform
                          CORS requests.
                        items:
                          format: string
                          type: string
                        type: array
                      allowOrigins:
                        description: String patterns that match allowed origins.
                        items:
                          oneOf:
                          - not:
                              anyOf:
                              - required:
                                - exact
                              - required:
                                - prefix
                              - required:
                                - regex
                          - required:
                            - exact
                          - required:
                            - prefix
                          - required:
                            - regex
                          properties:
                            exact:
                              format: string
                              type: string
                            prefix:
                              format: string
                              type: string
                            regex:
                              description: RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
                              format: string
                              type: string
                          type: object
                        type: array
                      exposeHeaders:
                        items:
                          format: string
                          type: string
                        type: array
                      maxAge:
                        type: string
                    type: object
                  delegate:
                    properties:
                      name:
                        description: Name specifies the name of the delegate VirtualService.
                        format: string
                        type: string
                      namespace:
                        description: Namespace specifies the namespace where the delegate
                          VirtualService resides.
                        format: string
                        type: string
                    type: object
                  fault:
                    description: Fault injection policy to apply on HTTP traffic at
                      the client side.
                    properties:
                      abort:
                        oneOf:
                        - not:
                            anyOf:
                            - required:
                              - httpStatus
                            - required:
                              - grpcStatus
                            - required:
                              - http2Error
                        - required:
                          - httpStatus
                        - required:
                          - grpcStatus
                        - required:
                          - http2Error
                        properties:
                          grpcStatus:
                            format: string
                            type: string
                          http2Error:
                            format: string
                            type: string
                          httpStatus:
                            description: HTTP status code to use to abort the Http
                              request.
                            format: int32
                            type: integer
                          percentage:
                            description: Percentage of requests to be aborted with
                              the error code provided.
                            properties:
                              value:
                                format: double
                                type: number
                            type: object
                        type: object
                      delay:
                        oneOf:
                        - not:
                            anyOf:
                            - required:
                              - fixedDelay
                            - required:
                              - exponentialDelay
                        - required:
                          - fixedDelay
                        - required:
                          - exponentialDelay
                        properties:
                          exponentialDelay:
                            type: string
                          fixedDelay:
                            description: Add a fixed delay before forwarding the request.
                            type: string
                          percent:
                            description: Percentage of requests on which the delay
                              will be injected (0-100).
                            format: int32
                            type: integer
                          percentage:
                            description: Percentage of requests on which the delay
                              will be injected.
                            properties:
                              value:
                                format: double
                                type: number
                            type: object
                        type: object
                    type: object
                  headers:
                    properties:
                      request:
                        properties:
                          add:
                            additionalProperties:
                              format: string
                              type: string
                            type: object
                          remove:
                            items:
                              format: string
                              type: string
                            type: array
                          set:
                            additionalProperties:
                              format: string
                              type: string
                            type: object
                        type: object
                      response:
                        properties:
                          add:
                            additionalProperties:
                              format: string
                              type: string
                            type: object
                          remove:
                            items:
                              format: string
                              type: string
                            type: array
                          set:
                            additionalProperties:
                              format: string
                              type: string
                            type: object
                        type: object
                    type: object
                  match:
                    items:
                      properties:
                        authority:
                          oneOf:
                          - not:
                              anyOf:
                              - required:
                                - exact
                              - required:
                                - prefix
                              - required:
                                - regex
                          - required:
                            - exact
                          - required:
                            - prefix
                          - required:
                            - regex
                          properties:
                            exact:
                              format: string
                              type: string
                            prefix:
                              format: string
                              type: string
                            regex:
                              description: RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
                              format: string
                              type: string
                          type: object
                        gateways:
                          description: Names of gateways where the rule should be
                            applied.
                          items:
                            format: string
                            type: string
                          type: array
                        headers:
                          additionalProperties:
                            oneOf:
                            - not:
                                anyOf:
                                - required:
                                  - exact
                                - required:
                                  - prefix
                                - required:
                                  - regex
                            - required:
                              - exact
                            - required:
                              - prefix
                            - required:
                              - regex
                            properties:
                              exact:
                                format: string
                                type: string
                              prefix:
                                format: string
                                type: string
                              regex:
                                description: RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
                                format: string
                                type: string
                            type: object
                          type: object
                        ignoreUriCase:
                          description: Flag to specify whether the URI matching should
                            be case-insensitive.
                          type: boolean
                        method:
                          oneOf:
                          - not:
                              anyOf:
                              - required:
                                - exact
                              - required:
                                - prefix
                              - required:
                                - regex
                          - required:
                            - exact
                          - required:
                            - prefix
                          - required:
                            - regex
                          properties:
                            exact:
                              format: string
                              type: string
                            prefix:
                              format: string
                              type: string
                            regex:
                              description: RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
                              format: string
                              type: string
                          type: object
                        name:
                          description: The name assigned to a match.
                          format: string
                          type: string
                        port:
                          description: Specifies the ports on the host that is being
                            addressed.
                          type: integer
                        queryParams:
                          additionalProperties:
                            oneOf:
                            - not:
                                anyOf:
                                - required:
                                  - exact
                                - required:
                                  - prefix
                                - required:
                                  - regex
                            - required:
                              - exact
                            - required:
                              - prefix
                            - required:
                              - regex
                            properties:
                              exact:
                                format: string
                                type: string
                              prefix:
                                format: string
                                type: string
                              regex:
                                description: RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
                                format: string
                                type: string
                            type: object
                          description: Query parameters for matching.
                          type: object
                        scheme:
                          oneOf:
                          - not:
                              anyOf:
                              - required:
                                - exact
                              - required:
                                - prefix
                              - required:
                                - regex
                          - required:
                            - exact
                          - required:
                            - prefix
                          - required:
                            - regex
                          properties:
                            exact:
                              format: string
                              type: string
                            prefix:
                              format: string
                              type: string
                            regex:
                              description: RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
                              format: string
                              type: string
                          type: object
                        sourceLabels:
                          additionalProperties:
                            format: string
                            type: string
                          type: object
                        sourceNamespace:
                          description: Source namespace constraining the applicability
                            of a rule to workloads in that namespace.
                          format: string
                          type: string
                        uri:
                          oneOf:
                          - not:
                              anyOf:
                              - required:
                                - exact
                              - required:
                                - prefix
                              - required:
                                - regex
                          - required:
                            - exact
                          - required:
                            - prefix
                          - required:
                            - regex
                          properties:
                            exact:
                              format: string
                              type: string
                            prefix:
                              format: string
                              type: string
                            regex:
                              description: RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
                              format: string
                              type: string
                          type: object
                        withoutHeaders:
                          additionalProperties:
                            oneOf:
                            - not:
                                anyOf:
                                - required:
                                  - exact
                                - required:
                                  - prefix
                                - required:
                                  - regex
                            - required:
                              - exact
                            - required:
                              - prefix
                            - required:
                              - regex
                            properties:
                              exact:
                                format: string
                                type: string
                              prefix:
                                format: string
                                type: string
                              regex:
                                description: RE2 style regex-based match (https://github.com/google/re2/wiki/Syntax).
                                format: string
                                type: string
                            type: object
                          description: withoutHeader has the same syntax with the
                            header, but has opposite meaning.
                          type: object
                      type: object
                    type: array
                  mirror:
                    properties:
                      host:
                        description: The name of a service from the service registry.
                        format: string
                        type: string
                      port:
                        description: Specifies the port on the host that is being
                          addressed.
                        properties:
                          number:
                            type: integer
                        type: object
                      subset:
                        description: The name of a subset within the service.
                        format: string
                        type: string
                    type: object
                  mirror_percent:
                    description: Percentage of the traffic to be mirrored by the `mirror`
                      field.
                    nullable: true
                    type: integer
                  mirrorPercent:
                    description: Percentage of the traffic to be mirrored by the `mirror`
                      field.
                    nullable: true
                    type: integer
                  mirrorPercentage:
                    description: Percentage of the traffic to be mirrored by the `mirror`
                      field.
                    properties:
                      value:
                        format: double
                        type: number
                    type: object
                  name:
                    description: The name assigned to the route for debugging purposes.
                    format: string
                    type: string
                  redirect:
                    description: A HTTP rule can either redirect or forward (default)
                      traffic.
                    properties:
                      authority:
                        format: string
                        type: string
                      redirectCode:
                        type: integer
                      uri:
                        format: string
                        type: string
                    type: object
                  retries:
                    description: Retry policy for HTTP requests.
                    properties:
                      attempts:
                        description: Number of retries to be allowed for a given request.
                        format: int32
                        type: integer
                      perTryTimeout:
                        description: Timeout per attempt for a given request, including
                          the initial call and any retries.
                        type: string
                      retryOn:
                        description: Specifies the conditions under which retry takes
                          place.
                        format: string
                        type: string
                      retryRemoteLocalities:
                        description: Flag to specify whether the retries should retry
                          to other localities.
                        nullable: true
                        type: boolean
                    type: object
                  rewrite:
                    description: Rewrite HTTP URIs and Authority headers.
                    properties:
                      authority:
                        description: rewrite the Authority/Host header with this value.
                        format: string
                        type: string
                      uri:
                        format: string
                        type: string
                    type: object
                  route:
                    description: A HTTP rule can either redirect or forward (default)
                      traffic.
                    items:
                      properties:
                        destination:
                          properties:
                            host:
                              description: The name of a service from the service
                                registry.
                              format: string
                              type: string
                            port:
                              description: Specifies the port on the host that is
                                being addressed.
                              properties:
                                number:
                                  type: integer
                              type: object
                            subset:
                              description: The name of a subset within the service.
                              format: string
                              type: string
                          type: object
                        headers:
                          properties:
                            request:
                              properties:
                                add:
                                  additionalProperties:
                                    format: string
                                    type: string
                                  type: object
                                remove:
                                  items:
                                    format: string
                                    type: string
                                  type: array
                                set:
                                  additionalProperties:
                                    format: string
                                    type: string
                                  type: object
                              type: object
                            response:
                              properties:
                                add:
                                  additionalProperties:
                                    format: string
                                    type: string
                                  type: object
                                remove:
                                  items:
                                    format: string
                                    type: string
                                  type: array
                                set:
                                  additionalProperties:
                                    format: string
                                    type: string
                                  type: object
                              type: object
                          type: object
                        weight:
                          format: int32
                          type: integer
                      type: object
                    type: array
                  timeout:
                    description: Timeout for HTTP requests, default is disabled.
                    type: string
                type: object
              type: array
            tcp:
              description: An ordered list of route rules for opaque TCP traffic.
              items:
                properties:
                  match:
                    items:
                      properties:
                        destinationSubnets:
                          description: IPv4 or IPv6 ip addresses of destination with
                            optional subnet.
                          items:
                            format: string
                            type: string
                          type: array
                        gateways:
                          description: Names of gateways where the rule should be
                            applied.
                          items:
                            format: string
                            type: string
                          type: array
                        port:
                          description: Specifies the port on the host that is being
                            addressed.
                          type: integer
                        sourceLabels:
                          additionalProperties:
                            format: string
                            type: string
                          type: object
                        sourceNamespace:
                          description: Source namespace constraining the applicability
                            of a rule to workloads in that namespace.
                          format: string
                          type: string
                        sourceSubnet:
                          description: IPv4 or IPv6 ip address of source with optional
                            subnet.
                          format: string
                          type: string
                      type: object
                    type: array
                  route:
                    description: The destination to which the connection should be
                      forwarded to.
                    items:
                      properties:
                        destination:
                          properties:
                            host:
                              description: The name of a service from the service
                                registry.
                              format: string
                              type: string
                            port:
                              description: Specifies the port on the host that is
                                being addressed.
                              properties:
                                number:
                                  type: integer
                              type: object
                            subset:
                              description: The name of a subset within the service.
                              format: string
                              type: string
                          type: object
                        weight:
                          format: int32
                          type: integer
                      type: object
                    type: array
                type: object
              type: array
            tls:
              items:
                properties:
                  match:
                    items:
                      properties:
                        destinationSubnets:
                          description: IPv4 or IPv6 ip addresses of destination with
                            optional subnet.
                          items:
                            format: string
                            type: string
                          type: array
                        gateways:
                          description: Names of gateways where the rule should be
                            applied.
                          items:
                            format: string
                            type: string
                          type: array
                        port:
                          description: Specifies the port on the host that is being
                            addressed.
                          type: integer
                        sniHosts:
                          description: SNI (server name indicator) to match on.
                          items:
                            format: string
                            type: string
                          type: array
                        sourceLabels:
                          additionalProperties:
                            format: string
                            type: string
                          type: object
                        sourceNamespace:
                          description: Source namespace constraining the applicability
                            of a rule to workloads in that namespace.
                          format: string
                          type: string
                      type: object
                    type: array
                  route:
                    description: The destination to which the connection should be
                      forwarded to.
                    items:
                      properties:
                        destination:
                          properties:
                            host:
                              description: The name of a service from the service
                                registry.
                              format: string
                              type: string
                            port:
                              description: Specifies the port on the host that is
                                being addressed.
                              properties:
                                number:
                                  type: integer
                              type: object
                            subset:
                              description: The name of a subset within the service.
                              format: string
                              type: string
                          type: object
                        weight:
                          format: int32
                          type: integer
                      type: object
                    type: array
                type: object
              type: array
          type: object
        status:
          type: object
          x-kubernetes-preserve-unknown-fields: true
      type: object
  versions:
  - name: v1alpha3
    served: true
    storage: true
  - name: v1beta1
    served: true
    storage: false
---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  annotations:
    "helm.sh/resource-policy": keep
  labels:
    app: istio-pilot
    chart: istio
    heritage: Tiller
    release: istio
  name: workloadentries.networking.istio.io
spec:
  additionalPrinterColumns:
  - JSONPath: .metadata.creationTimestamp
    description: 'CreationTimestamp is a timestamp representing the server time when
      this object was created. It is not guaranteed to be set in happens-before order
      across separate operations. Clients may not set this value. It is represented
      in RFC3339 form and is in UTC. Populated by the system. Read-only. Null for
      lists. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#metadata'
    name: Age
    type: date
  - JSONPath: .spec.address
    description: Address associated with the network endpoint.
    name: Address
    type: string
  group: networking.istio.io
  names:
    categories:
    - istio-io
    - networking-istio-io
    kind: WorkloadEntry
    listKind: WorkloadEntryList
    plural: workloadentries
    shortNames:
    - we
    singular: workloadentry
  preserveUnknownFields: false
  scope: Namespaced
  subresources:
    status: {}
  validation:
    openAPIV3Schema:
      properties:
        spec:
          description: 'Configuration affecting VMs onboarded into the mesh. See more
            details at: https://istio.io/docs/reference/config/networking/workload-entry.html'
          properties:
            address:
              format: string
              type: string
            labels:
              additionalProperties:
                format: string
                type: string
              description: One or more labels associated with the endpoint.
              type: object
            locality:
              description: The locality associated with the endpoint.
              format: string
              type: string
            network:
              format: string
              type: string
            ports:
              additionalProperties:
                type: integer
              description: Set of ports associated with the endpoint.
              type: object
            serviceAccount:
              format: string
              type: string
            weight:
              description: The load balancing weight associated with the endpoint.
              type: integer
          type: object
        status:
          type: object
          x-kubernetes-preserve-unknown-fields: true
      type: object
  versions:
  - name: v1alpha3
    served: true
    storage: true
  - name: v1beta1
    served: true
    storage: false
---
apiVersion: apiextensions.k8s.io/v1beta1
kind: CustomResourceDefinition
metadata:
  labels:
    app: istio-pilot
    chart: istio
    heritage: Tiller
    release: istio
  name: workloadgroups.networking.istio.io
spec:
  additionalPrinterColumns:
  - JSONPath: .metadata.creationTimestamp
    description: 'CreationTimestamp is a timestamp representing the server time when
      this object was created. It is not guaranteed to be set in happens-before order
      across separate operations. Clients may not set this value. It is represented
      in RFC3339 form and is in UTC. Populated by the system. Read-only. Null for
      lists. More info: https://git.k8s.io/community/contributors/devel/api-conventions.md#metadata'
    name: Age
    type: date
  group: networking.istio.io
  names:
    categories:
    - istio-io
    - networking-istio-io
    kind: WorkloadGroup
    listKind: WorkloadGroupList
    plural: workloadgroups
    shortNames:
    - wg
    singular: workloadgroup
  preserveUnknownFields: false
  scope: Namespaced
  subresources:
    status: {}
  validation:
    openAPIV3Schema:
      properties:
        spec:
          description: 'Describes a collection of workload instances. See more details
            at: https://istio.io/docs/reference/config/networking/workload-group.html'
          properties:
            metadata:
              description: Metadata that will be used for all corresponding `WorkloadEntries`.
              properties:
                annotations:
                  additionalProperties:
                    format: string
                    type: string
                  type: object
                labels:
                  additionalProperties:
                    format: string
                    type: string
                  type: object
              type: object
            probe:
              description: '`ReadinessProbe` describes the configuration the user
                must provide for healthchecking on their workload.'
              oneOf:
              - not:
                  anyOf:
                  - required:
                    - httpGet
                  - required:
                    - tcpSocket
                  - required:
                    - exec
              - required:
                - httpGet
              - required:
                - tcpSocket
              - required:
                - exec
              properties:
                exec:
                  description: Health is determined by how the command that is executed
                    exited.
                  properties:
                    command:
                      description: Command to run.
                      items:
                        format: string
                        type: string
                      type: array
                  type: object
                failureThreshold:
                  description: Minimum consecutive failures for the probe to be considered
                    failed after having succeeded.
                  format: int32
                  type: integer
                httpGet:
                  properties:
                    host:
                      description: Host name to connect to, defaults to the pod IP.
                      format: string
                      type: string
                    httpHeaders:
                      description: Headers the proxy will pass on to make the request.
                      items:
                        properties:
                          name:
                            format: string
                            type: string
                          value:
                            format: string
                            type: string
                        type: object
                      type: array
                    path:
                      description: Path to access on the HTTP server.
                      format: string
                      type: string
                    port:
                      description: Port on which the endpoint lives.
                      type: integer
                    scheme:
                      format: string
                      type: string
                  type: object
                initialDelaySeconds:
                  description: Number of seconds after the container has started before
                    readiness probes are initiated.
                  format: int32
                  type: integer
                periodSeconds:
                  description: How often (in seconds) to perform the probe.
                  format: int32
                  type: integer
                successThreshold:
                  description: Minimum consecutive successes for the probe to be considered
                    successful after having failed.
                  format: int32
                  type: integer
                tcpSocket:
                  description: Health is determined by if the proxy is able to connect.
                  properties:
                    host:
                      format: string
                      type: string
                    port:
                      type: integer
                  type: object
                timeoutSeconds:
                  description: Number of seconds after which the probe times out.
                  format: int32
                  type: integer
              type: object
            template:
              description: Template to be used for the generation of `WorkloadEntry`
                resources that belong to this `WorkloadGroup`.
              properties:
                address:
                  format: string
                  type: string
                labels:
                  additionalProperties:
                    format: string
                    type: string
                  description: One or more labels associated with the endpoint.
                  type: object
                locality:
                  description: The locality associated with the endpoint.
                  format: string
                  type: string
                network:
                  format: string
                  type: string
                ports:
                  additionalProperties:
                    type: integer
                  description: Set of ports associated with the endpoint.
                  type: object
                serviceAccount:
                  format: string
                  type: string
                weight:
                  description: The load balancing weight associated with the endpoint.
                  type: integer
              type: object
          type: object
        status:
          type: object
          x-kubernetes-preserve-unknown-fields: true
      type: object
  versions:
  - name: v1alpha3
    served: true
    storage: true
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: istio-ingressgateway-internal-service-account
  namespace: istio-system
  labels:
    app: istio-ingressgateway
    istio: istio-ingressgateway-internal
    release: istio
    istio.io/rev: default
    install.operator.istio.io/owning-resource: unknown
    operator.istio.io/component: "IngressGateways"
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: istio-ingressgateway-service-account
  namespace: istio-system
  labels:
    app: istio-ingressgateway
    istio: ingressgateway
    release: istio
    istio.io/rev: default
    install.operator.istio.io/owning-resource: unknown
    operator.istio.io/component: "IngressGateways"
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: istio-reader-service-account
  namespace: istio-system
  labels:
    app: istio-reader
    release: istio
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: istiod-service-account
  namespace: istio-system
  labels:
    app: istiod
    release: istio
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: istio-reader-istio-system
  labels:
    app: istio-reader
    release: istio
rules:
  - apiGroups:
      - "config.istio.io"
      - "security.istio.io"
      - "networking.istio.io"
      - "authentication.istio.io"
      - "rbac.istio.io"
    resources: ["*"]
    verbs: ["get", "list", "watch"]
  - apiGroups: [""]
    resources: ["endpoints", "pods", "services", "nodes", "replicationcontrollers", "namespaces", "secrets"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["networking.istio.io"]
    verbs: [ "get", "watch", "list" ]
    resources: [ "workloadentries" ]
  - apiGroups: ["apiextensions.k8s.io"]
    resources: ["customresourcedefinitions"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["discovery.k8s.io"]
    resources: ["endpointslices"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["apps"]
    resources: ["replicasets"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["authentication.k8s.io"]
    resources: ["tokenreviews"]
    verbs: ["create"]
  - apiGroups: ["authorization.k8s.io"]
    resources: ["subjectaccessreviews"]
    verbs: ["create"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: istiod-istio-system
  labels:
    app: istiod
    release: istio
rules:
  # sidecar injection controller
  - apiGroups: ["admissionregistration.k8s.io"]
    resources: ["mutatingwebhookconfigurations"]
    verbs: ["get", "list", "watch", "update", "patch"]

  # configuration validation webhook controller
  - apiGroups: ["admissionregistration.k8s.io"]
    resources: ["validatingwebhookconfigurations"]
    verbs: ["get", "list", "watch", "update"]

  # istio configuration
  # removing CRD permissions can break older versions of Istio running alongside this control plane (https://github.com/istio/istio/issues/29382)
  # please proceed with caution
  - apiGroups: ["config.istio.io", "security.istio.io", "networking.istio.io", "authentication.istio.io", "rbac.istio.io"]
    verbs: ["get", "watch", "list"]
    resources: ["*"]
  - apiGroups: ["networking.istio.io"]
    verbs: [ "get", "watch", "list", "update", "patch", "create", "delete" ]
    resources: [ "workloadentries" ]
  - apiGroups: ["networking.istio.io"]
    verbs: [ "get", "watch", "list", "update", "patch", "create", "delete" ]
    resources: [ "workloadentries/status" ]

  # auto-detect installed CRD definitions
  - apiGroups: ["apiextensions.k8s.io"]
    resources: ["customresourcedefinitions"]
    verbs: ["get", "list", "watch"]

  # discovery and routing
  - apiGroups: [""]
    resources: ["pods", "nodes", "services", "namespaces", "endpoints"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["discovery.k8s.io"]
    resources: ["endpointslices"]
    verbs: ["get", "list", "watch"]

  # ingress controller
  - apiGroups: ["networking.k8s.io"]
    resources: ["ingresses", "ingressclasses"]
    verbs: ["get", "list", "watch"]
  - apiGroups: ["networking.k8s.io"]
    resources: ["ingresses/status"]
    verbs: ["*"]

  # required for CA's namespace controller
  - apiGroups: [""]
    resources: ["configmaps"]
    verbs: ["create", "get", "list", "watch", "update"]

  # Istiod and bootstrap.
  - apiGroups: ["certificates.k8s.io"]
    resources:
      - "certificatesigningrequests"
      - "certificatesigningrequests/approval"
      - "certificatesigningrequests/status"
    verbs: ["update", "create", "get", "delete", "watch"]
  - apiGroups: ["certificates.k8s.io"]
    resources:
      - "signers"
    resourceNames:
    - "kubernetes.io/legacy-unknown"
    verbs: ["approve"]

  # Used by Istiod to verify the JWT tokens
  - apiGroups: ["authentication.k8s.io"]
    resources: ["tokenreviews"]
    verbs: ["create"]

  # Used by Istiod to verify gateway SDS
  - apiGroups: ["authorization.k8s.io"]
    resources: ["subjectaccessreviews"]
    verbs: ["create"]

  # Use for Kubernetes Service APIs
  - apiGroups: ["networking.x-k8s.io"]
    resources: ["*"]
    verbs: ["get", "watch", "list"]

  # Needed for multicluster secret reading, possibly ingress certs in the future
  - apiGroups: [""]
    resources: ["secrets"]
    verbs: ["get", "watch", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: istio-reader-istio-system
  labels:
    app: istio-reader
    release: istio
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: istio-reader-istio-system
subjects:
  - kind: ServiceAccount
    name: istio-reader-service-account
    namespace: istio-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: istiod-istio-system
  labels:
    app: istiod
    release: istio
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: istiod-istio-system
subjects:
  - kind: ServiceAccount
    name: istiod-service-account
    namespace: istio-system
---
apiVersion: admissionregistration.k8s.io/v1beta1
kind: ValidatingWebhookConfiguration
metadata:
  name: istiod-istio-system
  labels:
    app: istiod
    release: istio
    istio: istiod
webhooks:
  - name: validation.istio.io
    clientConfig:
      service:
        name: istiod
        namespace: istio-system
        path: "/validate"
      caBundle: "" # patched at runtime when the webhook is ready.
    rules:
      - operations:
        - CREATE
        - UPDATE
        apiGroups:
        - security.istio.io
        - networking.istio.io
        apiVersions:
        - "*"
        resources:
        - "*"
    # Fail open until the validation webhook is ready. The webhook controller
    # will update this to `Fail` and patch in the `caBundle` when the webhook
    # endpoint is ready.
    failurePolicy: Ignore
    sideEffects: None
    admissionReviewVersions: ["v1beta1", "v1"]
---
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: metadata-exchange-1.8
  namespace: istio-system
  labels:
    istio.io/rev: default
    install.operator.istio.io/owning-resource: unknown
    operator.istio.io/component: "Pilot"
spec:
  configPatches:
    - applyTo: HTTP_FILTER
      match:
        context: SIDECAR_INBOUND
        proxy:
          proxyVersion: '^1\.8.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.metadata_exchange
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {}
                vm_config:
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: envoy.wasm.metadata_exchange
    - applyTo: HTTP_FILTER
      match:
        context: SIDECAR_OUTBOUND
        proxy:
          proxyVersion: '^1\.8.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.metadata_exchange
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {}
                vm_config:
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: envoy.wasm.metadata_exchange
    - applyTo: HTTP_FILTER
      match:
        context: GATEWAY
        proxy:
          proxyVersion: '^1\.8.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.metadata_exchange
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {}
                vm_config:
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: envoy.wasm.metadata_exchange
---
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: metadata-exchange-1.9
  namespace: istio-system
  labels:
    istio.io/rev: default
    install.operator.istio.io/owning-resource: unknown
    operator.istio.io/component: "Pilot"
spec:
  configPatches:
    - applyTo: HTTP_FILTER
      match:
        context: SIDECAR_INBOUND
        proxy:
          proxyVersion: '^1\.9.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.metadata_exchange
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {}
                vm_config:
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: envoy.wasm.metadata_exchange
    - applyTo: HTTP_FILTER
      match:
        context: SIDECAR_OUTBOUND
        proxy:
          proxyVersion: '^1\.9.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.metadata_exchange
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {}
                vm_config:
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: envoy.wasm.metadata_exchange
    - applyTo: HTTP_FILTER
      match:
        context: GATEWAY
        proxy:
          proxyVersion: '^1\.9.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.metadata_exchange
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {}
                vm_config:
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: envoy.wasm.metadata_exchange
---
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: stats-filter-1.8
  namespace: istio-system
  labels:
    istio.io/rev: default
spec:
  configPatches:
    - applyTo: HTTP_FILTER
      match:
        context: SIDECAR_OUTBOUND
        proxy:
          proxyVersion: '^1\.8.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "envoy.filters.http.router"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                root_id: stats_outbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                    }
                vm_config:
                  vm_id: stats_outbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: envoy.wasm.stats
    - applyTo: HTTP_FILTER
      match:
        context: SIDECAR_INBOUND
        proxy:
          proxyVersion: '^1\.8.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "envoy.filters.http.router"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                root_id: stats_inbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                    }
                vm_config:
                  vm_id: stats_inbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: envoy.wasm.stats
    - applyTo: HTTP_FILTER
      match:
        context: GATEWAY
        proxy:
          proxyVersion: '^1\.8.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "envoy.filters.http.router"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                root_id: stats_outbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "disable_host_header_fallback": true
                    }
                vm_config:
                  vm_id: stats_outbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: envoy.wasm.stats
---
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: stats-filter-1.9
  namespace: istio-system
  labels:
    istio.io/rev: default
spec:
  configPatches:
    - applyTo: HTTP_FILTER
      match:
        context: SIDECAR_OUTBOUND
        proxy:
          proxyVersion: '^1\.9.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "envoy.filters.http.router"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                root_id: stats_outbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio",
                      "metrics": [
                        {
                          "dimensions": {
                            "source_cluster": "node.metadata['CLUSTER_ID']",
                            "destination_cluster": "upstream_peer.cluster_id"
                          }
                        }
                      ]
                    }
                vm_config:
                  vm_id: stats_outbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: envoy.wasm.stats
    - applyTo: HTTP_FILTER
      match:
        context: SIDECAR_INBOUND
        proxy:
          proxyVersion: '^1\.9.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "envoy.filters.http.router"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                root_id: stats_inbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio",
                      "metrics": [
                        {
                          "dimensions": {
                            "destination_cluster": "node.metadata['CLUSTER_ID']",
                            "source_cluster": "downstream_peer.cluster_id"
                          }
                        }
                      ]
                    }
                vm_config:
                  vm_id: stats_inbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: envoy.wasm.stats
    - applyTo: HTTP_FILTER
      match:
        context: GATEWAY
        proxy:
          proxyVersion: '^1\.9.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "envoy.filters.http.router"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.http.wasm.v3.Wasm
            value:
              config:
                root_id: stats_outbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio",
                      "disable_host_header_fallback": true,
                      "metrics": [
                        {
                          "dimensions": {
                            "source_cluster": "node.metadata['CLUSTER_ID']",
                            "destination_cluster": "upstream_peer.cluster_id"
                          }
                        }
                      ]
                    }
                vm_config:
                  vm_id: stats_outbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: envoy.wasm.stats
---
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: tcp-metadata-exchange-1.8
  namespace: istio-system
  labels:
    istio.io/rev: default
spec:
  configPatches:
    - applyTo: NETWORK_FILTER
      match:
        context: SIDECAR_INBOUND
        proxy:
          proxyVersion: '^1\.8.*'
        listener: {}
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.metadata_exchange
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.tcp.metadataexchange.config.MetadataExchange
            value:
              protocol: istio-peer-exchange
    - applyTo: CLUSTER
      match:
        context: SIDECAR_OUTBOUND
        proxy:
          proxyVersion: '^1\.8.*'
        cluster: {}
      patch:
        operation: MERGE
        value:
          filters:
          - name: istio.metadata_exchange
            typed_config:
              "@type": type.googleapis.com/udpa.type.v1.TypedStruct
              type_url: type.googleapis.com/envoy.tcp.metadataexchange.config.MetadataExchange
              value:
                protocol: istio-peer-exchange
    - applyTo: CLUSTER
      match:
        context: GATEWAY
        proxy:
          proxyVersion: '^1\.8.*'
        cluster: {}
      patch:
        operation: MERGE
        value:
          filters:
          - name: istio.metadata_exchange
            typed_config:
              "@type": type.googleapis.com/udpa.type.v1.TypedStruct
              type_url: type.googleapis.com/envoy.tcp.metadataexchange.config.MetadataExchange
              value:
                protocol: istio-peer-exchange
---
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: tcp-metadata-exchange-1.9
  namespace: istio-system
  labels:
    istio.io/rev: default
spec:
  configPatches:
    - applyTo: NETWORK_FILTER
      match:
        context: SIDECAR_INBOUND
        proxy:
          proxyVersion: '^1\.9.*'
        listener: {}
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.metadata_exchange
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.tcp.metadataexchange.config.MetadataExchange
            value:
              protocol: istio-peer-exchange
    - applyTo: CLUSTER
      match:
        context: SIDECAR_OUTBOUND
        proxy:
          proxyVersion: '^1\.9.*'
        cluster: {}
      patch:
        operation: MERGE
        value:
          filters:
          - name: istio.metadata_exchange
            typed_config:
              "@type": type.googleapis.com/udpa.type.v1.TypedStruct
              type_url: type.googleapis.com/envoy.tcp.metadataexchange.config.MetadataExchange
              value:
                protocol: istio-peer-exchange
    - applyTo: CLUSTER
      match:
        context: GATEWAY
        proxy:
          proxyVersion: '^1\.9.*'
        cluster: {}
      patch:
        operation: MERGE
        value:
          filters:
          - name: istio.metadata_exchange
            typed_config:
              "@type": type.googleapis.com/udpa.type.v1.TypedStruct
              type_url: type.googleapis.com/envoy.tcp.metadataexchange.config.MetadataExchange
              value:
                protocol: istio-peer-exchange
---
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: tcp-stats-filter-1.8
  namespace: istio-system
  labels:
    istio.io/rev: default
spec:
  configPatches:
    - applyTo: NETWORK_FILTER
      match:
        context: SIDECAR_INBOUND
        proxy:
          proxyVersion: '^1\.8.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.tcp_proxy"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.network.wasm.v3.Wasm
            value:
              config:
                root_id: stats_inbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                    }
                vm_config:
                  vm_id: tcp_stats_inbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: "envoy.wasm.stats"
    - applyTo: NETWORK_FILTER
      match:
        context: SIDECAR_OUTBOUND
        proxy:
          proxyVersion: '^1\.8.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.tcp_proxy"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.network.wasm.v3.Wasm
            value:
              config:
                root_id: stats_outbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                    }
                vm_config:
                  vm_id: tcp_stats_outbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: "envoy.wasm.stats"
    - applyTo: NETWORK_FILTER
      match:
        context: GATEWAY
        proxy:
          proxyVersion: '^1\.8.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.tcp_proxy"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.network.wasm.v3.Wasm
            value:
              config:
                root_id: stats_outbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                    }
                vm_config:
                  vm_id: tcp_stats_outbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: "envoy.wasm.stats"
---
apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: tcp-stats-filter-1.9
  namespace: istio-system
  labels:
    istio.io/rev: default
spec:
  configPatches:
    - applyTo: NETWORK_FILTER
      match:
        context: SIDECAR_INBOUND
        proxy:
          proxyVersion: '^1\.9.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.tcp_proxy"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.network.wasm.v3.Wasm
            value:
              config:
                root_id: stats_inbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio",
                      "metrics": [
                        {
                          "dimensions": {
                            "destination_cluster": "node.metadata['CLUSTER_ID']",
                            "source_cluster": "downstream_peer.cluster_id"
                          }
                        }
                      ]
                    }
                vm_config:
                  vm_id: tcp_stats_inbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: "envoy.wasm.stats"
    - applyTo: NETWORK_FILTER
      match:
        context: SIDECAR_OUTBOUND
        proxy:
          proxyVersion: '^1\.9.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.tcp_proxy"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.network.wasm.v3.Wasm
            value:
              config:
                root_id: stats_outbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio",
                      "metrics": [
                        {
                          "dimensions": {
                            "source_cluster": "node.metadata['CLUSTER_ID']",
                            "destination_cluster": "upstream_peer.cluster_id"
                          }
                        }
                      ]
                    }
                vm_config:
                  vm_id: tcp_stats_outbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: "envoy.wasm.stats"
    - applyTo: NETWORK_FILTER
      match:
        context: GATEWAY
        proxy:
          proxyVersion: '^1\.9.*'
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.tcp_proxy"
      patch:
        operation: INSERT_BEFORE
        value:
          name: istio.stats
          typed_config:
            "@type": type.googleapis.com/udpa.type.v1.TypedStruct
            type_url: type.googleapis.com/envoy.extensions.filters.network.wasm.v3.Wasm
            value:
              config:
                root_id: stats_outbound
                configuration:
                  "@type": "type.googleapis.com/google.protobuf.StringValue"
                  value: |
                    {
                      "debug": "false",
                      "stat_prefix": "istio",
                      "metrics": [
                        {
                          "dimensions": {
                            "source_cluster": "node.metadata['CLUSTER_ID']",
                            "destination_cluster": "upstream_peer.cluster_id"
                          }
                        }
                      ]
                    }
                vm_config:
                  vm_id: tcp_stats_outbound
                  runtime: envoy.wasm.runtime.null
                  code:
                    local:
                      inline_string: "envoy.wasm.stats"
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: istio
  namespace: istio-system
  labels:
    istio.io/rev: default
    install.operator.istio.io/owning-resource: unknown
    operator.istio.io/component: "Pilot"
    release: istio
data:

  # Configuration file for the mesh networks to be used by the Split Horizon EDS.
  meshNetworks: |-
    networks: {}

  mesh: |-
    accessLogEncoding: JSON
    accessLogFile: /dev/stdout
    accessLogFormat: |
      { "start_time" :"%START_TIME%",
        "method": "%REQ(:METHOD)%",
        "path": "%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%",
        "protocol": "%PROTOCOL%",
        "response_code": "%RESPONSE_CODE%",
        "response_flags": "%RESPONSE_FLAGS%",
        "response_code_details": "%RESPONSE_CODE_DETAILS%",
        "connection_termination_details": "%CONNECTION_TERMINATION_DETAILS%",
        "upstream_transport_failure_reason": "%UPSTREAM_TRANSPORT_FAILURE_REASON%",
        "bytes_received": "%BYTES_RECEIVED%",
        "bytes_sent":"%BYTES_SENT%",
        "duration": "%DURATION%",
        "upstream_service_time": "%REQ(x-envoy-upstream-service-time)%",
        "x_forwarded_for": "%REQ(X-FORWARDED-FOR)%",
        "user_agent": "%REQ(USER-AGENT)%",
        "request_id": "%REQ(X-REQUEST-ID)%",
        "authority": "%REQ(:AUTHORITY)%",
        "upstream_host": "%UPSTREAM_HOST%",
        "upstream_cluster": "%UPSTREAM_CLUSTER%",
        "upstream_local_address": "%UPSTREAM_LOCAL_ADDRESS%",
        "downstream_local_address": "%DOWNSTREAM_LOCAL_ADDRESS%",
        "downstream_remote_address": "%DOWNSTREAM_REMOTE_ADDRESS%",
        "requested_server_name": "%REQUESTED_SERVER_NAME%",
        "route_name": "%ROUTE_NAME%",
        "user_id": "%REQ(X-USER-ID)%",
        "device_type": "%REQ(X-DEVICE-TYPE)%",
        "traceId": "%REQ(x-b3-traceid)%",
        "istio_policy_status": "-"
      }
    defaultConfig:
      discoveryAddress: istiod.istio-system.svc:15012
      proxyMetadata: {}
      tracing:
        zipkin:
          address: zipkin.istio-system:9411
    enablePrometheusMerge: true
    enableTracing: true
    rootNamespace: istio-system
    trustDomain: cluster.local
---
apiVersion: v1
kind: ConfigMap
metadata:
  name: istio-sidecar-injector
  namespace: istio-system
  labels:
    istio.io/rev: default
    install.operator.istio.io/owning-resource: unknown
    operator.istio.io/component: "Pilot"
    release: istio
data:

  values: |-
    {
      "global": {
        "arch": {
          "amd64": 2,
          "ppc64le": 2,
          "s390x": 2
        },
        "caAddress": "",
        "configValidation": true,
        "defaultNodeSelector": {},
        "defaultPodDisruptionBudget": {
          "enabled": true
        },
        "defaultResources": {
          "requests": {
            "cpu": "10m"
          }
        },
        "enabled": true,
        "externalIstiod": false,
        "hub": "docker.io/istio",
        "imagePullPolicy": "",
        "imagePullSecrets": [],
        "istioNamespace": "istio-system",
        "istiod": {
          "enableAnalysis": false
        },
        "jwtPolicy": "third-party-jwt",
        "logAsJson": false,
        "logging": {
          "level": "default:info"
        },
        "meshID": "",
        "meshNetworks": {},
        "mountMtlsCerts": false,
        "multiCluster": {
          "clusterName": "",
          "enabled": false
        },
        "namespace": "istio-system",
        "network": "",
        "omitSidecarInjectorConfigMap": false,
        "oneNamespace": false,
        "operatorManageWebhooks": false,
        "pilotCertProvider": "istiod",
        "priorityClassName": "",
        "proxy": {
          "autoInject": "enabled",
          "clusterDomain": "cluster.local",
          "componentLogLevel": "misc:error",
          "enableCoreDump": false,
          "excludeIPRanges": "",
          "excludeInboundPorts": "",
          "excludeOutboundPorts": "",
          "holdApplicationUntilProxyStarts": false,
          "image": "proxyv2",
          "includeIPRanges": "*",
          "logLevel": "warning",
          "privileged": false,
          "readinessFailureThreshold": 30,
          "readinessInitialDelaySeconds": 1,
          "readinessPeriodSeconds": 2,
          "resources": {
            "limits": {
              "cpu": "2000m",
              "memory": "1024Mi"
            },
            "requests": {
              "cpu": "100m",
              "memory": "128Mi"
            }
          },
          "statusPort": 15020,
          "tracer": "zipkin"
        },
        "proxy_init": {
          "image": "proxyv2",
          "resources": {
            "limits": {
              "cpu": "2000m",
              "memory": "1024Mi"
            },
            "requests": {
              "cpu": "10m",
              "memory": "10Mi"
            }
          }
        },
        "remotePilotAddress": "",
        "sds": {
          "token": {
            "aud": "istio-ca"
          }
        },
        "sts": {
          "servicePort": 0
        },
        "tag": "1.9.2",
        "tracer": {
          "datadog": {
            "address": "$(HOST_IP):8126"
          },
          "lightstep": {
            "accessToken": "",
            "address": ""
          },
          "stackdriver": {
            "debug": false,
            "maxNumberOfAnnotations": 200,
            "maxNumberOfAttributes": 200,
            "maxNumberOfMessageEvents": 200
          },
          "zipkin": {
            "address": ""
          }
        },
        "trustDomain": "",
        "useMCP": false
      },
      "istio_cni": {
        "enabled": false
      },
      "revision": "",
      "sidecarInjectorWebhook": {
        "alwaysInjectSelector": [],
        "defaultTemplates": [],
        "enableNamespacesByDefault": false,
        "injectedAnnotations": {},
        "neverInjectSelector": [],
        "objectSelector": {
          "autoInject": true,
          "enabled": true
        },
        "rewriteAppHTTPProbe": true,
        "templates": {},
        "useLegacySelectors": true
      }
    }

  # To disable injection: use omitSidecarInjectorConfigMap, which disables the webhook patching
  # and istiod webhook functionality.
  #
  # New fields should not use Values - it is a 'primary' config object, users should be able
  # to fine tune it or use it with kube-inject.
  config: |-
    # defaultTemplates defines the default template to use for pods that do not explicitly specify a template
    defaultTemplates: [sidecar]
    policy: enabled
    alwaysInjectSelector:
      []
    neverInjectSelector:
      []
    injectedAnnotations:
    template: "{{ Template_Version_And_Istio_Version_Mismatched_Check_Installation }}"
    templates:
      sidecar: |
        {{- $containers := list }}
        {{- range $index, $container := .Spec.Containers }}{{ if not (eq $container.Name "istio-proxy") }}{{ $containers = append $containers $container.Name }}{{end}}{{- end}}
        metadata:
          labels:
            security.istio.io/tlsMode: {{ index .ObjectMeta.Labels `security.istio.io/tlsMode` | default "istio"  | quote }}
            service.istio.io/canonical-name: {{ index .ObjectMeta.Labels `service.istio.io/canonical-name` | default (index .ObjectMeta.Labels `app.kubernetes.io/name`) | default (index .ObjectMeta.Labels `app`) | default .DeploymentMeta.Name  | quote }}
            service.istio.io/canonical-revision: {{ index .ObjectMeta.Labels `service.istio.io/canonical-revision` | default (index .ObjectMeta.Labels `app.kubernetes.io/version`) | default (index .ObjectMeta.Labels `version`) | default "latest"  | quote }}
            istio.io/rev: {{ .Revision | default "default" | quote }}
          annotations: {
            {{- if eq (len $containers) 1 }}
            kubectl.kubernetes.io/default-logs-container: "{{ index $containers 0 }}",
            {{ end }}
        {{- if .Values.istio_cni.enabled }}
            {{- if not .Values.istio_cni.chained }}
            k8s.v1.cni.cncf.io/networks: '{{ appendMultusNetwork (index .ObjectMeta.Annotations `k8s.v1.cni.cncf.io/networks`) `istio-cni` }}',
            {{- end }}
            sidecar.istio.io/interceptionMode: "{{ annotation .ObjectMeta `sidecar.istio.io/interceptionMode` .ProxyConfig.InterceptionMode }}",
            {{ with annotation .ObjectMeta `traffic.sidecar.istio.io/includeOutboundIPRanges` .Values.global.proxy.includeIPRanges }}traffic.sidecar.istio.io/includeOutboundIPRanges: "{{.}}",{{ end }}
            {{ with annotation .ObjectMeta `traffic.sidecar.istio.io/excludeOutboundIPRanges` .Values.global.proxy.excludeIPRanges }}traffic.sidecar.istio.io/excludeOutboundIPRanges: "{{.}}",{{ end }}
            traffic.sidecar.istio.io/includeInboundPorts: "{{ annotation .ObjectMeta `traffic.sidecar.istio.io/includeInboundPorts` `*` }}",
            traffic.sidecar.istio.io/excludeInboundPorts: "{{ excludeInboundPort (annotation .ObjectMeta `status.sidecar.istio.io/port` .Values.global.proxy.statusPort) (annotation .ObjectMeta `traffic.sidecar.istio.io/excludeInboundPorts` .Values.global.proxy.excludeInboundPorts) }}",
            {{ if or (isset .ObjectMeta.Annotations `traffic.sidecar.istio.io/includeOutboundPorts`) (ne (valueOrDefault .Values.global.proxy.includeOutboundPorts "") "") }}
            traffic.sidecar.istio.io/includeOutboundPorts: "{{ annotation .ObjectMeta `traffic.sidecar.istio.io/includeOutboundPorts` .Values.global.proxy.includeOutboundPorts }}",
            {{- end }}
            {{ if or (isset .ObjectMeta.Annotations `traffic.sidecar.istio.io/excludeOutboundPorts`) (ne .Values.global.proxy.excludeOutboundPorts "") }}
            traffic.sidecar.istio.io/excludeOutboundPorts: "{{ annotation .ObjectMeta `traffic.sidecar.istio.io/excludeOutboundPorts` .Values.global.proxy.excludeOutboundPorts }}",
            {{- end }}
            {{ with index .ObjectMeta.Annotations `traffic.sidecar.istio.io/kubevirtInterfaces` }}traffic.sidecar.istio.io/kubevirtInterfaces: "{{.}}",{{ end }}
        {{- end }}
          }
        spec:
          {{- $holdProxy := or .ProxyConfig.HoldApplicationUntilProxyStarts.GetValue .Values.global.proxy.holdApplicationUntilProxyStarts }}
          initContainers:
          {{ if ne (annotation .ObjectMeta `sidecar.istio.io/interceptionMode` .ProxyConfig.InterceptionMode) `NONE` }}
          {{ if .Values.istio_cni.enabled -}}
          - name: istio-validation
          {{ else -}}
          - name: istio-init
          {{ end -}}
          {{- if contains "/" (annotation .ObjectMeta `sidecar.istio.io/proxyImage` .Values.global.proxy_init.image) }}
            image: "{{ annotation .ObjectMeta `sidecar.istio.io/proxyImage` .Values.global.proxy_init.image }}"
          {{- else }}
            image: "{{ .Values.global.hub }}/{{ .Values.global.proxy_init.image }}:{{ .Values.global.tag }}"
          {{- end }}
            args:
            - istio-iptables
            - "-p"
            - "15001"
            - "-z"
            - "15006"
            - "-u"
            - "1337"
            - "-m"
            - "{{ annotation .ObjectMeta `sidecar.istio.io/interceptionMode` .ProxyConfig.InterceptionMode }}"
            - "-i"
            - "{{ annotation .ObjectMeta `traffic.sidecar.istio.io/includeOutboundIPRanges` .Values.global.proxy.includeIPRanges }}"
            - "-x"
            - "{{ annotation .ObjectMeta `traffic.sidecar.istio.io/excludeOutboundIPRanges` .Values.global.proxy.excludeIPRanges }}"
            - "-b"
            - "{{ annotation .ObjectMeta `traffic.sidecar.istio.io/includeInboundPorts` `*` }}"
            - "-d"
          {{- if excludeInboundPort (annotation .ObjectMeta `status.sidecar.istio.io/port` .Values.global.proxy.statusPort) (annotation .ObjectMeta `traffic.sidecar.istio.io/excludeInboundPorts` .Values.global.proxy.excludeInboundPorts) }}
            - "15090,15021,{{ excludeInboundPort (annotation .ObjectMeta `status.sidecar.istio.io/port` .Values.global.proxy.statusPort) (annotation .ObjectMeta `traffic.sidecar.istio.io/excludeInboundPorts` .Values.global.proxy.excludeInboundPorts) }}"
          {{- else }}
            - "15090,15021"
          {{- end }}
            {{ if or (isset .ObjectMeta.Annotations `traffic.sidecar.istio.io/includeOutboundPorts`) (ne (valueOrDefault .Values.global.proxy.includeOutboundPorts "") "") -}}
            - "-q"
            - "{{ annotation .ObjectMeta `traffic.sidecar.istio.io/includeOutboundPorts` .Values.global.proxy.includeOutboundPorts }}"
            {{ end -}}
            {{ if or (isset .ObjectMeta.Annotations `traffic.sidecar.istio.io/excludeOutboundPorts`) (ne (valueOrDefault .Values.global.proxy.excludeOutboundPorts "") "") -}}
            - "-o"
            - "{{ annotation .ObjectMeta `traffic.sidecar.istio.io/excludeOutboundPorts` .Values.global.proxy.excludeOutboundPorts }}"
            {{ end -}}
            {{ if (isset .ObjectMeta.Annotations `traffic.sidecar.istio.io/kubevirtInterfaces`) -}}
            - "-k"
            - "{{ index .ObjectMeta.Annotations `traffic.sidecar.istio.io/kubevirtInterfaces` }}"
            {{ end -}}
            {{ if .Values.istio_cni.enabled -}}
            - "--run-validation"
            - "--skip-rule-apply"
            {{ end -}}
            imagePullPolicy: "{{ valueOrDefault .Values.global.imagePullPolicy `Always` }}"
          {{- if .ProxyConfig.ProxyMetadata }}
            env:
            {{- range $key, $value := .ProxyConfig.ProxyMetadata }}
            - name: {{ $key }}
              value: "{{ $value }}"
            {{- end }}
          {{- end }}
            resources:
          {{- if or (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyCPU`) (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyMemory`) (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyCPULimit`) (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyMemoryLimit`) }}
            {{- if or (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyCPU`) (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyMemory`) }}
              requests:
                {{ if (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyCPU`) -}}
                cpu: "{{ index .ObjectMeta.Annotations `sidecar.istio.io/proxyCPU` }}"
                {{ end }}
                {{ if (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyMemory`) -}}
                memory: "{{ index .ObjectMeta.Annotations `sidecar.istio.io/proxyMemory` }}"
                {{ end }}
            {{- end }}
            {{- if or (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyCPULimit`) (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyMemoryLimit`) }}
              limits:
                {{ if (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyCPULimit`) -}}
                cpu: "{{ index .ObjectMeta.Annotations `sidecar.istio.io/proxyCPULimit` }}"
                {{ end }}
                {{ if (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyMemoryLimit`) -}}
                memory: "{{ index .ObjectMeta.Annotations `sidecar.istio.io/proxyMemoryLimit` }}"
                {{ end }}
            {{- end }}
          {{- else }}
            {{- if .Values.global.proxy.resources }}
              {{ toYaml .Values.global.proxy.resources | indent 6 }}
            {{- end }}
          {{- end }}
            securityContext:
              allowPrivilegeEscalation: {{ .Values.global.proxy.privileged }}
              privileged: {{ .Values.global.proxy.privileged }}
              capabilities:
            {{- if not .Values.istio_cni.enabled }}
                add:
                - NET_ADMIN
                - NET_RAW
            {{- end }}
                drop:
                - ALL
            {{- if not .Values.istio_cni.enabled }}
              readOnlyRootFilesystem: false
              runAsGroup: 0
              runAsNonRoot: false
              runAsUser: 0
            {{- else }}
              readOnlyRootFilesystem: true
              runAsGroup: 1337
              runAsUser: 1337
              runAsNonRoot: true
            {{- end }}
            restartPolicy: Always
          {{ end -}}
          {{- if eq .Values.global.proxy.enableCoreDump true }}
          - name: enable-core-dump
            args:
            - -c
            - sysctl -w kernel.core_pattern=/var/lib/istio/data/core.proxy && ulimit -c unlimited
            command:
              - /bin/sh
          {{- if contains "/" (annotation .ObjectMeta `sidecar.istio.io/proxyImage` .Values.global.proxy_init.image) }}
            image: "{{ annotation .ObjectMeta `sidecar.istio.io/proxyImage` .Values.global.proxy_init.image }}"
          {{- else }}
            image: "{{ .Values.global.hub }}/{{ .Values.global.proxy_init.image }}:{{ .Values.global.tag }}"
          {{- end }}
            imagePullPolicy: "{{ valueOrDefault .Values.global.imagePullPolicy `Always` }}"
            resources: {}
            securityContext:
              allowPrivilegeEscalation: true
              capabilities:
                add:
                - SYS_ADMIN
                drop:
                - ALL
              privileged: true
              readOnlyRootFilesystem: false
              runAsGroup: 0
              runAsNonRoot: false
              runAsUser: 0
          {{ end }}
          containers:
          - name: istio-proxy
          {{- if contains "/" (annotation .ObjectMeta `sidecar.istio.io/proxyImage` .Values.global.proxy.image) }}
            image: "{{ annotation .ObjectMeta `sidecar.istio.io/proxyImage` .Values.global.proxy.image }}"
          {{- else }}
            image: "{{ .Values.global.hub }}/{{ .Values.global.proxy.image }}:{{ .Values.global.tag }}"
          {{- end }}
            ports:
            - containerPort: 15090
              protocol: TCP
              name: http-envoy-prom
            args:
            - proxy
            - sidecar
            - --domain
            - $(POD_NAMESPACE).svc.{{ .Values.global.proxy.clusterDomain }}
            - --serviceCluster
            {{ if ne "" (index .ObjectMeta.Labels "app") -}}
            - "{{ index .ObjectMeta.Labels `app` }}.$(POD_NAMESPACE)"
            {{ else -}}
            - "{{ valueOrDefault .DeploymentMeta.Name `istio-proxy` }}.{{ valueOrDefault .DeploymentMeta.Namespace `default` }}"
            {{ end -}}
            - --proxyLogLevel={{ annotation .ObjectMeta `sidecar.istio.io/logLevel` .Values.global.proxy.logLevel }}
            - --proxyComponentLogLevel={{ annotation .ObjectMeta `sidecar.istio.io/componentLogLevel` .Values.global.proxy.componentLogLevel }}
            - --log_output_level={{ annotation .ObjectMeta `sidecar.istio.io/agentLogLevel` .Values.global.logging.level }}
          {{- if .Values.global.sts.servicePort }}
            - --stsPort={{ .Values.global.sts.servicePort }}
          {{- end }}
          {{- if .Values.global.logAsJson }}
            - --log_as_json
          {{- end }}
          {{- if gt .ProxyConfig.Concurrency.GetValue 0 }}
            - --concurrency
            - "{{ .ProxyConfig.Concurrency.GetValue }}"
          {{- end -}}
          {{- if .Values.global.proxy.lifecycle }}
            lifecycle:
              {{ toYaml .Values.global.proxy.lifecycle | indent 6 }}
          {{- else if $holdProxy }}
            lifecycle:
              postStart:
                exec:
                  command:
                  - pilot-agent
                  - wait
          {{- end }}
            env:
            - name: JWT_POLICY
              value: {{ .Values.global.jwtPolicy }}
            - name: PILOT_CERT_PROVIDER
              value: {{ .Values.global.pilotCertProvider }}
            - name: CA_ADDR
            {{- if .Values.global.caAddress }}
              value: {{ .Values.global.caAddress }}
            {{- else }}
              value: istiod{{- if not (eq .Values.revision "") }}-{{ .Values.revision }}{{- end }}.{{ .Values.global.istioNamespace }}.svc:15012
            {{- end }}
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: INSTANCE_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: SERVICE_ACCOUNT
              valueFrom:
                fieldRef:
                  fieldPath: spec.serviceAccountName
            - name: HOST_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            - name: CANONICAL_SERVICE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.labels['service.istio.io/canonical-name']
            - name: CANONICAL_REVISION
              valueFrom:
                fieldRef:
                  fieldPath: metadata.labels['service.istio.io/canonical-revision']
            - name: PROXY_CONFIG
              value: |
                     {{ protoToJSON .ProxyConfig }}
            - name: ISTIO_META_POD_PORTS
              value: |-
                [
                {{- $first := true }}
                {{- range $index1, $c := .Spec.Containers }}
                  {{- range $index2, $p := $c.Ports }}
                    {{- if (structToJSON $p) }}
                    {{if not $first}},{{end}}{{ structToJSON $p }}
                    {{- $first = false }}
                    {{- end }}
                  {{- end}}
                {{- end}}
                ]
            - name: ISTIO_META_APP_CONTAINERS
              value: "{{ $containers | join "," }}"
            - name: ISTIO_META_CLUSTER_ID
              value: "{{ valueOrDefault .Values.global.multiCluster.clusterName `Kubernetes` }}"
            - name: ISTIO_META_INTERCEPTION_MODE
              value: "{{ or (index .ObjectMeta.Annotations `sidecar.istio.io/interceptionMode`) .ProxyConfig.InterceptionMode.String }}"
            {{- if .Values.global.network }}
            - name: ISTIO_META_NETWORK
              value: "{{ .Values.global.network }}"
            {{- end }}
            {{ if .ObjectMeta.Annotations }}
            - name: ISTIO_METAJSON_ANNOTATIONS
              value: |
                     {{ toJSON .ObjectMeta.Annotations }}
            {{ end }}
            {{- if .DeploymentMeta.Name }}
            - name: ISTIO_META_WORKLOAD_NAME
              value: "{{ .DeploymentMeta.Name }}"
            {{ end }}
            {{- if and .TypeMeta.APIVersion .DeploymentMeta.Name }}
            - name: ISTIO_META_OWNER
              value: kubernetes://apis/{{ .TypeMeta.APIVersion }}/namespaces/{{ valueOrDefault .DeploymentMeta.Namespace `default` }}/{{ toLower .TypeMeta.Kind}}s/{{ .DeploymentMeta.Name }}
            {{- end}}
            {{- if (isset .ObjectMeta.Annotations `sidecar.istio.io/bootstrapOverride`) }}
            - name: ISTIO_BOOTSTRAP_OVERRIDE
              value: "/etc/istio/custom-bootstrap/custom_bootstrap.json"
            {{- end }}
            {{- if .Values.global.meshID }}
            - name: ISTIO_META_MESH_ID
              value: "{{ .Values.global.meshID }}"
            {{- else if (valueOrDefault .MeshConfig.TrustDomain .Values.global.trustDomain) }}
            - name: ISTIO_META_MESH_ID
              value: "{{ (valueOrDefault .MeshConfig.TrustDomain .Values.global.trustDomain) }}"
            {{- end }}
            {{- with (valueOrDefault .MeshConfig.TrustDomain .Values.global.trustDomain)  }}
            - name: TRUST_DOMAIN
              value: "{{ . }}"
            {{- end }}
            {{- if and (eq .Values.global.proxy.tracer "datadog") (isset .ObjectMeta.Annotations `apm.datadoghq.com/env`) }}
            {{- range $key, $value := fromJSON (index .ObjectMeta.Annotations `apm.datadoghq.com/env`) }}
            - name: {{ $key }}
              value: "{{ $value }}"
            {{- end }}
            {{- end }}
            {{- range $key, $value := .ProxyConfig.ProxyMetadata }}
            - name: {{ $key }}
              value: "{{ $value }}"
            {{- end }}
            imagePullPolicy: "{{ valueOrDefault .Values.global.imagePullPolicy `Always` }}"
            {{ if ne (annotation .ObjectMeta `status.sidecar.istio.io/port` .Values.global.proxy.statusPort) `0` }}
            readinessProbe:
              httpGet:
                path: /healthz/ready
                port: 15021
              initialDelaySeconds: {{ annotation .ObjectMeta `readiness.status.sidecar.istio.io/initialDelaySeconds` .Values.global.proxy.readinessInitialDelaySeconds }}
              periodSeconds: {{ annotation .ObjectMeta `readiness.status.sidecar.istio.io/periodSeconds` .Values.global.proxy.readinessPeriodSeconds }}
              timeoutSeconds: 3
              failureThreshold: {{ annotation .ObjectMeta `readiness.status.sidecar.istio.io/failureThreshold` .Values.global.proxy.readinessFailureThreshold }}
            {{ end -}}
            securityContext:
              allowPrivilegeEscalation: {{ .Values.global.proxy.privileged }}
              capabilities:
                {{ if or (eq (annotation .ObjectMeta `sidecar.istio.io/interceptionMode` .ProxyConfig.InterceptionMode) `TPROXY`) (eq (annotation .ObjectMeta `sidecar.istio.io/capNetBindService` .Values.global.proxy.capNetBindService) `true`) -}}
                add:
                {{ if eq (annotation .ObjectMeta `sidecar.istio.io/interceptionMode` .ProxyConfig.InterceptionMode) `TPROXY` -}}
                - NET_ADMIN
                {{- end }}
                {{ if eq (annotation .ObjectMeta `sidecar.istio.io/capNetBindService` .Values.global.proxy.capNetBindService) `true` -}}
                - NET_BIND_SERVICE
                {{- end }}
                {{- end }}
                drop:
                - ALL
              privileged: {{ .Values.global.proxy.privileged }}
              readOnlyRootFilesystem: {{ not .Values.global.proxy.enableCoreDump }}
              runAsGroup: 1337
              fsGroup: 1337
              {{ if or (eq (annotation .ObjectMeta `sidecar.istio.io/interceptionMode` .ProxyConfig.InterceptionMode) `TPROXY`) (eq (annotation .ObjectMeta `sidecar.istio.io/capNetBindService` .Values.global.proxy.capNetBindService) `true`) -}}
              runAsNonRoot: false
              runAsUser: 0
              {{- else -}}
              runAsNonRoot: true
              runAsUser: 1337
              {{- end }}
            resources:
          {{- if or (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyCPU`) (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyMemory`) (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyCPULimit`) (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyMemoryLimit`) }}
            {{- if or (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyCPU`) (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyMemory`) }}
              requests:
                {{ if (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyCPU`) -}}
                cpu: "{{ index .ObjectMeta.Annotations `sidecar.istio.io/proxyCPU` }}"
                {{ end }}
                {{ if (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyMemory`) -}}
                memory: "{{ index .ObjectMeta.Annotations `sidecar.istio.io/proxyMemory` }}"
                {{ end }}
            {{- end }}
            {{- if or (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyCPULimit`) (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyMemoryLimit`) }}
              limits:
                {{ if (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyCPULimit`) -}}
                cpu: "{{ index .ObjectMeta.Annotations `sidecar.istio.io/proxyCPULimit` }}"
                {{ end }}
                {{ if (isset .ObjectMeta.Annotations `sidecar.istio.io/proxyMemoryLimit`) -}}
                memory: "{{ index .ObjectMeta.Annotations `sidecar.istio.io/proxyMemoryLimit` }}"
                {{ end }}
            {{- end }}
          {{- else }}
            {{- if .Values.global.proxy.resources }}
              {{ toYaml .Values.global.proxy.resources | indent 6 }}
            {{- end }}
          {{- end }}
            volumeMounts:
            {{- if eq .Values.global.pilotCertProvider "istiod" }}
            - mountPath: /var/run/secrets/istio
              name: istiod-ca-cert
            {{- end }}
            - mountPath: /var/lib/istio/data
              name: istio-data
            {{ if (isset .ObjectMeta.Annotations `sidecar.istio.io/bootstrapOverride`) }}
            - mountPath: /etc/istio/custom-bootstrap
              name: custom-bootstrap-volume
            {{- end }}
            # SDS channel between istioagent and Envoy
            - mountPath: /etc/istio/proxy
              name: istio-envoy
            {{- if eq .Values.global.jwtPolicy "third-party-jwt" }}
            - mountPath: /var/run/secrets/tokens
              name: istio-token
            {{- end }}
            {{- if .Values.global.mountMtlsCerts }}
            # Use the key and cert mounted to /etc/certs/ for the in-cluster mTLS communications.
            - mountPath: /etc/certs/
              name: istio-certs
              readOnly: true
            {{- end }}
            - name: istio-podinfo
              mountPath: /etc/istio/pod
             {{- if and (eq .Values.global.proxy.tracer "lightstep") .ProxyConfig.GetTracing.GetTlsSettings }}
            - mountPath: {{ directory .ProxyConfig.GetTracing.GetTlsSettings.GetCaCertificates }}
              name: lightstep-certs
              readOnly: true
            {{- end }}
              {{- if isset .ObjectMeta.Annotations `sidecar.istio.io/userVolumeMount` }}
              {{ range $index, $value := fromJSON (index .ObjectMeta.Annotations `sidecar.istio.io/userVolumeMount`) }}
            - name: "{{  $index }}"
              {{ toYaml $value | indent 6 }}
              {{ end }}
              {{- end }}
          volumes:
          {{- if (isset .ObjectMeta.Annotations `sidecar.istio.io/bootstrapOverride`) }}
          - name: custom-bootstrap-volume
            configMap:
              name: {{ annotation .ObjectMeta `sidecar.istio.io/bootstrapOverride` "" }}
          {{- end }}
          # SDS channel between istioagent and Envoy
          - emptyDir:
              medium: Memory
            name: istio-envoy
          - name: istio-data
            emptyDir: {}
          - name: istio-podinfo
            downwardAPI:
              items:
                - path: "labels"
                  fieldRef:
                    fieldPath: metadata.labels
                - path: "annotations"
                  fieldRef:
                    fieldPath: metadata.annotations
                - path: "cpu-limit"
                  resourceFieldRef:
                    containerName: istio-proxy
                    resource: limits.cpu
                    divisor: 1m
                - path: "cpu-request"
                  resourceFieldRef:
                    containerName: istio-proxy
                    resource: requests.cpu
                    divisor: 1m
          {{- if eq .Values.global.jwtPolicy "third-party-jwt" }}
          - name: istio-token
            projected:
              sources:
              - serviceAccountToken:
                  path: istio-token
                  expirationSeconds: 43200
                  audience: {{ .Values.global.sds.token.aud }}
          {{- end }}
          {{- if eq .Values.global.pilotCertProvider "istiod" }}
          - name: istiod-ca-cert
            configMap:
              name: istio-ca-root-cert
          {{- end }}
          {{- if .Values.global.mountMtlsCerts }}
          # Use the key and cert mounted to /etc/certs/ for the in-cluster mTLS communications.
          - name: istio-certs
            secret:
              optional: true
              {{ if eq .Spec.ServiceAccountName "" }}
              secretName: istio.default
              {{ else -}}
              secretName: {{  printf "istio.%s" .Spec.ServiceAccountName }}
              {{  end -}}
          {{- end }}
            {{- if isset .ObjectMeta.Annotations `sidecar.istio.io/userVolume` }}
            {{range $index, $value := fromJSON (index .ObjectMeta.Annotations `sidecar.istio.io/userVolume`) }}
          - name: "{{ $index }}"
            {{ toYaml $value | indent 4 }}
            {{ end }}
            {{ end }}
          {{- if and (eq .Values.global.proxy.tracer "lightstep") .ProxyConfig.GetTracing.GetTlsSettings }}
          - name: lightstep-certs
            secret:
              optional: true
              secretName: lightstep.cacert
          {{- end }}
          {{- if .Values.global.imagePullSecrets }}
          imagePullSecrets:
            {{- range .Values.global.imagePullSecrets }}
            - name: {{ . }}
            {{- end }}
          {{- end }}
          {{- if eq (env "ENABLE_LEGACY_FSGROUP_INJECTION" "true") "true" }}
          securityContext:
            fsGroup: 1337
          {{- end }}
      gateway: |
        {{- $containers := list }}
        {{- range $index, $container := .Spec.Containers }}{{ if not (eq $container.Name "istio-proxy") }}{{ $containers = append $containers $container.Name }}{{end}}{{- end}}
        metadata:
          labels:
            service.istio.io/canonical-name: {{ index .ObjectMeta.Labels `service.istio.io/canonical-name` | default (index .ObjectMeta.Labels `app.kubernetes.io/name`) | default (index .ObjectMeta.Labels `app`) | default .DeploymentMeta.Name  | quote }}
            service.istio.io/canonical-revision: {{ index .ObjectMeta.Labels `service.istio.io/canonical-revision` | default (index .ObjectMeta.Labels `app.kubernetes.io/version`) | default (index .ObjectMeta.Labels `version`) | default "latest"  | quote }}
            istio.io/rev: {{ .Revision | default "default" | quote }}
          annotations: {
            {{- if eq (len $containers) 1 }}
            kubectl.kubernetes.io/default-logs-container: "{{ index $containers 0 }}",
            {{ end }}
          }
        spec:
          containers:
          - name: istio-proxy
          {{- if contains "/" .Values.global.proxy.image }}
            image: "{{ annotation .ObjectMeta `sidecar.istio.io/proxyImage` .Values.global.proxy.image }}"
          {{- else }}
            image: "{{ .Values.global.hub }}/{{ .Values.global.proxy.image }}:{{ .Values.global.tag }}"
          {{- end }}
            ports:
            - containerPort: 15090
              protocol: TCP
              name: http-envoy-prom
            args:
            - proxy
            - router
            - --domain
            - $(POD_NAMESPACE).svc.{{ .Values.global.proxy.clusterDomain }}
            - --serviceCluster
            {{ if ne "" (index .ObjectMeta.Labels "app") -}}
            - "{{ index .ObjectMeta.Labels `app` }}.$(POD_NAMESPACE)"
            {{ else -}}
            - "{{ valueOrDefault .DeploymentMeta.Name `istio-proxy` }}.{{ valueOrDefault .DeploymentMeta.Namespace `default` }}"
            {{ end -}}
            - --proxyLogLevel={{ annotation .ObjectMeta `sidecar.istio.io/logLevel` .Values.global.proxy.logLevel }}
            - --proxyComponentLogLevel={{ annotation .ObjectMeta `sidecar.istio.io/componentLogLevel` .Values.global.proxy.componentLogLevel }}
            - --log_output_level={{ annotation .ObjectMeta `sidecar.istio.io/agentLogLevel` .Values.global.logging.level }}
          {{- if .Values.global.sts.servicePort }}
            - --stsPort={{ .Values.global.sts.servicePort }}
          {{- end }}
          {{- if .Values.global.logAsJson }}
            - --log_as_json
          {{- end }}
          {{- if .Values.global.proxy.lifecycle }}
            lifecycle:
              {{ toYaml .Values.global.proxy.lifecycle | indent 6 }}
          {{- end }}
            env:
            - name: JWT_POLICY
              value: {{ .Values.global.jwtPolicy }}
            - name: PILOT_CERT_PROVIDER
              value: {{ .Values.global.pilotCertProvider }}
            - name: CA_ADDR
            {{- if .Values.global.caAddress }}
              value: {{ .Values.global.caAddress }}
            {{- else }}
              value: istiod{{- if not (eq .Values.revision "") }}-{{ .Values.revision }}{{- end }}.{{ .Values.global.istioNamespace }}.svc:15012
            {{- end }}
            - name: POD_NAME
              valueFrom:
                fieldRef:
                  fieldPath: metadata.name
            - name: POD_NAMESPACE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.namespace
            - name: INSTANCE_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: SERVICE_ACCOUNT
              valueFrom:
                fieldRef:
                  fieldPath: spec.serviceAccountName
            - name: HOST_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.hostIP
            - name: CANONICAL_SERVICE
              valueFrom:
                fieldRef:
                  fieldPath: metadata.labels['service.istio.io/canonical-name']
            - name: CANONICAL_REVISION
              valueFrom:
                fieldRef:
                  fieldPath: metadata.labels['service.istio.io/canonical-revision']
            - name: PROXY_CONFIG
              value: |
                     {{ protoToJSON .ProxyConfig }}
            - name: ISTIO_META_POD_PORTS
              value: |-
                [
                {{- $first := true }}
                {{- range $index1, $c := .Spec.Containers }}
                  {{- range $index2, $p := $c.Ports }}
                    {{- if (structToJSON $p) }}
                    {{if not $first}},{{end}}{{ structToJSON $p }}
                    {{- $first = false }}
                    {{- end }}
                  {{- end}}
                {{- end}}
                ]
            - name: ISTIO_META_APP_CONTAINERS
              value: "{{ $containers | join "," }}"
            - name: ISTIO_META_CLUSTER_ID
              value: "{{ valueOrDefault .Values.global.multiCluster.clusterName `Kubernetes` }}"
            - name: ISTIO_META_INTERCEPTION_MODE
              value: "{{ .ProxyConfig.InterceptionMode.String }}"
            {{- if .Values.global.network }}
            - name: ISTIO_META_NETWORK
              value: "{{ .Values.global.network }}"
            {{- end }}
            {{ if .ObjectMeta.Annotations }}
            - name: ISTIO_METAJSON_ANNOTATIONS
              value: |
                     {{ toJSON .ObjectMeta.Annotations }}
            {{ end }}
            {{- if .DeploymentMeta.Name }}
            - name: ISTIO_META_WORKLOAD_NAME
              value: "{{ .DeploymentMeta.Name }}"
            {{ end }}
            {{- if and .TypeMeta.APIVersion .DeploymentMeta.Name }}
            - name: ISTIO_META_OWNER
              value: kubernetes://apis/{{ .TypeMeta.APIVersion }}/namespaces/{{ valueOrDefault .DeploymentMeta.Namespace `default` }}/{{ toLower .TypeMeta.Kind}}s/{{ .DeploymentMeta.Name }}
            {{- end}}
            {{- if .Values.global.meshID }}
            - name: ISTIO_META_MESH_ID
              value: "{{ .Values.global.meshID }}"
            {{- else if (valueOrDefault .MeshConfig.TrustDomain .Values.global.trustDomain) }}
            - name: ISTIO_META_MESH_ID
              value: "{{ (valueOrDefault .MeshConfig.TrustDomain .Values.global.trustDomain) }}"
            {{- end }}
            {{- with (valueOrDefault .MeshConfig.TrustDomain .Values.global.trustDomain)  }}
            - name: TRUST_DOMAIN
              value: "{{ . }}"
            {{- end }}
            {{- range $key, $value := .ProxyConfig.ProxyMetadata }}
            - name: {{ $key }}
              value: "{{ $value }}"
            {{- end }}
            {{with .Values.global.imagePullPolicy }}imagePullPolicy: "{{.}}"{{end}}
            readinessProbe:
              httpGet:
                path: /healthz/ready
                port: 15021
              initialDelaySeconds: {{.Values.global.proxy.readinessInitialDelaySeconds }}
              periodSeconds: {{ .Values.global.proxy.readinessPeriodSeconds }}
              timeoutSeconds: 3
              failureThreshold: {{ .Values.global.proxy.readinessFailureThreshold }}
            volumeMounts:
            {{- if eq .Values.global.pilotCertProvider "istiod" }}
            - mountPath: /var/run/secrets/istio
              name: istiod-ca-cert
            {{- end }}
            - mountPath: /var/lib/istio/data
              name: istio-data
            # SDS channel between istioagent and Envoy
            - mountPath: /etc/istio/proxy
              name: istio-envoy
            {{- if eq .Values.global.jwtPolicy "third-party-jwt" }}
            - mountPath: /var/run/secrets/tokens
              name: istio-token
            {{- end }}
            {{- if .Values.global.mountMtlsCerts }}
            # Use the key and cert mounted to /etc/certs/ for the in-cluster mTLS communications.
            - mountPath: /etc/certs/
              name: istio-certs
              readOnly: true
            {{- end }}
            - name: istio-podinfo
              mountPath: /etc/istio/pod
          volumes:
          # SDS channel between istioagent and Envoy
          - emptyDir:
              medium: Memory
            name: istio-envoy
          - name: istio-data
            emptyDir: {}
          - name: istio-podinfo
            downwardAPI:
              items:
                - path: "labels"
                  fieldRef:
                    fieldPath: metadata.labels
                - path: "annotations"
                  fieldRef:
                    fieldPath: metadata.annotations
                - path: "cpu-limit"
                  resourceFieldRef:
                    containerName: istio-proxy
                    resource: limits.cpu
                    divisor: 1m
                - path: "cpu-request"
                  resourceFieldRef:
                    containerName: istio-proxy
                    resource: requests.cpu
                    divisor: 1m
          {{- if eq .Values.global.jwtPolicy "third-party-jwt" }}
          - name: istio-token
            projected:
              sources:
              - serviceAccountToken:
                  path: istio-token
                  expirationSeconds: 43200
                  audience: {{ .Values.global.sds.token.aud }}
          {{- end }}
          {{- if eq .Values.global.pilotCertProvider "istiod" }}
          - name: istiod-ca-cert
            configMap:
              name: istio-ca-root-cert
          {{- end }}
          {{- if .Values.global.mountMtlsCerts }}
          # Use the key and cert mounted to /etc/certs/ for the in-cluster mTLS communications.
          - name: istio-certs
            secret:
              optional: true
              {{ if eq .Spec.ServiceAccountName "" }}
              secretName: istio.default
              {{ else -}}
              secretName: {{  printf "istio.%s" .Spec.ServiceAccountName }}
              {{  end -}}
          {{- end }}
          {{- if .Values.global.imagePullSecrets }}
          imagePullSecrets:
            {{- range .Values.global.imagePullSecrets }}
            - name: {{ . }}
            {{- end }}
          {{- end }}
          {{- if eq (env "ENABLE_LEGACY_FSGROUP_INJECTION" "true") "true" }}
          securityContext:
            fsGroup: 1337
          {{- end }}
---
apiVersion: admissionregistration.k8s.io/v1beta1
kind: MutatingWebhookConfiguration
metadata:
  name: istio-sidecar-injector
  labels:
    istio.io/rev: default
    install.operator.istio.io/owning-resource: unknown
    operator.istio.io/component: "Pilot"
    app: sidecar-injector
    release: istio
webhooks:
- name: sidecar-injector.istio.io
  clientConfig:
    service:
      name: istiod
      namespace: istio-system
      path: "/inject"
    caBundle: ""
  sideEffects: None
  rules:
  - operations: [ "CREATE" ]
    apiGroups: [""]
    apiVersions: ["v1"]
    resources: ["pods"]
  failurePolicy: Fail
  admissionReviewVersions: ["v1beta1", "v1"]
  namespaceSelector:
    matchLabels:
      istio-injection: enabled
  objectSelector:
    matchExpressions:
    - key: "sidecar.istio.io/inject"
      operator: NotIn
      values:
      - "false"
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: istio-ingressgateway
    install.operator.istio.io/owning-resource: unknown
    istio: ingressgateway
    istio.io/rev: default
    operator.istio.io/component: IngressGateways
    release: istio
  name: istio-ingressgateway
  namespace: istio-system
spec:
  selector:
    matchLabels:
      app: istio-ingressgateway
      istio: ingressgateway
  strategy:
    rollingUpdate:
      maxSurge: 100%
      maxUnavailable: 25%
  template:
    metadata:
      annotations:
        prometheus.io/path: /stats/prometheus
        prometheus.io/port: "15020"
        prometheus.io/scrape: "true"
        sidecar.istio.io/inject: "false"
      labels:
        app: istio-ingressgateway
        chart: gateways
        heritage: Tiller
        install.operator.istio.io/owning-resource: unknown
        istio: ingressgateway
        istio.io/rev: default
        operator.istio.io/component: IngressGateways
        release: istio
        service.istio.io/canonical-name: istio-ingressgateway
        service.istio.io/canonical-revision: latest
        sidecar.istio.io/inject: "false"
    spec:
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - preference:
              matchExpressions:
              - key: kubernetes.io/arch
                operator: In
                values:
                - amd64
            weight: 2
          - preference:
              matchExpressions:
              - key: kubernetes.io/arch
                operator: In
                values:
                - ppc64le
            weight: 2
          - preference:
              matchExpressions:
              - key: kubernetes.io/arch
                operator: In
                values:
                - s390x
            weight: 2
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: kubernetes.io/arch
                operator: In
                values:
                - amd64
                - ppc64le
                - s390x
      containers:
      - args:
        - proxy
        - router
        - --domain
        - $(POD_NAMESPACE).svc.cluster.local
        - --proxyLogLevel=warning
        - --proxyComponentLogLevel=misc:error
        - --log_output_level=default:info
        - --serviceCluster
        - istio-ingressgateway
        env:
        - name: JWT_POLICY
          value: third-party-jwt
        - name: PILOT_CERT_PROVIDER
          value: istiod
        - name: CA_ADDR
          value: istiod.istio-system.svc:15012
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: spec.nodeName
        - name: POD_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.namespace
        - name: INSTANCE_IP
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.podIP
        - name: HOST_IP
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.hostIP
        - name: SERVICE_ACCOUNT
          valueFrom:
            fieldRef:
              fieldPath: spec.serviceAccountName
        - name: CANONICAL_SERVICE
          valueFrom:
            fieldRef:
              fieldPath: metadata.labels['service.istio.io/canonical-name']
        - name: CANONICAL_REVISION
          valueFrom:
            fieldRef:
              fieldPath: metadata.labels['service.istio.io/canonical-revision']
        - name: ISTIO_META_WORKLOAD_NAME
          value: istio-ingressgateway
        - name: ISTIO_META_OWNER
          value: kubernetes://apis/apps/v1/namespaces/istio-system/deployments/istio-ingressgateway
        - name: ISTIO_META_UNPRIVILEGED_POD
          value: "true"
        - name: ISTIO_META_ROUTER_MODE
          value: standard
        - name: ISTIO_META_CLUSTER_ID
          value: Kubernetes
        image: docker.io/istio/proxyv2:1.9.2
        name: istio-proxy
        ports:
        - containerPort: 15021
          protocol: TCP
        - containerPort: 8080
          protocol: TCP
        - containerPort: 8443
          protocol: TCP
        - containerPort: 15012
          protocol: TCP
        - containerPort: 15443
          protocol: TCP
        - containerPort: 15090
          name: http-envoy-prom
          protocol: TCP
        readinessProbe:
          failureThreshold: 30
          httpGet:
            path: /healthz/ready
            port: 15021
            scheme: HTTP
          initialDelaySeconds: 1
          periodSeconds: 2
          successThreshold: 1
          timeoutSeconds: 1
        resources:
          limits:
            cpu: 1000m
            memory: 1024Mi
          requests:
            cpu: 100m
            memory: 128Mi
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          privileged: false
          readOnlyRootFilesystem: true
        volumeMounts:
        - mountPath: /etc/istio/proxy
          name: istio-envoy
        - mountPath: /etc/istio/config
          name: config-volume
        - mountPath: /var/run/secrets/istio
          name: istiod-ca-cert
        - mountPath: /var/run/secrets/tokens
          name: istio-token
          readOnly: true
        - mountPath: /var/lib/istio/data
          name: istio-data
        - mountPath: /etc/istio/pod
          name: podinfo
        - mountPath: /etc/istio/ingressgateway-certs
          name: ingressgateway-certs
          readOnly: true
        - mountPath: /etc/istio/ingressgateway-ca-certs
          name: ingressgateway-ca-certs
          readOnly: true
      securityContext:
        fsGroup: 1337
        runAsGroup: 1337
        runAsNonRoot: true
        runAsUser: 1337
      serviceAccountName: istio-ingressgateway-service-account
      volumes:
      - configMap:
          name: istio-ca-root-cert
        name: istiod-ca-cert
      - downwardAPI:
          items:
          - fieldRef:
              fieldPath: metadata.labels
            path: labels
          - fieldRef:
              fieldPath: metadata.annotations
            path: annotations
          - path: cpu-limit
            resourceFieldRef:
              containerName: istio-proxy
              divisor: 1m
              resource: limits.cpu
          - path: cpu-request
            resourceFieldRef:
              containerName: istio-proxy
              divisor: 1m
              resource: requests.cpu
        name: podinfo
      - emptyDir: {}
        name: istio-envoy
      - emptyDir: {}
        name: istio-data
      - name: istio-token
        projected:
          sources:
          - serviceAccountToken:
              audience: istio-ca
              expirationSeconds: 43200
              path: istio-token
      - configMap:
          name: istio
          optional: true
        name: config-volume
      - name: ingressgateway-certs
        secret:
          optional: true
          secretName: istio-ingressgateway-certs
      - name: ingressgateway-ca-certs
        secret:
          optional: true
          secretName: istio-ingressgateway-ca-certs
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: istio-ingressgateway
    install.operator.istio.io/owning-resource: unknown
    istio: istio-ingressgateway-internal
    istio.io/rev: default
    operator.istio.io/component: IngressGateways
    release: istio
  name: istio-ingressgateway-internal
  namespace: istio-system
spec:
  selector:
    matchLabels:
      app: istio-ingressgateway
      istio: istio-ingressgateway-internal
  strategy:
    rollingUpdate:
      maxSurge: 100%
      maxUnavailable: 25%
  template:
    metadata:
      annotations:
        prometheus.io/path: /stats/prometheus
        prometheus.io/port: "15020"
        prometheus.io/scrape: "true"
        sidecar.istio.io/inject: "false"
      labels:
        app: istio-ingressgateway
        chart: gateways
        heritage: Tiller
        install.operator.istio.io/owning-resource: unknown
        istio: istio-ingressgateway-internal
        istio.io/rev: default
        operator.istio.io/component: IngressGateways
        release: istio
        service.istio.io/canonical-name: istio-ingressgateway-internal
        service.istio.io/canonical-revision: latest
        sidecar.istio.io/inject: "false"
    spec:
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - preference:
              matchExpressions:
              - key: kubernetes.io/arch
                operator: In
                values:
                - amd64
            weight: 2
          - preference:
              matchExpressions:
              - key: kubernetes.io/arch
                operator: In
                values:
                - ppc64le
            weight: 2
          - preference:
              matchExpressions:
              - key: kubernetes.io/arch
                operator: In
                values:
                - s390x
            weight: 2
          requiredDuringSchedulingIgnoredDuringExecution:
            nodeSelectorTerms:
            - matchExpressions:
              - key: kubernetes.io/arch
                operator: In
                values:
                - amd64
                - ppc64le
                - s390x
      containers:
      - args:
        - proxy
        - router
        - --domain
        - $(POD_NAMESPACE).svc.cluster.local
        - --proxyLogLevel=warning
        - --proxyComponentLogLevel=misc:error
        - --log_output_level=default:info
        - --serviceCluster
        - istio-ingressgateway-internal
        env:
        - name: JWT_POLICY
          value: third-party-jwt
        - name: PILOT_CERT_PROVIDER
          value: istiod
        - name: CA_ADDR
          value: istiod.istio-system.svc:15012
        - name: NODE_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: spec.nodeName
        - name: POD_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.namespace
        - name: INSTANCE_IP
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.podIP
        - name: HOST_IP
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: status.hostIP
        - name: SERVICE_ACCOUNT
          valueFrom:
            fieldRef:
              fieldPath: spec.serviceAccountName
        - name: CANONICAL_SERVICE
          valueFrom:
            fieldRef:
              fieldPath: metadata.labels['service.istio.io/canonical-name']
        - name: CANONICAL_REVISION
          valueFrom:
            fieldRef:
              fieldPath: metadata.labels['service.istio.io/canonical-revision']
        - name: ISTIO_META_WORKLOAD_NAME
          value: istio-ingressgateway-internal
        - name: ISTIO_META_OWNER
          value: kubernetes://apis/apps/v1/namespaces/istio-system/deployments/istio-ingressgateway-internal
        - name: ISTIO_META_UNPRIVILEGED_POD
          value: "true"
        - name: ISTIO_META_ROUTER_MODE
          value: standard
        - name: ISTIO_META_CLUSTER_ID
          value: Kubernetes
        image: docker.io/istio/proxyv2:1.9.2
        name: istio-proxy
        ports:
        - containerPort: 15021
          protocol: TCP
        - containerPort: 8080
          protocol: TCP
        - containerPort: 8443
          protocol: TCP
        - containerPort: 15012
          protocol: TCP
        - containerPort: 15443
          protocol: TCP
        - containerPort: 16717
          protocol: TCP
        - containerPort: 15090
          name: http-envoy-prom
          protocol: TCP
        readinessProbe:
          failureThreshold: 30
          httpGet:
            path: /healthz/ready
            port: 15021
            scheme: HTTP
          initialDelaySeconds: 1
          periodSeconds: 2
          successThreshold: 1
          timeoutSeconds: 1
        resources:
          limits:
            cpu: 1000m
            memory: 1024Mi
          requests:
            cpu: 100m
            memory: 128Mi
        securityContext:
          allowPrivilegeEscalation: false
          capabilities:
            drop:
            - ALL
          privileged: false
          readOnlyRootFilesystem: true
        volumeMounts:
        - mountPath: /etc/istio/proxy
          name: istio-envoy
        - mountPath: /etc/istio/config
          name: config-volume
        - mountPath: /var/run/secrets/istio
          name: istiod-ca-cert
        - mountPath: /var/run/secrets/tokens
          name: istio-token
          readOnly: true
        - mountPath: /var/lib/istio/data
          name: istio-data
        - mountPath: /etc/istio/pod
          name: podinfo
        - mountPath: /etc/istio/ingressgateway-certs
          name: ingressgateway-certs
          readOnly: true
        - mountPath: /etc/istio/ingressgateway-ca-certs
          name: ingressgateway-ca-certs
          readOnly: true
      securityContext:
        fsGroup: 1337
        runAsGroup: 1337
        runAsNonRoot: true
        runAsUser: 1337
      serviceAccountName: istio-ingressgateway-internal-service-account
      volumes:
      - configMap:
          name: istio-ca-root-cert
        name: istiod-ca-cert
      - downwardAPI:
          items:
          - fieldRef:
              fieldPath: metadata.labels
            path: labels
          - fieldRef:
              fieldPath: metadata.annotations
            path: annotations
          - path: cpu-limit
            resourceFieldRef:
              containerName: istio-proxy
              divisor: 1m
              resource: limits.cpu
          - path: cpu-request
            resourceFieldRef:
              containerName: istio-proxy
              divisor: 1m
              resource: requests.cpu
        name: podinfo
      - emptyDir: {}
        name: istio-envoy
      - emptyDir: {}
        name: istio-data
      - name: istio-token
        projected:
          sources:
          - serviceAccountToken:
              audience: istio-ca
              expirationSeconds: 43200
              path: istio-token
      - configMap:
          name: istio
          optional: true
        name: config-volume
      - name: ingressgateway-certs
        secret:
          optional: true
          secretName: istio-ingressgateway-certs
      - name: ingressgateway-ca-certs
        secret:
          optional: true
          secretName: istio-ingressgateway-ca-certs
---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: istiod
    install.operator.istio.io/owning-resource: unknown
    istio: pilot
    istio.io/rev: default
    operator.istio.io/component: Pilot
    release: istio
  name: istiod
  namespace: istio-system
spec:
  selector:
    matchLabels:
      istio: pilot
  strategy:
    rollingUpdate:
      maxSurge: 100%
      maxUnavailable: 25%
  template:
    metadata:
      annotations:
        prometheus.io/port: "15014"
        prometheus.io/scrape: "true"
        sidecar.istio.io/inject: "false"
      labels:
        app: istiod
        install.operator.istio.io/owning-resource: unknown
        istio: pilot
        istio.io/rev: default
        operator.istio.io/component: Pilot
        sidecar.istio.io/inject: "false"
    spec:
      containers:
      - args:
        - discovery
        - --monitoringAddr=:15014
        - --log_output_level=default:info
        - --domain
        - cluster.local
        - --keepaliveMaxServerConnectionAge
        - 30m
        env:
        - name: REVISION
          value: default
        - name: JWT_POLICY
          value: third-party-jwt
        - name: PILOT_CERT_PROVIDER
          value: istiod
        - name: POD_NAME
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: metadata.namespace
        - name: SERVICE_ACCOUNT
          valueFrom:
            fieldRef:
              apiVersion: v1
              fieldPath: spec.serviceAccountName
        - name: KUBECONFIG
          value: /var/run/secrets/remote/config
        - name: PILOT_TRACE_SAMPLING
          value: "1"
        - name: PILOT_ENABLE_PROTOCOL_SNIFFING_FOR_OUTBOUND
          value: "true"
        - name: PILOT_ENABLE_PROTOCOL_SNIFFING_FOR_INBOUND
          value: "true"
        - name: ISTIOD_ADDR
          value: istiod.istio-system.svc:15012
        - name: PILOT_ENABLE_ANALYSIS
          value: "false"
        - name: CLUSTER_ID
          value: Kubernetes
        - name: EXTERNAL_ISTIOD
          value: "false"
        image: docker.io/istio/pilot:1.9.2
        name: discovery
        ports:
        - containerPort: 8080
          protocol: TCP
        - containerPort: 15010
          protocol: TCP
        - containerPort: 15017
          protocol: TCP
        readinessProbe:
          httpGet:
            path: /ready
            port: 8080
          initialDelaySeconds: 1
          periodSeconds: 3
          timeoutSeconds: 5
        resources:
          limits:
            cpu: 1000m
            memory: 4096Mi
          requests:
            cpu: 100m
            memory: 1024Mi
        securityContext:
          capabilities:
            drop:
            - ALL
          runAsGroup: 1337
          runAsNonRoot: true
          runAsUser: 1337
        volumeMounts:
        - mountPath: /etc/istio/config
          name: config-volume
        - mountPath: /var/run/secrets/tokens
          name: istio-token
          readOnly: true
        - mountPath: /var/run/secrets/istio-dns
          name: local-certs
        - mountPath: /etc/cacerts
          name: cacerts
          readOnly: true
        - mountPath: /var/run/secrets/remote
          name: istio-kubeconfig
          readOnly: true
        - mountPath: /var/lib/istio/inject
          name: inject
          readOnly: true
      securityContext:
        fsGroup: 1337
      serviceAccountName: istiod-service-account
      volumes:
      - emptyDir:
          medium: Memory
        name: local-certs
      - name: istio-token
        projected:
          sources:
          - serviceAccountToken:
              audience: istio-ca
              expirationSeconds: 43200
              path: istio-token
      - name: cacerts
        secret:
          optional: true
          secretName: cacerts
      - name: istio-kubeconfig
        secret:
          optional: true
          secretName: istio-kubeconfig
      - configMap:
          name: istio-sidecar-injector
        name: inject
      - configMap:
          name: istio
        name: config-volume
---
apiVersion: policy/v1beta1
kind: PodDisruptionBudget
metadata:
  name: istio-ingressgateway
  namespace: istio-system
  labels:
    app: istio-ingressgateway
    istio: ingressgateway
    release: istio
    istio.io/rev: default
    install.operator.istio.io/owning-resource: unknown
    operator.istio.io/component: "IngressGateways"
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: istio-ingressgateway
      istio: ingressgateway
---
apiVersion: policy/v1beta1
kind: PodDisruptionBudget
metadata:
  name: istio-ingressgateway-internal
  namespace: istio-system
  labels:
    app: istio-ingressgateway
    istio: istio-ingressgateway-internal
    release: istio
    istio.io/rev: default
    install.operator.istio.io/owning-resource: unknown
    operator.istio.io/component: "IngressGateways"
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: istio-ingressgateway
      istio: istio-ingressgateway-internal
---
apiVersion: policy/v1beta1
kind: PodDisruptionBudget
metadata:
  name: istiod
  namespace: istio-system
  labels:
    app: istiod
    istio.io/rev: default
    install.operator.istio.io/owning-resource: unknown
    operator.istio.io/component: "Pilot"
    release: istio
    istio: pilot
spec:
  minAvailable: 1
  selector:
    matchLabels:
      app: istiod
      istio: pilot
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: istio-ingressgateway-internal-sds
  namespace: istio-system
  labels:
    release: istio
    istio.io/rev: default
    install.operator.istio.io/owning-resource: unknown
    operator.istio.io/component: "IngressGateways"
rules:
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "watch", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: istio-ingressgateway-sds
  namespace: istio-system
  labels:
    release: istio
    istio.io/rev: default
    install.operator.istio.io/owning-resource: unknown
    operator.istio.io/component: "IngressGateways"
rules:
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "watch", "list"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: istiod-istio-system
  namespace: istio-system
  labels:
    app: istiod
    release: istio
rules:
- apiGroups: ["networking.istio.io"]
  verbs: ["create"]
  resources: ["gateways"]

- apiGroups: [""]
  resources: ["secrets"]
  # TODO lock this down to istio-ca-cert if not using the DNS cert mesh config
  verbs: ["create", "get", "watch", "list", "update", "delete"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: istio-ingressgateway-internal-sds
  namespace: istio-system
  labels:
    release: istio
    istio.io/rev: default
    install.operator.istio.io/owning-resource: unknown
    operator.istio.io/component: "IngressGateways"
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: istio-ingressgateway-internal-sds
subjects:
- kind: ServiceAccount
  name: istio-ingressgateway-internal-service-account
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: istio-ingressgateway-sds
  namespace: istio-system
  labels:
    release: istio
    istio.io/rev: default
    install.operator.istio.io/owning-resource: unknown
    operator.istio.io/component: "IngressGateways"
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: istio-ingressgateway-sds
subjects:
- kind: ServiceAccount
  name: istio-ingressgateway-service-account
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: istiod-istio-system
  namespace: istio-system
  labels:
    app: istiod
    release: istio
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: istiod-istio-system
subjects:
  - kind: ServiceAccount
    name: istiod-service-account
    namespace: istio-system
---
apiVersion: autoscaling/v2beta1
kind: HorizontalPodAutoscaler
metadata:
  labels:
    app: istio-ingressgateway
    install.operator.istio.io/owning-resource: unknown
    istio: ingressgateway
    istio.io/rev: default
    operator.istio.io/component: IngressGateways
    release: istio
  name: istio-ingressgateway
  namespace: istio-system
spec:
  maxReplicas: 5
  metrics:
  - resource:
      name: cpu
      targetAverageUtilization: 80
    type: Resource
  minReplicas: 3
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: istio-ingressgateway
---
apiVersion: autoscaling/v2beta1
kind: HorizontalPodAutoscaler
metadata:
  labels:
    app: istio-ingressgateway
    install.operator.istio.io/owning-resource: unknown
    istio: istio-ingressgateway-internal
    istio.io/rev: default
    operator.istio.io/component: IngressGateways
    release: istio
  name: istio-ingressgateway-internal
  namespace: istio-system
spec:
  maxReplicas: 5
  metrics:
  - resource:
      name: cpu
      targetAverageUtilization: 80
    type: Resource
  minReplicas: 3
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: istio-ingressgateway-internal
---
apiVersion: autoscaling/v2beta1
kind: HorizontalPodAutoscaler
metadata:
  labels:
    app: istiod
    install.operator.istio.io/owning-resource: unknown
    istio.io/rev: default
    operator.istio.io/component: Pilot
    release: istio
  name: istiod
  namespace: istio-system
spec:
  maxReplicas: 5
  metrics:
  - resource:
      name: cpu
      targetAverageUtilization: 80
    type: Resource
  minReplicas: 3
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: istiod
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    service.beta.kubernetes.io/azure-dns-label-name: wapol-wpm-api-prod
    service.beta.kubernetes.io/azure-load-balancer-resource-group: wapol-wpm-rg-prod
  labels:
    app: istio-ingressgateway
    install.operator.istio.io/owning-resource: unknown
    istio: ingressgateway
    istio.io/rev: default
    operator.istio.io/component: IngressGateways
    release: istio
  name: istio-ingressgateway
  namespace: istio-system
spec:
  loadBalancerIP: *************
  ports:
  - name: status-port
    port: 15021
    protocol: TCP
    targetPort: 15021
  - name: http2
    port: 80
    protocol: TCP
    targetPort: 8080
  - name: https
    port: 443
    protocol: TCP
    targetPort: 8443
  - name: tcp-istiod
    port: 15012
    protocol: TCP
    targetPort: 15012
  - name: tls
    port: 15443
    protocol: TCP
    targetPort: 15443
  selector:
    app: istio-ingressgateway
    istio: ingressgateway
  type: LoadBalancer
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    service.beta.kubernetes.io/azure-load-balancer-internal: "true"
  labels:
    app: istio-ingressgateway
    install.operator.istio.io/owning-resource: unknown
    istio: istio-ingressgateway-internal
    istio.io/rev: default
    operator.istio.io/component: IngressGateways
    release: istio
  name: istio-ingressgateway-internal
  namespace: istio-system
spec:
  loadBalancerIP: *************
  ports:
  - name: status-port
    port: 15021
    protocol: TCP
    targetPort: 15021
  - name: http2
    port: 80
    protocol: TCP
    targetPort: 8080
  - name: https
    port: 443
    protocol: TCP
    targetPort: 8443
  - name: tcp-istiod
    port: 15012
    protocol: TCP
    targetPort: 15012
  - name: tls
    port: 15443
    protocol: TCP
    targetPort: 15443
  - name: cadtocaddataadapter-prod
    port: 16717
    protocol: TCP
    targetPort: 16717
  selector:
    app: istio-ingressgateway
    istio: istio-ingressgateway-internal
  type: LoadBalancer
---
apiVersion: v1
kind: Service
metadata:
  name: istiod
  namespace: istio-system
  labels:
    istio.io/rev: default
    install.operator.istio.io/owning-resource: unknown
    operator.istio.io/component: "Pilot"
    app: istiod
    istio: pilot
    release: istio
spec:
  ports:
    - port: 15010
      name: grpc-xds # plaintext
      protocol: TCP
    - port: 15012
      name: https-dns # mTLS with k8s-signed cert
      protocol: TCP
    - port: 443
      name: https-webhook # validation and injection
      targetPort: 15017
      protocol: TCP
    - port: 15014
      name: http-monitoring # prometheus stats
      protocol: TCP
  selector:
    app: istiod
    # Label used by the 'default' service. For versioned deployments we match with app and version.
    # This avoids default deployment picking the canary
    istio: pilot
---
