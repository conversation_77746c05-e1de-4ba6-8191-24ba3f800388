apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  namespace: istio-system
  name: virtualservice-external-core-event-publisher-prod
spec:
  hosts:
    - "wapol-api-prod.pscore.cloud"
  gateways:
    - gateway-external
  http:
    - match:
        - uri:
            prefix: /event-publisher/v1/
      rewrite:
        uri: /
      route:
        - destination:
            host: backend-core-event-publisher-svc-prod.backend-prod.svc.cluster.local
            port:
              number: 80
    - match:
        - uri:
            prefix: /api/v1/event-publisher/v1/
      rewrite:
        uri: /
      route:
        - destination:
            host: backend-core-event-publisher-svc-prod.backend-prod.svc.cluster.local
            port:
              number: 80
