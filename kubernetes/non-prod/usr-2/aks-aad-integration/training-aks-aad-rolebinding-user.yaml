kind: RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: user-full-access-training
  namespace: backend-training
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: user-full-access-training
subjects:
  - kind: Group
    namespace: backend-training
    name: d029f418-fac5-41f3-9d85-7f91604d00cc #change with an appropriate AAD group object ID
---
kind: RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: user-full-access-training
  namespace: backend-integration-training
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: user-full-access-training
subjects:
  - kind: Group
    namespace: backend-training
    name: d029f418-fac5-41f3-9d85-7f91604d00cc #change with an appropriate AAD group object ID

---
kind: RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: user-full-access-training
  namespace: support-services
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: user-full-access-training
subjects:
  - kind: Group
    namespace: support-services
    name: d029f418-fac5-41f3-9d85-7f91604d00cc #change with an appropriate AAD group object ID
