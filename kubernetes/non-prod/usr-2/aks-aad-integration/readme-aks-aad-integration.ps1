#reference sources:
# https://docs.microsoft.com/en-us/azure/aks/azure-ad-integration-cli
# https://docs.microsoft.com/en-us/azure/aks/azure-ad-rbac

#0. Login using an admin account. This account need tenant admin right to consent to AKS AAD integration, also needs AAD admin right to assisng roles to AAD groups.
az login

$aksname = "wapol-wpm-aks-cluster-usr"
$resourceGroup = "wapol-wpm-rg-usr"
$tenantId=$(az account show --query tenantId -o tsv)

#check aad profile is enabled
az aks show -g $resourceGroup -n $aksname

#4. Config AKS RBAC and AAD
#get AKS ID
$AKS_ID=$(az aks show --resource-group $resourceGroup --name $aksname --query id -o tsv)

#If the group exists, get AAD group ID
$AKS_DEV_GROUP=$(az ad group show --group wapol-wpm-aks-developer-usr --query objectId -o tsv)

#Assign AKS access role to the group
az role assignment create --assignee $AKS_DEV_GROUP --role "Azure Kubernetes Service Cluster User Role" --scope $AKS_ID

#4. Allow the AAD admin group to use kubectl
#get AKS ID
$AKS_ID=$(az aks show --resource-group $resourceGroup --name $aksname --query id -o tsv)

#If the group exists, get AAD group ID
$AKS_ADMIN_GROUP=$(az ad group show --group wapol-wpm-aks-admin-usr  --query objectId -o tsv)

#Assign AKS access role to the group
az role assignment create --assignee $AKS_ADMIN_GROUP --role "Azure Kubernetes Service Cluster User Role" --scope $AKS_ID

#5. Create rolebinding, clusterbinding
#Login using buitl-in system admin:
az aks get-credentials --resource-group $resourceGroup --name $aksname

#Change the AAD group ID in rolebinding and clusterrolebinding in the yaml files BEFORE run following command.
kubectl apply -f usr-role-user.yaml
kubectl apply -f usr-rolebinding-user.yaml
kubectl apply -f all-clusterrolebinding-user.yaml

kubectl apply -f all-clusterrolebinding-admin.yaml

#6. Create Azure Devops Service Account
kubectl apply -f all-serviceaccount-azure-devops.yaml
kubectl apply -f all-clusterrole-azure-devops.yaml
kubectl apply -f all-clusterrolebinding-azure-devops.yaml

#7. Create Azure Devops Kubernetes connection using the serviceaccount secret

#Get the secret created for the Service Account created in step 6 and note service account secret name.
Kubectl -n support-services get secrets

#Get the azure devops service account secret
kubectl -n support-services get secret <service-account-secret-name> -o json

#Go to Azure Devops and add the service connection
https://dev.azure.com/msi-cie/Gridstone/_settings/adminservices

#8. Get the kube.config and run some test commands
az aks get-credentials --resource-group $resourceGroup --name $aksname --overwrite-existing

#Get AKS resources
kubectl get nodes
kubectl get namespaces
kubectl get pods -A