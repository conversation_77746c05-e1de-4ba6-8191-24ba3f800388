kind: RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: user-full-access-usr
  namespace: backend-usr
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: user-full-access-usr
subjects:
  - kind: Group
    namespace: backend-usr
    name: d029f418-fac5-41f3-9d85-7f91604d00cc #change with an appropriate AAD group object ID
---
kind: RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: user-full-access-usr
  namespace: backend-integration-usr
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: user-full-access-usr
subjects:
  - kind: Group
    namespace: backend-usr
    name: d029f418-fac5-41f3-9d85-7f91604d00cc #change with an appropriate AAD group object ID

---
kind: RoleBinding
apiVersion: rbac.authorization.k8s.io/v1
metadata:
  name: user-full-access-usr
  namespace: support-services
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: user-full-access-usr
subjects:
  - kind: Group
    namespace: support-services
    name: d029f418-fac5-41f3-9d85-7f91604d00cc #change with an appropriate AAD group object ID
