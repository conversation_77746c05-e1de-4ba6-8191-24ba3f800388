apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  namespace: backend-training
  name: virtualservice-internal-integration-reference-data-updater-training
spec:
  hosts:
    - "wapol-agw-training.pscore.cloud"
    - "wapol-agw-training-2.pscore.cloud"
    - "wapol-api-internal-training.pscore.cloud"
  gateways:
    - gateway-internal
  http:
    - match:
        - uri:
            prefix: /api/v1/referencedata-updater/
      rewrite:
        uri: /
      route:
        - destination:
            host: backend-integration-reference-data-updater-svc-training.backend-training.svc.cluster.local
            port:
              number: 80
    - match:
        - uri:
            prefix: /referencedata-updater/
      rewrite:
        uri: /
      route:
        - destination:
            host: backend-integration-reference-data-updater-svc-training.backend-training.svc.cluster.local
            port:
              number: 80
