apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  namespace: backend-training
  name: virtualservice-internal-core-situational-alert-externalapi-training
spec:
  hosts:
    - "wapol-agw-training.pscore.cloud"
    - "wapol-agw-training-2.pscore.cloud"
    - "wapol-api-internal-training.pscore.cloud"
  gateways:
    - gateway-internal
  http:
    - match:
        - uri:
            prefix: /situational-alert-external-api/v1/
      rewrite:
        uri: /
      route:
        - destination:
            host: backend-core-situational-alert-externalapi-svc-training.backend-training.svc.cluster.local
            port:
              number: 80
