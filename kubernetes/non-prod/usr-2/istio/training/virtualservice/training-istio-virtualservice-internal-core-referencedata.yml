apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  namespace: backend-training
  name: virtualservice-internal-core-reference-data-training
spec:
  hosts:
    - "wapol-agw-training.pscore.cloud"
    - "wapol-agw-training-2.pscore.cloud"
    - "wapol-api-internal-training.pscore.cloud"
  gateways:
    - gateway-internal
  http:
    - match:
        - uri:
            prefix: /api/v1/referencedata/
      rewrite:
        uri: /
      route:
        - destination:
            host: backend-core-reference-data-svc-training.backend-training.svc.cluster.local
            port:
              number: 80
      timeout: 60s
    - match:
        - uri:
            prefix: /referencedata/
      rewrite:
        uri: /
      route:
        - destination:
            host: backend-core-reference-data-svc-training.backend-training.svc.cluster.local
            port:
              number: 80
      timeout: 60s
