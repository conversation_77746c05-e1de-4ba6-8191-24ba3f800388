apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  namespace: backend-training
  name: virtualservice-internal-core-core-proxy-training
spec:
  hosts:
    - "wapol-agw-training.pscore.cloud"
    - "wapol-agw-training-2.pscore.cloud"
  gateways:
    - gateway-internal
  http:
    - match:
        - uri:
            prefix: /api/v1/entity/
        - uri:
            exact: /api/v1/__/endpoints
      route:
        - destination:
            host: backend-core-core-proxy-svc-training.backend-training.svc.cluster.local
            port:
              number: 80
      timeout: 60s
    - match:
        - uri:
            prefix: /entity/
      rewrite:
        uri: /api/v1/entity/
      route:
        - destination:
            host: backend-core-core-proxy-svc-training.backend-training.svc.cluster.local
            port:
              number: 80
      timeout: 60s
