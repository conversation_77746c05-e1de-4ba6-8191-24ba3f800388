apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  namespace: istio-system
  name: virtualservice-external-core-situational-alert-externalapi-training
spec:
  hosts:
    - "wapol-api-training.pscore.cloud"
  gateways:
    - gateway-external
  http:
    - match:
        - uri:
            prefix: /situational-alert-external-api/v1/
      rewrite:
        uri: /
      route:
        - destination:
            host: backend-core-situational-alert-externalapi-svc-training.backend-training.svc.cluster.local
            port:
              number: 80
      corsPolicy:
        allowOrigins:
          - exact: http://localhost:8080
          - exact: https://wapol-api-training.pscore.cloud.pscore.cloud
        allowCredentials: true
        allowMethods:
          - POST
          - GET
          - PUT
          - DELETE
          - PATCH
          - OPTIONS
        allowHeaders:
          - x-device-id
          - x-platform
          - x-session-id
          - x-transaction-id
          - x-user-id
          - keep-alive
          - user-agent
          - content-type
          - authorization
