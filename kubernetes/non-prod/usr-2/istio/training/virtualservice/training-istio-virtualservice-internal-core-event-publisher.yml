apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  namespace: backend-training
  name: virtualservice-internal-core-event-publisher-training
spec:
  hosts:
    - "wapol-agw-training.pscore.cloud"
    - "wapol-agw-training-2.pscore.cloud"
  gateways:
    - gateway-internal
  http:
    - match:
        - uri:
            prefix: /event-publisher/v1/
      rewrite:
        uri: /
      route:
        - destination:
            host: backend-core-event-publisher-svc-training.backend-training.svc.cluster.local
            port:
              number: 80
      timeout: 60s
    - match:
        - uri:
            prefix: /api/v1/event-publisher/v1/
      rewrite:
        uri: /
      route:
        - destination:
            host: backend-core-event-publisher-svc-training.backend-training.svc.cluster.local
            port:
              number: 80
      timeout: 60s
