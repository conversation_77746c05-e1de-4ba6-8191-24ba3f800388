apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  namespace: backend-training
  name: virtualservice-internal-core-reporting-training
spec:
  hosts:
    - "wapol-agw-training.pscore.cloud"
    - "wapol-agw-training-2.pscore.cloud"
    - "wapol-api-internal-training.pscore.cloud"
  gateways:
    - gateway-internal
  http:
    - match:
        - uri:
            prefix: /reporting/v1/anpr/user/connection
      rewrite:
        uri: /anpr/user/connection
      route:
        - destination:
            host: backend-core-reporting-svc-training.backend-training.svc.cluster.local
            port:
              number: 80
