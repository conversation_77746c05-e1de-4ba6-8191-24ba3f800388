apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  namespace: backend-training
  name: virtualservice-internal-core-user-persistence-training
spec:
  hosts:
    - "wapol-agw-training.pscore.cloud"
    - "wapol-agw-training-2.pscore.cloud"
  gateways:
    - gateway-internal
  http:
    - match:
      - uri:
          prefix: /api/v1/persistence/
      route:
        - destination:
            host: backend-core-user-persistence-svc-training.backend-training.svc.cluster.local
            port:
              number: 80
      timeout: 60s
    - match:
      - uri:
          prefix: /persistence/v1/
      rewrite:
        uri: /
      route:
      - destination:
          host: backend-core-user-persistence-svc-training.backend-training.svc.cluster.local
          port:
            number: 80
      timeout: 60s
    - match:
      - uri:
          prefix: /persistence/
      rewrite:
        uri: /
      route:
      - destination:
          host: backend-core-user-persistence-svc-training.backend-training.svc.cluster.local
          port:
            number: 80
      timeout: 60s
