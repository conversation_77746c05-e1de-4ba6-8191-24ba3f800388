apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  namespace: backend-training
  name: gateway-internal
spec:
  selector:
    istio: istio-ingressgateway-internal
  servers:
    - port:
        number: 80
        name: http
        protocol: HTTP
      hosts:
        - "wapol-agw-training.pscore.cloud"
        - "wapol-agw-training-2.pscore.cloud"
        - "wapol-api-internal-training.pscore.cloud"
    - port:
        number: 16716
        name: cadtocaddataadapter-training
        protocol: TCP
      hosts:
        - "*"
