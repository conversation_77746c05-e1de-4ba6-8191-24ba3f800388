apiVersion: "security.istio.io/v1beta1"
kind: "AuthorizationPolicy"
metadata:
  name: authorizationpolicy-internal-to-core-referencedata-training
  namespace: backend-training
spec:
  selector:
    matchLabels:
      app: backend-core-reference-data
  action: ALLOW
  rules:
    - from:
        - source:
            principals:
              [
                "cluster.local/ns/backend-training/sa/backend-integration-reference-data-updater",
                "cluster.local/ns/backend-training/sa/backend-integration-events",
                "cluster.local/ns/backend-training/sa/backend-integration-search",
              ]
