apiVersion: "security.istio.io/v1beta1"
kind: "AuthorizationPolicy"
metadata:
  name: "authorizationpolicy-internal-to-core-cad"
  namespace: backend-training
spec:
  selector:
    matchLabels:
      app: backend-core-cad
  action: ALLOW
  rules:
    - from:
        - source:
            #add a list of service account that wants to access
            principals:
              [
                "cluster.local/ns/backend-training/sa/backend-core-situational-alert-externalapi",
                "cluster.local/ns/backend-training/sa/backend-core-situationalalerts-subscriber",
                "cluster.local/ns/backend-training/sa/backend-integration-cadtocad-data-adapter",
                "cluster.local/ns/backend-integration-training/sa/backend-integration-cad-data-adapter",
              ]
