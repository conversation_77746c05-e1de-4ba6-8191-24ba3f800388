apiVersion: "security.istio.io/v1beta1"
kind: "AuthorizationPolicy"
metadata:
  name: "authorizationpolicy-internal-to-core-push-notification"
  namespace: backend-training
spec:
  selector:
    matchLabels:
      app: backend-push-notification
  action: ALLOW
  rules:
    - from:
        - source:
            #add a list of service account that wants to access
            principals:
              [
                "cluster.local/ns/backend-training/sa/backend-core-cad-for-dequeue",
                "cluster.local/ns/backend-training/sa/backend-core-situational-alert-externalapi",
                "cluster.local/ns/backend-training/sa/backend-core-situationalalerts-subscriber",
              ]
