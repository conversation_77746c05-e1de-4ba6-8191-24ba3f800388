apiVersion: "security.istio.io/v1beta1"
kind: "AuthorizationPolicy"
metadata:
  name: "authorizationpolicy-internal-to-core-situational-alert"
  namespace: backend-training
spec:
  selector:
    matchLabels:
      app: backend-core-situationalalerts
  action: ALLOW
  rules:
    - from:
        - source:
            #add a list of service account that wants to access
            principals:
              [
                "cluster.local/ns/backend-training/sa/backend-core-proxy",
                "cluster.local/ns/backend-training/sa/backend-core-cad",
                "cluster.local/ns/backend-training/sa/backend-core-situationalalerts-subscriber",
                "cluster.local/ns/backend-training/sa/backend-core-situational-alert-externalapi",
              ]
