---
# DestinationRule for Core Reference Data Service in Dev to connect to es1
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: originate-mtls-for-es-1
  namespace: backend-training
spec:
  workloadSelector:
    matchLabels:
      app: backend-core-reference-data
  host: wapol-es-1-internal-usr.pscore.cloud
  trafficPolicy:
    loadBalancer:
      simple: ROUND_ROBIN
    portLevelSettings:
      - port:
          number: 9200
        tls:
          mode: MUTUAL
          credentialName: es-client-credential # this must match the secret created earlier to hold client certs, and works only when DR has a workloadSelector
          sni: wapol-es-1-internal-usr.pscore.cloud
---
# DestinationRule for Core Reference Data Service in Training to connect to es1
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: originate-mtls-for-es-2
  namespace: backend-training
spec:
  workloadSelector:
    matchLabels:
      app: backend-core-reference-data
  host: wapol-es-2-internal-usr.pscore.cloud
  trafficPolicy:
    loadBalancer:
      simple: ROUND_ROBIN
    portLevelSettings:
      - port:
          number: 9200
        tls:
          mode: MUTUAL
          credentialName: es-client-credential # this must match the secret created earlier to hold client certs, and works only when DR has a workloadSelector
          sni: wapol-es-2-internal-usr.pscore.cloud
---
# DestinationRule for Core Reference Data Service in Training to connect to es1
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: originate-mtls-for-es-3
  namespace: backend-training
spec:
  workloadSelector:
    matchLabels:
      app: backend-core-reference-data
  host: wapol-es-3-internal-usr.pscore.cloud
  trafficPolicy:
    loadBalancer:
      simple: ROUND_ROBIN
    portLevelSettings:
      - port:
          number: 9200
        tls:
          mode: MUTUAL
          credentialName: es-client-credential # this must match the secret created earlier to hold client certs, and works only when DR has a workloadSelector
          sni: wapol-es-3-internal-usr.pscore.cloud
---
# DestinationRule for Core Reference Data Service in Dev to connect to es1
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: destinationrule-internal-es-1-core-reporting-training
  namespace: backend-training
spec:
  workloadSelector:
    matchLabels:
      app: backend-core-reporting
  host: wapol-es-1-internal-usr.pscore.cloud
  trafficPolicy:
    loadBalancer:
      simple: ROUND_ROBIN
    portLevelSettings:
      - port:
          number: 9200
        tls:
          mode: MUTUAL
          credentialName: es-client-credential # this must match the secret created earlier to hold client certs, and works only when DR has a workloadSelector
          sni: wapol-es-1-internal-usr.pscore.cloud
---
# DestinationRule for Core Reference Data Service in Training to connect to es1
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: destinationrule-internal-es-2-core-reporting-training
  namespace: backend-training
spec:
  workloadSelector:
    matchLabels:
      app: backend-core-reporting
  host: wapol-es-2-internal-usr.pscore.cloud
  trafficPolicy:
    loadBalancer:
      simple: ROUND_ROBIN
    portLevelSettings:
      - port:
          number: 9200
        tls:
          mode: MUTUAL
          credentialName: es-client-credential # this must match the secret created earlier to hold client certs, and works only when DR has a workloadSelector
          sni: wapol-es-2-internal-usr.pscore.cloud
---
# DestinationRule for Core Reference Data Service in Training to connect to es1
apiVersion: networking.istio.io/v1alpha3
kind: DestinationRule
metadata:
  name: destinationrule-internal-es-3-core-reporting-training
  namespace: backend-training
spec:
  workloadSelector:
    matchLabels:
      app: backend-core-reporting
  host: wapol-es-3-internal-usr.pscore.cloud
  trafficPolicy:
    loadBalancer:
      simple: ROUND_ROBIN
    portLevelSettings:
      - port:
          number: 9200
        tls:
          mode: MUTUAL
          credentialName: es-client-credential # this must match the secret created earlier to hold client certs, and works only when DR has a workloadSelector
          sni: wapol-es-3-internal-usr.pscore.cloud
