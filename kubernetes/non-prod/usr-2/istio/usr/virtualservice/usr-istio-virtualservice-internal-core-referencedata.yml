apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  namespace: backend-usr
  name: virtualservice-internal-core-reference-data-usr
spec:
  hosts:
    - "wapol-agw-usr.pscore.cloud"
    - "wapol-agw-usr-2.pscore.cloud"
    - "wapol-api-internal-usr.pscore.cloud"
  gateways:
    - gateway-internal
  http:
    - match:
        - uri:
            prefix: /api/v1/referencedata/
      rewrite:
        uri: /
      route:
        - destination:
            host: backend-core-reference-data-svc-usr.backend-usr.svc.cluster.local
            port:
              number: 80
      timeout: 60s
    - match:
        - uri:
            prefix: /referencedata/
      rewrite:
        uri: /
      route:
        - destination:
            host: backend-core-reference-data-svc-usr.backend-usr.svc.cluster.local
            port:
              number: 80
      timeout: 60s
