apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  namespace: backend-usr
  name: virtualservice-internal-core-user-persistence-usr
spec:
  hosts:
  - "wapol-agw-usr.pscore.cloud"
  - "wapol-agw-usr-2.pscore.cloud"
  gateways:
  - gateway-internal
  http:
  - match:
    - uri:
        prefix: /api/v1/persistence/
    route:
    - destination:
        host: backend-core-user-persistence-svc-usr.backend-usr.svc.cluster.local
        port:
          number: 80
    timeout: 60s
  - match:
    - uri:
        prefix: /persistence/v1/
    rewrite:
      uri: /
    route:
    - destination:
        host: backend-core-user-persistence-svc-usr.backend-usr.svc.cluster.local
        port:
          number: 80
    timeout: 60s
  - match:
    - uri:
        prefix: /persistence/
    rewrite:
      uri: /
    route:
    - destination:
        host: backend-core-user-persistence-svc-usr.backend-usr.svc.cluster.local
        port:
          number: 80
