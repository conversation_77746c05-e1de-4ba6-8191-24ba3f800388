apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  namespace: istio-system
  name: virtualservice-external-core-event-publisher-usr
spec:
  hosts:
  - "wapol-api-usr.pscore.cloud"
  gateways:
  - gateway-external
  http:
  - match:
    - uri:
        prefix: /event-publisher/v1/
    rewrite:
      uri: /
    route:
    - destination:
        host: backend-core-event-publisher-svc-usr.backend-usr.svc.cluster.local
        port:
          number: 80
    timeout: 60s
  - match:
    - uri:
        prefix: /api/v1/event-publisher/v1/
    rewrite:
      uri: /
    route:
    - destination:
        host: backend-core-event-publisher-svc-usr.backend-usr.svc.cluster.local
        port:
          number: 80
    timeout: 60s