apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  namespace: backend-usr
  name: virtualservice-internal-core-core-proxy-usr
spec:
  hosts:
    - "wapol-agw-usr.pscore.cloud"
    - "wapol-agw-usr-2.pscore.cloud"
  gateways:
    - gateway-internal
  http:
    - match:
        - uri:
            prefix: /api/v1/entity/
        - uri:
            exact: /api/v1/__/endpoints
      route:
        - destination:
            host: backend-core-core-proxy-svc-usr.backend-usr.svc.cluster.local
            port:
              number: 80
      timeout: 60s
    - match:
        - uri:
            prefix: /entity/
      rewrite:
        uri: /api/v1/entity/
      route:
        - destination:
            host: backend-core-core-proxy-svc-usr.backend-usr.svc.cluster.local
            port:
              number: 80
      timeout: 60s
