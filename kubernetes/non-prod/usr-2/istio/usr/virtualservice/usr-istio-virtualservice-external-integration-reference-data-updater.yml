apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  namespace: istio-system
  name: virtualservice-external-integration-reference-data-updater-usr
spec:
  hosts:
    - "wapol-api-usr.pscore.cloud"
  gateways:
    - gateway-external
  http:
    - match:
        - uri:
            prefix: /api/v1/referencedata-updater/
      rewrite:
        uri: /
      route:
        - destination:
            host: backend-integration-reference-data-updater-svc-usr.backend-usr.svc.cluster.local
            port:
              number: 80
    - match:
        - uri:
            prefix: /referencedata-updater/
      rewrite:
        uri: /
      route:
        - destination:
            host: backend-integration-reference-data-updater-svc-usr.backend-usr.svc.cluster.local
            port:
              number: 80
