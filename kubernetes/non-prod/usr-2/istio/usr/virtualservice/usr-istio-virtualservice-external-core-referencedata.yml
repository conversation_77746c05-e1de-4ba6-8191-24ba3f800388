apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  namespace: istio-system
  name: virtualservice-external-core-reference-data-usr
spec:
  hosts:
    - "wapol-api-usr.pscore.cloud"
  gateways:
    - gateway-external
  http:
    - match:
        - uri:
            prefix: /api/v1/referencedata/
      rewrite:
        uri: /
      route:
        - destination:
            host: backend-core-reference-data-svc-usr.backend-usr.svc.cluster.local
            port:
              number: 80
      timeout: 60s
    - match:
        - uri:
            prefix: /referencedata/
      rewrite:
        uri: /
      route:
        - destination:
            host: backend-core-reference-data-svc-usr.backend-usr.svc.cluster.local
            port:
              number: 80
      timeout: 60s
