apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  namespace: backend-usr
  name: virtualservice-internal-core-situational-alert-usr
spec:
  hosts:
    - "wapol-agw-usr.pscore.cloud"
    - "wapol-agw-usr-2.pscore.cloud"
    - "wapol-api-internal-usr.pscore.cloud"
  gateways:
    - gateway-internal
  http:
    - match:
        - uri:
            prefix: /situational-alert/v1/
      rewrite:
        uri: /
      route:
        - destination:
            host: backend-core-situationalalerts-svc-usr.backend-usr.svc.cluster.local
            port:
              number: 80
    - match:
        - uri:
            prefix: /situational-alert/v1/
      rewrite:
        uri: /
      route:
        - destination:
            host: backend-core-situationalalerts-svc-usr.backend-usr.svc.cluster.local
            port:
              number: 80
    - match:
        - uri:
            prefix: /situational-alert/
      rewrite:
        uri: /
      route:
        - destination:
            host: backend-core-situationalalerts-svc-usr.backend-usr.svc.cluster.local
            port:
              number: 80
