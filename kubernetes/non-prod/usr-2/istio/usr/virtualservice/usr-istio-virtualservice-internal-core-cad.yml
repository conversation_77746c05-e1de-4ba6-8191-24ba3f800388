apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  namespace: backend-usr
  name: virtualservice-internal-core-cad-usr
spec:
  hosts:
    - "wapol-agw-usr.pscore.cloud"
    - "wapol-agw-usr-2.pscore.cloud"
  gateways:
    - gateway-internal
  http:
    - match:
        - uri:
            prefix: /cad/
        - uri:
            prefix: /api/v1/cad/
        - uri:
            prefix: /cad/v1/
      rewrite:
        uri: /cad/
      route:
        - destination:
            host: backend-core-cad-svc-usr.backend-usr.svc.cluster.local
            port:
              number: 80
      corsPolicy:
        allowOrigins:
          - exact: http://localhost:8080
          - exact: https://wapol-api-usr.pscore.cloud
        allowCredentials: true
        allowMethods:
          - POST
          - GET
          - PUT
          - DELETE
          - PATCH
          - OPTIONS
        allowHeaders:
          - x-device-id
          - x-platform
          - x-session-id
          - x-transaction-id
          - x-user-id
          - x-access-key
          - keep-alive
          - user-agent
          - content-type
          - authorization
    - match:
        - uri:
            prefix: /cad/
      rewrite:
        uri: /api/v1/cad/
      route:
        - destination:
            host: backend-core-cad-svc-usr.backend-usr.svc.cluster.local
            port:
              number: 80
      corsPolicy:
        allowOrigins:
          - exact: http://localhost:8080
          - exact: https://wapol-api-usr.pscore.cloud
        allowCredentials: true
        allowMethods:
          - POST
          - GET
          - PUT
          - DELETE
          - PATCH
          - OPTIONS
        allowHeaders:
          - x-device-id
          - x-platform
          - x-session-id
          - x-transaction-id
          - x-user-id
          - keep-alive
          - user-agent
          - content-type
          - authorization
