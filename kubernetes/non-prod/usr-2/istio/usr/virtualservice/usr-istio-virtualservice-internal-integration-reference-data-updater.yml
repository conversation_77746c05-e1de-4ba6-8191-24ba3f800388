apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  namespace: backend-usr
  name: virtualservice-internal-integration-reference-data-updater-usr
spec:
  hosts:
    - "wapol-agw-usr.pscore.cloud"
    - "wapol-agw-usr-2.pscore.cloud"
    - "wapol-api-internal-usr.pscore.cloud"
  gateways:
    - gateway-internal
  http:
    - match:
        - uri:
            prefix: /api/v1/referencedata-updater/
      rewrite:
        uri: /
      route:
        - destination:
            host: backend-integration-reference-data-updater-svc-usr.backend-usr.svc.cluster.local
            port:
              number: 80
    - match:
        - uri:
            prefix: /referencedata-updater/
      rewrite:
        uri: /
      route:
        - destination:
            host: backend-integration-reference-data-updater-svc-usr.backend-usr.svc.cluster.local
            port:
              number: 80
