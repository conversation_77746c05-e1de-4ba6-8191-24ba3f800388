apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  namespace: istio-system
  name: virtualservice-external-core-user-persistence-usr
spec:
  hosts:
  - "wapol-api-usr.pscore.cloud"
  gateways:
  - gateway-external
  http:
  - match:
    - uri:
        prefix: /api/v1/persistence/
    route:
    - destination:
        host: backend-core-user-persistence-svc-usr.backend-usr.svc.cluster.local
        port:
          number: 80
    timeout: 60s
  - match:
    - uri:
        prefix: /persistence/v1/
    rewrite:
      uri: /
    route:
    - destination:
        host: backend-core-user-persistence-svc-usr.backend-usr.svc.cluster.local
        port:
          number: 80
    timeout: 60s
  - match:
    - uri:
        prefix: /persistence/
    rewrite:
      uri: /api/v1/persistence/
    route:
    - destination:
        host: backend-core-user-persistence-svc-usr.backend-usr.svc.cluster.local
        port:
          number: 80
    timeout: 60s