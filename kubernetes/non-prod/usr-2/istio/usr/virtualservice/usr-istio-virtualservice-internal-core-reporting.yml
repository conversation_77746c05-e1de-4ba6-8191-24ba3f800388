apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  namespace: backend-usr
  name: virtualservice-internal-core-reporting-usr
spec:
  hosts:
    - "wapol-agw-usr.pscore.cloud"
    - "wapol-agw-usr-2.pscore.cloud"
    - "wapol-api-internal-usr.pscore.cloud"
  gateways:
    - gateway-internal
  http:
    - match:
        - uri:
            prefix: /reporting/v1/anpr/user/connection
      rewrite:
        uri: /anpr/user/connection
      route:
        - destination:
            host: backend-core-reporting-svc-usr.backend-usr.svc.cluster.local
            port:
              number: 80
