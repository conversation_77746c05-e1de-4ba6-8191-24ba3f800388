apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  namespace: istio-system
  name: virtualservice-external-core-core-proxy-usr
spec:
  hosts:
  - "wapol-api-usr.pscore.cloud"
  gateways:
  - gateway-external
  http:
  - match:
    - uri:
        prefix: /api/v1/cad/
    - uri:
        prefix: /api/v1/entity/
    - uri:
        exact: /api/v1/__/endpoints
    route:
    - destination:
        host: backend-core-core-proxy-svc-usr.backend-usr.svc.cluster.local
        port:
          number: 80
    timeout: 60s
  - match:
    - uri:
        prefix: /cad/
    rewrite:
      uri: /api/v1/cad/
    route:
    - destination:
        host: backend-core-core-proxy-svc-usr.backend-usr.svc.cluster.local
        port:
          number: 80
    timeout: 60s
  - match:
    - uri:
        prefix: /entity/
    rewrite:
      uri: /api/v1/entity/
    route:
    - destination:
        host: backend-core-core-proxy-svc-usr.backend-usr.svc.cluster.local
        port:
          number: 80
    timeout: 60s