apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  namespace: backend-usr
  name: virtualservice-internal-integration-cadtocad-data-adapter-usr
spec:
  hosts:
  - "*"
  gateways:
  - gateway-internal
  tcp:
  - match:
    - port: 16717
    route:
    - destination:
        host: backend-integration-cadtocad-data-adapter-svc-usr.backend-usr.svc.cluster.local
        port:
          number: 16717