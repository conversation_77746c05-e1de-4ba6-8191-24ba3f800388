apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  namespace: backend-usr
  name: virtualservice-internal-core-event-publisher-usr
spec:
  hosts:
  - "wapol-agw-usr.pscore.cloud"
  - "wapol-agw-usr-2.pscore.cloud"
  gateways:
  - gateway-internal
  http:
  - match:
    - uri:
        prefix: /event-publisher/v1/
    rewrite:
      uri: /
    route:
    - destination:
        host: backend-core-event-publisher-svc-usr.backend-usr.svc.cluster.local
        port:
          number: 80
    timeout: 60s
  - match:
    - uri:
        prefix: /api/v1/event-publisher/v1/
    rewrite:
      uri: /
    route:
    - destination:
        host: backend-core-event-publisher-svc-usr.backend-usr.svc.cluster.local
        port:
          number: 80
    timeout: 60s
