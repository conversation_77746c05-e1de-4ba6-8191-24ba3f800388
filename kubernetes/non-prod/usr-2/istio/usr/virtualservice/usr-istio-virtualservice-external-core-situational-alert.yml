apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  namespace: istio-system
  name: virtualservice-external-core-situational-alert-usr
spec:
  hosts:
    - "wapol-api-usr.pscore.cloud"
  gateways:
    - gateway-external
  http:
    - match:
        - uri:
            prefix: /api/v1/situational-alert/
      route:
        - destination:
            host: backend-core-situationalalerts-svc-usr.backend-usr.svc.cluster.local
            port:
              number: 80
    - match:
        - uri:
            prefix: /situational-alert/v1/
      rewrite:
        uri: /
      route:
        - destination:
            host: backend-core-situationalalerts-svc-usr.backend-usr.svc.cluster.local
            port:
              number: 80
    - match:
        - uri:
            prefix: /situational-alert/
      rewrite:
        uri: /api/v1/situational-alert/
      route:
        - destination:
            host: backend-core-situationalalerts-svc-usr.backend-usr.svc.cluster.local
            port:
              number: 80
