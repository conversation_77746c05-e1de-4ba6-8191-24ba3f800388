apiVersion: networking.istio.io/v1alpha3
kind: VirtualService
metadata:
  namespace: backend-usr
  name: virtualservice-internal-core-situational-alert-externalapi-usr
spec:
  hosts:
    - "wapol-agw-usr.pscore.cloud"
    - "wapol-agw-usr-2.pscore.cloud"
    - "wapol-api-internal-usr.pscore.cloud"
  gateways:
    - gateway-internal
  http:
    - match:
        - uri:
            prefix: /situational-alert-external-api/v1/
      rewrite:
        uri: /
      route:
        - destination:
            host: backend-core-situational-alert-externalapi-svc-usr.backend-usr.svc.cluster.local
            port:
              number: 80
