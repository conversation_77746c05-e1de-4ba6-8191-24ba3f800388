apiVersion: "security.istio.io/v1beta1"
kind: "AuthorizationPolicy"
metadata:
  name: "authorizationpolicy-internal-to-core-referencedata"
  namespace: backend-usr
spec:
  selector:
    matchLabels:
      app: backend-core-reference-data
  action: ALLOW
  rules:
    - from:
        - source:
            principals:
              [
                "cluster.local/ns/backend-usr/sa/backend-integration-reference-data-updater",
                "cluster.local/ns/backend-usr/sa/backend-integration-events",
                "cluster.local/ns/backend-usr/sa/backend-integration-search",
              ]
