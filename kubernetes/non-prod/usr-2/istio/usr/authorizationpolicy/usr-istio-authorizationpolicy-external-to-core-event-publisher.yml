apiVersion: security.istio.io/v1beta1
kind: RequestAuthentication
metadata:
  name: requestauthentication-external-to-backend-core-event-publisher
  namespace: backend-usr
spec:
  selector:
    matchLabels:
      app: backend-core-event-publisher
  jwtRules:
    - issuer: "https://login.microsoftonline.com/224550a4-7c3a-4e2e-842e-82e17f1b40a4/v2.0"
      jwksUri: "https://login.microsoftonline.com/common/discovery/v2.0/keys"
      forwardOriginalToken: true
      fromHeaders:
        - name: authorization
          prefix: "bearer "
        - name: Authorization
          prefix: "bearer "
---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: authorizationpolicy-external-to-core-event-publisher
  namespace: backend-usr
spec:
  selector:
    matchLabels:
      app: backend-core-event-publisher
  action: ALLOW
  rules:
    - from:
        - source:
            requestPrincipals:
              [
                "https://login.microsoftonline.com/224550a4-7c3a-4e2e-842e-82e17f1b40a4/v2.0/*",
              ]
