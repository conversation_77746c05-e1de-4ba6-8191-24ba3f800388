apiVersion: "security.istio.io/v1beta1"
kind: "AuthorizationPolicy"
metadata:
  name: "authorizationpolicy-internal-to-core-user-persistence"
  namespace: backend-usr
spec:
  selector:
    matchLabels:
      app: backend-core-user-persistence
  action: ALLOW
  rules:
    - from:
        - source:
            #add a list of service account that wants to access
            principals:
              [
                "cluster.local/ns/backend-usr/sa/backend-core-proxy",
                "cluster.local/ns/backend-usr/sa/backend-core-cad",
              ]
