apiVersion: security.istio.io/v1beta1
kind: RequestAuthentication
metadata:
  name: requestauthentication-external-to-core-reference-data
  namespace: backend-usr
spec:
  selector:
    matchLabels:
      app: backend-core-reference-data
  jwtRules:
    - issuer: "https://login.microsoftonline.com/224550a4-7c3a-4e2e-842e-82e17f1b40a4/v2.0"
      jwksUri: "https://login.microsoftonline.com/common/discovery/v2.0/keys"
      forwardOriginalToken: true
      fromHeaders:
        - name: authorization
          prefix: "bearer "
        - name: Authorization
          prefix: "bearer "
---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: authorizationpolicy-external-to-core-reference-data
  namespace: backend-usr
spec:
  selector:
    matchLabels:
      app: backend-core-reference-data
  action: ALLOW
  rules:
    - from:
        - source:
            requestPrincipals:
              [
                "https://login.microsoftonline.com/224550a4-7c3a-4e2e-842e-82e17f1b40a4/v2.0/*",
              ]
    - to:
        - operation:
            methods: ["POST"]
            paths: ["/import/import*"]
      when:
        - key: request.headers[host]
          values: ["wapol-api-internal-usr.pscore.cloud"]
