apiVersion: "security.istio.io/v1beta1"
kind: "AuthorizationPolicy"
metadata:
  name: "authorizationpolicy-internal-to-core-cad"
  namespace: backend-usr
spec:
  selector:
    matchLabels:
      app: backend-core-cad
  action: ALLOW
  rules:
    - from:
        - source:
            #add a list of service account that wants to access
            principals:
              [
                "cluster.local/ns/backend-usr/sa/backend-core-situational-alert-externalapi",
                "cluster.local/ns/backend-usr/sa/backend-core-situationalalerts-subscriber",
                "cluster.local/ns/backend-usr/sa/backend-core-push-notification",
                "cluster.local/ns/backend-usr/sa/backend-integration-cadtocad-data-adapter",
                "cluster.local/ns/backend-integration-usr/sa/backend-integration-cad-data-adapter",
              ]
