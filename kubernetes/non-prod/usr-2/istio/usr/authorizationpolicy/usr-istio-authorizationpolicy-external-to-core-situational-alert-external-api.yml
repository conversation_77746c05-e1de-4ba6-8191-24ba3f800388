apiVersion: security.istio.io/v1beta1
kind: RequestAuthentication
metadata:
  name: requestauthentication-external-to-core-situational-alert-external-api
  namespace: backend-usr
spec:
  selector:
    matchLabels:
      app: backend-core-situational-alert-externalapi
  jwtRules:
    - issuer: "https://login.microsoftonline.com/224550a4-7c3a-4e2e-842e-82e17f1b40a4/v2.0"
      jwksUri: "https://login.microsoftonline.com/common/discovery/v2.0/keys"
      forwardOriginalToken: true
      fromHeaders:
        - name: authorization
          prefix: "bearer "
        - name: Authorization
          prefix: "bearer "
        - name: Authorization
          prefix: "Bearer "
    - issuer: "https://sts.windows.net/988004e3-b844-4b61-a48e-4fa2e005ab70/"
      jwksUri: "https://login.microsoftonline.com/988004e3-b844-4b61-a48e-4fa2e005ab70/discovery/v2.0/keys"
      forwardOriginalToken: true
      fromHeaders:
        - name: authorization
          prefix: "bearer "
        - name: Authorization
          prefix: "bearer "
        - name: Authorization
          prefix: "Bearer "
---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: authorizationpolicy-external-to-core-situational-alert-externalapi
  namespace: backend-usr
spec:
  selector:
    matchLabels:
      app: backend-core-situational-alert-externalapi
  action: ALLOW
  rules:
    - from:
        - source:
            requestPrincipals:
              [
                "https://login.microsoftonline.com/224550a4-7c3a-4e2e-842e-82e17f1b40a4/v2.0/*",
              ]
    - from:
      to:
        - operation:
            methods: ["POST"]
            paths: ["/external"]
      when:
        - key: request.auth.claims[iss]
          values:
            - "https://login.microsoftonline.com/224550a4-7c3a-4e2e-842e-82e17f1b40a4/v2.0"
            - "https://sts.windows.net/988004e3-b844-4b61-a48e-4fa2e005ab70/*"
