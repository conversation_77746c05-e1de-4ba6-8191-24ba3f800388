apiVersion: networking.istio.io/v1alpha3
kind: Gateway
metadata:
  namespace: backend-usr
  name: gateway-internal
spec:
  selector:
    istio: istio-ingressgateway-internal
  servers:
  - port:
      number: 80
      name: http
      protocol: HTTP
    hosts:
    - "wapol-agw-usr.pscore.cloud"
    - "wapol-agw-usr-2.pscore.cloud"
    - "wapol-api-internal-usr.pscore.cloud"
  - port:
      number: 16717
      name: cadtocaddataadapter-usr
      protocol: TCP
    hosts:
    - "*"
