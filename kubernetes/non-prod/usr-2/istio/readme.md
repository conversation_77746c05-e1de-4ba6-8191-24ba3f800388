Follow this guideline to install istio onto the AKS. There are a couple of options:

- To install from scratch (no istio has been installed yet), perform all steps from step 1.
- To upgrade istio version, perform steps 1 to 5.
- To update istio configs, bypass steps 2, 3, 4, 5 and 6, just apply the manifest files, eg in step 7, 8, 9 or 10.

1. Switch Kube context to the cluster

   ```
   kubectl config use-context xxxxxx
   ```

2. create istio-system namespace

   ```
   kubectl create ns istio-system
   ```

3. generate deployment manifest which can be committed to Github and show what exaclty will be installed. (optional)

   ```
   istioctl manifest generate -f all-istio-based-components-deployment.yml > all-istio-based-components-deployment-manifest.yml
   ```

4. Put with a revision label(the version of istio to be installed or upgraded to) on namespaces for istio to automatically inject envoy sidecar

   ```
   kubectl label namespace aaa-services istio.io/rev=1-25-1
   kubectl label namespace backend-usr istio.io/rev=1-25-1
   kubectl label namespace backend-training istio.io/rev=1-25-1
   kubectl label namespace backend-integration-usr istio.io/rev=1-25-1
   kubectl label namespace backend-integration-training istio.io/rev=1-25-1
   ```

5. create secret provider

   ```
   apply -f kubernetes/non-prod/usr-2/secret-store-csi-driver/backend-secret-store.yml
   ```
6. run istio-auto-updater.sh to install istio

   refer to the instructions here: https://github.com/Gridstone/devops-template/wiki/istio-auto-updater

   ```
   istio-auto-updater.sh
   ```

7. create tls secret for gateway external

   ```
   kubectl create secret tls aks-ingress-tls -n istio-system --key pscore.cloud.key --cert pscore.cloud.2022.crt
   ```

8. create an istio gateway to route backend traffic

   ```
   kubectl apply -f usr-istio-gateway-external.yml
   kubectl apply -f usr-istio-gateway-internal.yml
   kubectl apply -f training-istio-gateway-internal.yml
   ```

9. create virtual service to route backend traffic

   ```
   kubectl apply -f usr-istio-virtualservice-external-core-core-proxy.yml
   kubectl apply -f usr-istio-virtualservice-external-core-referencedata.yml
   kubectl apply -f usr-istio-virtualservice-external-core-user-persistence.yml
   kubectl apply -f usr-istio-virtualservice-external-core-event-publisher.yml
   kubectl apply -f usr-istio-virtualservice-external-core-situational-alert.yml
   kubectl apply -f usr-istio-virtualservice-external-core-situational-alert-external-api.yml

   kubectl apply -f usr-istio-virtualservice-internal-core-core-proxy.yml
   kubectl apply -f usr-istio-virtualservice-internal-core-event-publisher.yml
   kubectl apply -f usr-istio-virtualservice-internal-core-referencedata.yml
   kubectl apply -f usr-istio-virtualservice-internal-core-user-persistence.yml
   kubectl apply -f usr-istio-virtualservice-internal-integration-cadtocad-data-adapter.yml
   kubectl apply -f usr-istio-virtualservice-internal-integration-reference-data-updater.yml
   kubectl apply -f usr-istio-virtualservice-internal-core-situational-alert.yml
   kubectl apply -f usr-istio-virtualservice-internal-core-situational-alert-external-api.yml

   kubectl apply -f training-istio-virtualservice-internal-core-core-proxy.yml
   kubectl apply -f training-istio-virtualservice-internal-core-event-publisher.yml
   kubectl apply -f training-istio-virtualservice-internal-core-referencedata.yml
   kubectl apply -f training-istio-virtualservice-internal-core-user-persistence.yml
   kubectl apply -f training-istio-virtualservice-internal-integration-cadtocad-data-adapter.yml
   kubectl apply -f training-istio-virtualservice-internal-integration-reference-data-updater.yml
   ```

10. create security (authentication and authorization) policy

   request access (from external)
   #kubectl apply -f usr-istio-authorizationpolicy-allow-nothing.yml

   ```
   kubectl apply -f usr-istio-authorizationpolicy-external-to-core-cad.yml
   kubectl apply -f usr-istio-authorizationpolicy-external-to-core-core-proxy.yml
   kubectl apply -f usr-istio-authorizationpolicy-external-to-core-event-publisher.yml
   kubectl apply -f usr-istio-authorizationpolicy-external-to-core-referencedata.yml
   kubectl apply -f usr-istio-authorizationpolicy-external-to-core-situational-alert.yml
   kubectl apply -f usr-istio-authorizationpolicy-external-to-core-situational-alert-external-api.yml
   kubectl apply -f usr-istio-authorizationpolicy-external-to-core-user-persistence.yml
   kubectl apply -f usr-istio-authorizationpolicy-external-to-integration-reference-data-updater.yml

   kubectl apply -f training-istio-authorizationpolicy-external-to-core-cad.yml
   kubectl apply -f training-istio-authorizationpolicy-external-to-core-core-proxy.yml
   kubectl apply -f training-istio-authorizationpolicy-external-to-core-event-publisher.yml
   kubectl apply -f training-istio-authorizationpolicy-external-to-core-referencedata.yml
   kubectl apply -f training-istio-authorizationpolicy-external-to-core-situational-alert.yml
   kubectl apply -f training-istio-authorizationpolicy-external-to-core-situational-alert-external-api.yml
   kubectl apply -f training-istio-authorizationpolicy-external-to-core-user-persistence.yml
   kubectl apply -f training-istio-authorizationpolicy-external-to-integration-reference-data-updater.yml
   ```

   request access (from internal)

   ```
   kubectl apply -f usr-istio-authorizationpolicy-internal-to-core-cad.yml
   kubectl apply -f usr-istio-authorizationpolicy-internal-to-core-referencedata.yml
   kubectl apply -f usr-istio-authorizationpolicy-internal-to-core-push-notification.yml
   kubectl apply -f usr-istio-authorizationpolicy-internal-to-core-situational-alert.yml
   kubectl apply -f usr-istio-authorizationpolicy-internal-to-core-situational-alert-external-api.yml
   kubectl apply -f usr-istio-authorizationpolicy-internal-to-core-user-persistence.yml
   kubectl apply -f usr-istio-authorizationpolicy-internal-to-integration-reference-data-updater.yml


   kubectl apply -f training-istio-authorizationpolicy-internal-to-core-cad.yml
   kubectl apply -f training-istio-authorizationpolicy-internal-to-core-referencedata.yml
   kubectl apply -f training-istio-authorizationpolicy-internal-to-core-push-notification.yml
   kubectl apply -f training-istio-authorizationpolicy-internal-to-core-situational-alert.yml
   kubectl apply -f training-istio-authorizationpolicy-internal-to-core-situational-alert-external-api.yml
   kubectl apply -f training-istio-authorizationpolicy-internal-to-core-user-persistence.yml
   kubectl apply -f training-istio-authorizationpolicy-internal-to-integration-reference-data-updater.yml
   ```

11. create envoyfilter

```
kubectl apply -f usr-istio-envoy-filter-ingressgateway.yml
kubectl apply -f usr-istio-envoy-filter-ingressgateway-internal.yml
```

12. create serviceentry and destinationrule for traffic to ES via TLS

    ```
    kubectl apply -f usr-istio-serviceentry-internal-es.yml
    kubectl apply -f training-istio-serviceentry-internal-es.yml
    kubectl apply -f usr-istio-destinationrule-internal-es.yml
    kubectl apply -f training-istio-destinationrule-internal-es.yml
    ```

13. NN 5/5/21 not apply yet
    #peer access
    $kubectl apply -f usr-istio-authorizationpolicy-internal-to-core-cad.yml
$kubectl apply -f usr-istio-authorizationpolicy-internal-to-core-push-notification.yml
    $kubectl apply -f usr-istio-authorizationpolicy-internal-to-integration-cad-queries.yml
$kubectl apply -f usr-istio-authorizationpolicy-internal-to-integration-cad-data-adapter.yml
