#!/bin/bash
# Define the root folder (assuming it's "dev").
ROOT_FOLDER="training"
# Create an output folder to store diff files.
OUTPUT_FOLDER="diff_output_$ROOT_FOLDER"
# Create the output folder if it doesn't exist.
mkdir -p "$OUTPUT_FOLDER"
# Loop through subdirectories under the root folder.
for subfolder in "$ROOT_FOLDER"/*; do
    # Check if it's a directory.
    if [ -d "$subfolder" ]; then
        # Get the name of the environment based on the folder name.
        ENV_NAME=$(basename "$subfolder")
        # Initialize the diff output file for this environment.
        DIFF_FILE="$OUTPUT_FOLDER/${ENV_NAME}_diff.log"
        echo "Generating diffs for environment: $ENV_NAME" >"$DIFF_FILE"
        # Loop through each YAML file in the subfolder.
        for file in "$subfolder"/*.yml; do
            # Check if the file exists (handle the case where no YAML files are present).
            if [ -f "$file" ]; then
                echo "Diff for $file:" >>"$DIFF_FILE"
                # Append the diff output to the diff file.
                kubectl diff -f "$file" >>"$DIFF_FILE"
                echo "" >>"$DIFF_FILE" # Add a blank line for readability.
            else
                echo "No YAML files found in $subfolder." >>"$DIFF_FILE"
            fi
        done
        echo "Diff file created at: $DIFF_FILE" # Confirmation message.
    fi
done
