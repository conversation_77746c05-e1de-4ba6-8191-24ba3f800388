apiVersion: networking.istio.io/v1alpha3
kind: EnvoyFilter
metadata:
  name: envoyfilter-ingressgateway-internal
  namespace: istio-system
spec:
  workloadSelector:
    labels:
      app: istio-ingressgateway-internal
  configPatches:
    - applyTo: HTTP_FILTER
      match:
        context: GATEWAY
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "envoy.filters.http.router"
      patch:
        operation: INSERT_BEFORE
        value:
          name: envoy.filters.http.compressor
          typed_config:
            "@type": type.googleapis.com/envoy.extensions.filters.http.compressor.v3.Compressor
            remove_accept_encoding_header: true
            compressor_library:
              name: text_optimized
              typed_config:
                "@type": type.googleapis.com/envoy.extensions.compression.gzip.compressor.v3.Gzip
    - applyTo: HTTP_FILTER
      match:
        context: GATEWAY
        listener:
          filterChain:
            filter:
              name: "envoy.filters.network.http_connection_manager"
              subFilter:
                name: "envoy.filters.http.router"
      patch:
        operation: INSERT_BEFORE
        value:
          name: envoy.filters.http.decompressor
          typed_config:
            "@type": type.googleapis.com/envoy.extensions.filters.http.decompressor.v3.Decompressor
            decompressor_library:
              name: decompress_client
              typed_config:
                "@type": "type.googleapis.com/envoy.extensions.compression.gzip.decompressor.v3.Gzip"
            # disable for response decompression
            response_direction_config:
              common_config:
                enabled:
                  default_value: false
                  runtime_key: response_decompressor_enabled
