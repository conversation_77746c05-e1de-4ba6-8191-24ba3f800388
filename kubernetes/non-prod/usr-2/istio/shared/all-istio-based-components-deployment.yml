apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
spec:
  profile: default
  tag: 1.25.1
  revision: 1-25-1
  meshConfig:
    accessLogFile: /dev/stdout
    #accessLogFormat: "[%START_TIME%] \"%REQ(:METHOD)% %REQ(X-ENVOY-ORIGINAL-PATH?:PATH)% %PROTOCOL%\" %RESPONSE_CODE% %RESPONSE_FLAGS% %RESPONSE_CODE_DETAILS% %CONNECTION_TERMINATION_DETAILS% \"%UPSTREAM_TRANSPORT_FAILURE_REASON%\" %BYTES_RECEIVED% %BYTES_SENT% %DURATION% %RESP(X-ENVOY-UPSTREAM-SERVICE-TIME)% \"%REQ(X-FORWARDED-FOR)%\" \"%REQ(USER-AGENT)%\" \"%REQ(X-REQUEST-ID)%\" \"%REQ(:AUTHORITY)%\" \"%UPSTREAM_HOST%\" %UPSTREAM_CLUSTER% %UPSTREAM_LOCAL_ADDRESS% %DOWNSTREAM_LOCAL_ADDRESS% %DOWNSTREAM_REMOTE_ADDRESS% %REQUESTED_SERVER_NAME% %ROUTE_NAME% %REQ(X-USER-ID)% %REQ(X-DEVICE-TYPE)% \n"
    accessLogFormat: |
      { "start_time" :"%START_TIME%",
        "method": "%REQ(:METHOD)%",
        "path": "%REQ(X-ENVOY-ORIGINAL-PATH?:PATH)%",
        "protocol": "%PROTOCOL%",
        "response_code": "%RESPONSE_CODE%",
        "response_flags": "%RESPONSE_FLAGS%",
        "response_code_details": "%RESPONSE_CODE_DETAILS%",
        "connection_termination_details": "%CONNECTION_TERMINATION_DETAILS%",
        "upstream_transport_failure_reason": "%UPSTREAM_TRANSPORT_FAILURE_REASON%",
        "bytes_received": "%BYTES_RECEIVED%",
        "bytes_sent":"%BYTES_SENT%",
        "duration": "%DURATION%",
        "upstream_service_time": "%REQ(x-envoy-upstream-service-time)%",
        "x_forwarded_for": "%REQ(X-FORWARDED-FOR)%",
        "user_agent": "%REQ(USER-AGENT)%",
        "request_id": "%REQ(X-REQUEST-ID)%",
        "authority": "%REQ(:AUTHORITY)%",
        "upstream_host": "%UPSTREAM_HOST%",
        "upstream_cluster": "%UPSTREAM_CLUSTER%",
        "upstream_local_address": "%UPSTREAM_LOCAL_ADDRESS%",
        "downstream_local_address": "%DOWNSTREAM_LOCAL_ADDRESS%",
        "downstream_remote_address": "%DOWNSTREAM_REMOTE_ADDRESS%",
        "requested_server_name": "%REQUESTED_SERVER_NAME%",
        "route_name": "%ROUTE_NAME%",
        "user_id": "%REQ(X-USER-ID)%",
        "pscore_request_id": "%REQ(REQUEST-ID)%",
        "device_id": "%REQ(X-DEVICE-ID)%",
        "device_type": "%REQ(X-DEVICE-TYPE)%",
        "traceId": "%REQ(x-b3-traceid)%",
        "istio_policy_status": "-"
      }
    accessLogEncoding: JSON
    enableTracing: true
  components:
    pilot:
      namespace: istio-system
      enabled: true
      k8s:
        #podAnnotations:
        #  sidecar.istio.io/componentLogLevel: debug
        resources:
          requests:
            cpu: 100m # override from default 500m
            memory: 100Mi # ... default 2048Mi
          limits:
            cpu: 1000m # override from default 500m
            memory: 4096Mi # ... default 2048Mi
        hpaSpec:
          maxReplicas: 5 # ... default 5
          minReplicas: 3 # ... default 1
        overlays:
          - kind: Deployment
            name: istiod-1-25-1
            patches:
              - path: spec.template.spec.containers.[name:discovery].args.[9]
                value: "--log_as_json" # add to last item
    ingressGateways:
      - name: istio-ingressgateway
        namespace: istio-system
        enabled: true
        k8s:
          #podAnnotations:
          #  sidecar.istio.io/componentLogLevel: info
          serviceAnnotations:
            service.beta.kubernetes.io/azure-load-balancer-resource-group: wapol-wpm-rg-2-usr
            service.beta.kubernetes.io/azure-dns-label-name: wapol-wpm-api-usr-2
          service:
            loadBalancerIP: ************
          resources:
            requests:
              cpu: 100m # override from default 100m
              memory: 128Mi # ... default 128Mi
            limits:
              cpu: 1000m # override from default 2000m
              memory: 1024Mi # ... default 1024Mi
          hpaSpec:
            maxReplicas: 3 # ... default 5
            minReplicas: 2 # ... default 1
          overlays:
            - kind: Deployment
              name: istio-ingressgateway
              patches:
                - path: spec.template.spec.containers.[name:istio-proxy].args.[9]
                  value: "--log_as_json"
                - path: spec.template.spec.containers.[name:istio-proxy].volumeMounts[9]
                  value:
                    name: secrets-store01-inline
                    mountPath: "/mnt/secrets-store"
                    readOnly: true
                - path: spec.template.spec.volumes[9]
                  value:
                    name: secrets-store01-inline
                    csi:
                      driver: secrets-store.csi.k8s.io
                      readOnly: true
                      volumeAttributes:
                        secretProviderClass: "azure-wapol-kv-secrets-provider"
      - name: istio-ingressgateway-internal
        namespace: istio-system
        enabled: true
        label:
          istio: istio-ingressgateway-internal
        k8s:
          #podAnnotations:
          #  sidecar.istio.io/componentLogLevel: info
          serviceAnnotations:
            service.beta.kubernetes.io/azure-load-balancer-internal: "true"
          service:
            loadBalancerIP: ************* #available ip from the AKS subnet
            ports:
              ##default tcp ports
              - name: status-port
                port: 15021
                protocol: TCP
                targetPort: 15021
              - name: http2
                port: 80
                protocol: TCP
                targetPort: 8080
              - name: https
                port: 443
                protocol: TCP
                targetPort: 8443
              - name: tcp-istiod
                port: 15012
                protocol: TCP
                targetPort: 15012
              - name: tls
                port: 15443
                protocol: TCP
                targetPort: 15443
              ##custom tcp ports
              - name: cadtocaddataadapter-usr
                port: 16717
                protocol: TCP
                targetPort: 16717
              - name: cadtocaddataadapter-training
                port: 16716
                protocol: TCP
                targetPort: 16716
          resources:
            requests:
              cpu: 100m # override from default 100m
              memory: 128Mi # ... default 128Mi
            limits:
              cpu: 1000m # override from default 2000m
              memory: 1024Mi # ... default 1024Mi
          hpaSpec:
            maxReplicas: 5 # ... default 5
            minReplicas: 3 # ... default 1
          overlays:
            - kind: Deployment
              name: istio-ingressgateway-internal
              patches:
                - path: spec.template.spec.containers.[name:istio-proxy].args.[9]
                  value: "--log_as_json"
      - name: istio-egressgateway
        namespace: istio-system
        enabled: false
