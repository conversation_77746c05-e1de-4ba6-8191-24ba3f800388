apiVersion: v1
kind: Namespace
metadata:
  name: backend-usr
  labels:
    tier: backend
    environment: usr
---
apiVersion: v1
kind: Namespace
metadata:
  name: support-services
  labels:
    tier: backend
    environment: usr
---
apiVersion: v1
kind: Namespace
metadata:
  name: aaa-services
  labels:
    tier: backend
    environment: usr
---
apiVersion: v1
kind: Namespace
metadata:
  name: monitoring-services
  labels:
    tier: backend
    environment: dev
---
apiVersion: v1
kind: Namespace
metadata:
  name: backend-training
---
apiVersion: v1
kind: Namespace
metadata:
  name: backend-integration-usr
---
apiVersion: v1
kind: Namespace
metadata:
  name: backend-integration-training
