apiVersion: secrets-store.csi.x-k8s.io/v1
kind: SecretProviderClass
metadata:
  name: azure-wapol-kv-secrets-provider
  namespace: istio-system
spec:
  provider: azure
  parameters:
    usePodIdentity: "false"
    useVMManagedIdentity: "true" # Set to true for using managed identity
    userAssignedIdentityID: 90ec3937-0128-4517-9ab7-af7ff1b08551 # Set the clientID of the user-assigned managed identity to use
    keyvaultName: wapol-wpm-kv-usr # Set to the name of your key vault
    cloudName: "" # [OPTIONAL for Azure] if not provided, the Azure environment defaults to AzurePublicCloud
    objects: |
      array:
        - |
          objectName: pscoreCloudCrt
          objectType: secret              # object types: secret, key, or cert (converted to secret to retrieve key/cert separately)
    tenantId: 988004e3-b844-4b61-a48e-4fa2e005ab70 # The tenant ID of the key vault
  secretObjects: # secretObjects defines the desired state of synced K8s secret objects
    - secretName: aks-ingress-tls
      type: kubernetes.io/tls
      data:
        - objectName: pscoreCloudCrt
          key: tls.key
        - objectName: pscoreCloudCrt
          key: tls.crt
