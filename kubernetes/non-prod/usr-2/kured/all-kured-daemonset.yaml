---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: kured
rules:
  # Allow kured to read spec.unschedulable
  # Allow kubectl to drain/uncordon
  #
  # NB: These permissions are tightly coupled to the bundled version of kubectl; the ones below
  # match https://github.com/kubernetes/kubernetes/blob/v1.19.4/staging/src/k8s.io/kubectl/pkg/cmd/drain/drain.go
  #
  - apiGroups: [""]
    resources: ["nodes"]
    verbs: ["get", "patch"]
  - apiGroups: [""]
    resources: ["pods"]
    verbs: ["list", "delete", "get"]
  - apiGroups: ["apps"]
    resources: ["daemonsets"]
    verbs: ["get"]
  - apiGroups: [""]
    resources: ["pods/eviction"]
    verbs: ["create"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: kured
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: kured
subjects:
  - kind: ServiceAccount
    name: kured
    namespace: kube-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  namespace: kube-system
  name: kured
rules:
  # Allow kured to lock/unlock itself
  - apiGroups: ["apps"]
    resources: ["daemonsets"]
    resourceNames: ["kured"]
    verbs: ["update"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  namespace: kube-system
  name: kured
subjects:
  - kind: ServiceAccount
    namespace: kube-system
    name: kured
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: kured
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: kured
  namespace: kube-system
---
apiVersion: apps/v1
kind: DaemonSet
metadata:
  name: kured # Must match `--ds-name`
  namespace: kube-system # Must match `--ds-namespace`
spec:
  selector:
    matchLabels:
      name: kured
  updateStrategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        name: kured
    spec:
      serviceAccountName: kured
      tolerations:
        - key: node-role.kubernetes.io/control-plane
          effect: NoSchedule
        - key: node-role.kubernetes.io/master
          effect: NoSchedule
      hostPID: true # Facilitate entering the host mount namespace via init
      restartPolicy: Always
      containers:
        - name: kured
          image:
            ghcr.io/kubereboot/kured:1.12.2
            # If you find yourself here wondering why there is no
            # :latest tag on Docker Hub,see the FAQ in the README
          imagePullPolicy: IfNotPresent
          securityContext:
            privileged: true # Give permission to nsenter /proc/1/ns/mnt
          env:
            # Pass in the name of the node on which this pod is scheduled
            # for use with drain/uncordon operations and lock acquisition
            - name: KURED_NODE_ID
              valueFrom:
                fieldRef:
                  fieldPath: spec.nodeName
          command:
            - /usr/bin/kured
              #sentinel check period (default 1h0m0s)
            - --period=5m
            # string slack hook URL for reboot notifications [deprecated in favor of --notify-url]
            - --notify-url="slack://*****************************************************@opsgenie-wapol/"
              # timeout after which the drain is aborted (default: 0, infinite time)
            - --drain-timeout=5m
              # expire lock annotation after this duration (default: 0, disabled)
            - --lock-ttl=10m
            - --lock-release-delay=1m
              # namespace containing daemonset
            #- --ds-namespace=kube-system-maintenance

            #       - --alert-filter-regexp=^RebootRequired$  # alert names to ignore when checking for active alerts
            #       - --alert-firing-only=false               # only consider firing alerts when checking for active alerts
            #       - --annotate-nodes=false                   # if set, the annotations 'weave.works/kured-reboot-in-progress' and 'weave.works/kured-most-recent-reboot-needed' will be given to nodes undergoing kured reboots
            #       - --blocking-pod-selector=runtime=long,cost=expensive     # label selector identifying pods whose presence should prevent reboots
            #       - --blocking-pod-selector=name=temperamental
            #       - --drain-grace-period=-1               #time in seconds given to each pod to terminate gracefully, if negative, the default value specified in the pod will be used (default -1)
            #       - --drain-timeout=0                     #timeout after which the drain is aborted (default: 0, infinite time)
            #       - --ds-name=kured                       #name of daemonset on which to place lock (default "kured")
            #       - --force-reboot=false                  #force a reboot even if the drain fails or times out
            #       - --lock-annotation=weave.works/kured-node-lock  #annotation in which to record locking node (default "weave.works/kured-node-lock")
            #       - --lock-release-delay=30m              # delay lock release for this duration (default: 0, disabled)
            #       - --lock-ttl=0                          # expire lock annotation after this duration (default: 0, disabled)
            #       - --log-format=text                     # use text or json log format (default "text")
            #       - --message-template-drain=Draining node %s        #  message template used to notify about a node being drained (default "Draining node %s")
            #       - --message-template-reboot=Rebooting node %s      # message template used to notify about a node being rebooted (default "Rebooting node %s")
            #       - --message-template-uncordon=Uncordoning node %s  # message template used to notify about a node being successfully uncordoned (default "Node %s rebooted & uncordoned successfully!")
            #       --node-id string                      node name kured runs on, should be passed down from spec.nodeName via KURED_NODE_ID environment variable
            #       - --notify-url=""                       # notify URL for reboot notifications (cannot use with --slack-hook-url flags)
            #       - --period=5m                           #sentinel check period (default 1h0m0s)
            #       --post-reboot-node-labels strings     labels to add to nodes after uncordoning
            #       --pre-reboot-node-labels strings      labels to add to nodes before cordoning
            #       - --prefer-no-schedule-taint=""        # Taint name applied during pending node reboot (to prevent receiving additional pods from other rebooting nodes). Disabled by default. Set e.g. to "weave.works/kured-node-reboot" to enable tainting.
            #       - --prometheus-url=http://prometheus.monitoring.svc.cluster.local  #Prometheus instance to probe for active alerts
            #       --reboot-command string               command to run when a reboot is required (default "/bin/systemctl reboot")
            #       - --reboot-days=sun,mon,tue,wed,thu,fri,sat  # schedule reboot on these days (default [su,mo,tu,we,th,fr,sa])
            #       - --reboot-delay=90s                    # delay reboot for this duration (default: 0, disabled)
            #       - --reboot-sentinel=/var/run/reboot-required  # path to file whose existence triggers the reboot command (default "/var/run/reboot-required")
            #       - --reboot-sentinel-command=""          # command for which a zero return code will trigger a reboot command
            #       - --skip-wait-for-delete-timeout=0      # when seconds is greater than zero, skip waiting for the pods whose deletion timestamp is older than N seconds while draining a node
            #       - --slack-channel=alerting              #  slack channel for reboot notifications
            #       - --slack-username=prod                 # slack username for reboot notifications (default "kured")
            #       - --time-zone=UTC                      #use this timezone for schedule inputs (default "UTC")
