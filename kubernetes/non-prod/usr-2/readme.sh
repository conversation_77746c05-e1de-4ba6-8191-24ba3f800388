#!/bin/bash

# Set the running variables
ingress_namespace="ingress-controller"
resourceGroup="wapol-wpm-rg-2-usr"
clusterName="wapol-wpm-aks-cluster-2-usr"
docker_email="<EMAIL>"
docker_server="wapolwpmacrdev.azurecr.io"
app_id="20a922f4-acf0-4344-a987-40f6336a0161"
app_password=""
tenant_id="988004e3-b844-4b61-a48e-4fa2e005ab70"
subscription_id="d0304bbb-bb3d-4d7c-acc3-cf396dd16ba8"
environment_name="usr"

# Login to Azure
az login --service-principal -u "$app_id" -p "$app_password" --tenant "$tenant_id"

# Downloads credentials and configures the Kubernetes CLI to use them
az aks get-credentials --resource-group "$resourceGroup" --name "$clusterName"

# Check the Cluster version
kubectl version

# Check all K8S resources
kubectl get all --all-namespaces

# Create all required namespaces
kubectl apply -f ./all-namespace.yaml

# Get the resource group name of the AKS cluster
AKSClusterGroupName=$(az aks show --resource-group "$resourceGroup" --name "$clusterName" --query nodeResourceGroup -o tsv)

#----------------------------------------------#
#-Run below steps to deploy ingress controller-#
#----------------------------------------------#

# Get the object id of service principal of aks-cluster
aks_sp_object_id=$(az resource list -n "$clusterName" --query '[*].identity.principalId' --out tsv)

# Assign network contributor role on mdc-pscore-rg-usr to aks cluster using principal id above.
# This role assignment can be done only through tenant owner (with user access admin privilege)
az role assignment create --assignee-object-id "$aks_sp_object_id" --role "Network Contributor" --scope "/subscriptions/$subscription_id/resourceGroups/$resourceGroup"

# Get the static IP address of the portal
public_ip=$(az network public-ip show --resource-group "$resourceGroup" --name "wapol-wpm-aks-publicip-2-$environment_name" --query ipAddress --output tsv)

# Change the load balancer public IP in ingress-controller-deployment.yaml with the value of $public_ip
# Change the resource group in the service annotation
# Assign an internal ip for aks internal lb

# Deploy ingress controller
kubectl apply -f ./ingress-controller/all-ingress-controller-deployment.yaml

#----------------------#
#-start repeat per env-#
#----------------------#
# Set the environment first, ie usr
env="usr"
backend_namespace="backend-$env"
# frontend_namespace="frontend-$env"

# Create a name-space
# kubectl create namespace $frontend_namespace
# kubectl create namespace $backend_namespace

# Get namespaces
kubectl get namespaces

# Create secret to pull image from Motorola Container Registry
kubectl create secret -n "$backend_namespace" docker-registry acr-secret --docker-server "$docker_server" --docker-email "$docker_email" --docker-username "$app_id" --docker-password "$app_password"
kubectl create secret -n backend-integration-usr docker-registry acr-secret --docker-server "$docker_server" --docker-email "$docker_email" --docker-username "$app_id" --docker-password "$app_password"
kubectl create secret -n backend-training docker-registry acr-secret --docker-server "$docker_server" --docker-email "$docker_email" --docker-username "$app_id" --docker-password "$app_password"
kubectl create secret -n backend-integration-training docker-registry acr-secret --docker-server "$docker_server" --docker-email "$docker_email" --docker-username "$app_id" --docker-password "$app_password"

# Create TLS secret using the CA signed certificate to use with ingress resource
kubectl create secret tls aks-ingress-tls -n "$backend_namespace" --key pscore.cloud.key --cert pscore.cloud.crt

# Check the secret has been created
kubectl get secrets -A

# Deploy ingress extension (rules)
kubectl apply -f "./ingress-controller/$env-backend-core-proxy-external-ingress-rules.yaml"
kubectl apply -f "./ingress-controller/$env-backend-core-proxy-internal-ingress-rules.yaml"
kubectl apply -f "./ingress-controller/$env-backend-reference-data-external-ingress-rules.yaml"
kubectl apply -f "./ingress-controller/$env-backend-reference-data-internal-ingress-rules.yaml"
kubectl apply -f "./ingress-controller/$env-backend-user-persistence-external-ingress-rules.yaml"
kubectl apply -f "./ingress-controller/$env-backend-user-persistence-internal-ingress-rules.yaml"

#--------------------#
#-end repeat per env-#
#--------------------#

# Verify if the deployment is successful
kubectl get all --all-namespaces

#---------------------------------#
#--repeat per aks cluster per acr-#
#---------------------------------#

# Attach ACR to a kubernetes cluster
# az aks update -n $clusterName -g $resourceGroup --attach-acr /subscriptions/d0304bbb-bb3d-4d7c-acc3-cf396dd16ba8/resourceGroups/$resourceGroupofAcr/providers/Microsoft.ContainerRegistry/registries/$AcrName

# Example for mdcpscoreacrdev and wapolwpmacrdev ACRs
az aks update -n "$clusterName" -g "$resourceGroup" --attach-acr "/subscriptions/d0304bbb-bb3d-4d7c-acc3-cf396dd16ba8/resourceGroups/mdc-pscore-shared-services-rg-dev/providers/Microsoft.ContainerRegistry/registries/mdcpscoreacrdev"
az aks update -n "$clusterName" -g "$resourceGroup" --attach-acr "/subscriptions/d0304bbb-bb3d-4d7c-acc3-cf396dd16ba8/resourceGroups/wapol-wpm-shared-services-rg-dev/providers/Microsoft.ContainerRegistry/registries/wapolwpmacrdev"

#------------------------------------#
#-end repeat per aks cluster per acr-#
#------------------------------------#


#--------------------------------------------#
#Deploy Azure AD Pod Identity - per cluster--#
#--------------------------------------------#
# https://azure.github.io/aad-pod-identity/docs/demo/standard_walkthrough/
# https://azure.github.io/aad-pod-identity/docs/getting-started/role-assignment/

nodeResourceGroup="wapol-wpm-aks-rg-2-usr"     # same as managed identity resource group
identityResourceGroup="wapol-wpm-aks-rg-2-usr"

###################
# AKS managed identity

# Follow the link above. Replace step 2 with steps below to get Identity resourceId and clientId to be replaced in all-aadpodidentity.yaml
resourceID=$(az aks show -g $resourceGroup -n $clusterName --query 'identityProfile.kubeletidentity.resourceId' -otsv)
clientID=$(az aks show -g $resourceGroup -n $clusterName --query 'identityProfile.kubeletidentity.clientId' -otsv)

# The roles Managed Identity Operator and Virtual Machine Contributor must be assigned to the cluster managed identity identified by the ID obtained above, before deploying AAD Pod Identity so that it can assign and un-assign identities from the underlying VM/VMSS.
az role assignment create --role "Managed Identity Operator" --assignee "$clientID" --scope "/subscriptions/$subscription_id/resourcegroups/$nodeResourceGroup"
az role assignment create --role "Virtual Machine Contributor" --assignee "$clientID" --scope "/subscriptions/$subscription_id/resourcegroups/$nodeResourceGroup"

# Assign the role "Reader" to the identity so it has read access to the resource group. At the same time, store the identity assignment ID as an environment variable.
az role assignment create --role Reader --assignee "$clientID" --scope "/subscriptions/$subscription_id/resourceGroups/$nodeResourceGroup"

# assigning to cluster managed id permission on nodepool managed id above
# az aks show -g $resourceGroup -n $clusterName --query "identity"
# az role assignment create --role "Managed Identity Operator" --assignee $principalId --scope /subscriptions/$subscription_id/resourcegroups/$nodeResourceGroup


#-----------------------------#
#-Give permission to above identity on keyvault
#-----------------------------#
# Assign reader role to managed id above on kv resource group
kvResourceGroup="wapol-wpm-shared-services-rg-usr"
# az role assignment create --role Reader --assignee $clientID --scope /subscriptions/$subscription_id/resourceGroups/$kvResourceGroup

# Grant above managed identity access to key vault secrets, keys, certs
az keyvault set-policy --name "wapol-wpm-kv-$environment_name" --spn "$clientID" --secret-permissions backup delete get list set --key-permissions backup delete get list --certificate-permissions backup delete get list

# To test run below commands from pod.
# curl 'http://***************/metadata/identity/oauth2/token?api-version=2018-02-01&resource=https%3A%2F%2Fvault.azure.net' -H Metadata:true
# curl https://wapol-wpm-kv-usr.vault.azure.net/secrets/wapol-apns-certificatate-usr?api-version=2016-10-01 -H "Authorization: Bearer <token>"

# Check nmi and mic pods.
# Check azureassignedidentity
kubectl get azureassignedidentity -A

#--------------------------------------------#
#Deploy AD RBAC for AKS users--#
#--------------------------------------------#

# reference sources:
# https://docs.microsoft.com/en-us/azure/aks/azure-ad-integration-cli
# https://docs.microsoft.com/en-us/azure/aks/azure-ad-rbac

# 0. Login using an admin account. This account need tenant admin right to consent to AKS AAD integration, also needs AAD admin right to assign roles to AAD groups.
az login

aksname="wapol-wpm-aks-cluster-2-usr"
resourceGroup="wapol-wpm-rg-2-usr"
tenantId=$(az account show --query tenantId -o tsv)

# check aad profile is enabled
az aks show -g "$resourceGroup" -n "$aksname"

# 4. Config AKS RBAC and AAD
# get AKS ID
AKS_ID=$(az aks show --resource-group "$resourceGroup" --name "$aksname" --query id -o tsv)

# If the group exists, get AAD group ID
AKS_DEV_GROUP=$(az ad group show --group "wapol-wpm-aks-developer-usr" --query id -o tsv)

# Assign AKS access role to the group
az role assignment create --assignee $AKS_DEV_GROUP --role "Azure Kubernetes Service Cluster User Role" --scope "$AKS_ID"

# 4. Allow the AAD admin group to use kubectl
# get AKS ID
AKS_ID=$(az aks show --resource-group $resourceGroup --name $aksname --query id -o tsv)

# If the group exists, get AAD group ID
AKS_ADMIN_GROUP=$(az ad group show --group "wapol-wpm-aks-admin-usr" --query id -o tsv)

# Assign AKS access role to the group
az role assignment create --assignee "$AKS_ADMIN_GROUP" --role "Azure Kubernetes Service Cluster User Role" --scope "$AKS_ID"

# 5. Create rolebinding, clusterbinding
# Login using built-in system admin:
az aks get-credentials --resource-group "$resourceGroup" --name "$aksname"

# Change the AAD group ID in rolebinding and clusterrolebinding in the yaml files BEFORE run following command.
kubectl apply -f "./aks-aad-integration/usr-aks-aad-role-user.yaml"
kubectl apply -f "./aks-aad-integration/usr-aks-aad-rolebinding-user.yaml"
kubectl apply -f ./aks-aad-integration/all-aks-aad-clusterrolebinding-user.yaml
kubectl apply -f ./aks-aad-integration/all-aks-aad-clusterrolebinding-admin.yaml

# 6. Create Azure Devops Service Account
kubectl apply -f ./aks-aad-integration/all-aks-aad-serviceaccount-azure-devops.yaml
kubectl apply -f ./aks-aad-integration/all-aks-aad-clusterrole-azure-devops.yaml
kubectl apply -f ./aks-aad-integration/all-aks-aad-clusterrolebinding-azure-devops.yaml

# 7. Create Azure Devops Kubernetes connection using the serviceaccount secret

# Get the secret created for the Service Account created in step 6 and note service account secret name.
kubectl -n support-services get secrets

# Get the azure devops service account secret
# kubectl -n support-services get secret <service-account-secret-name> -o json

# Go to Azure Devops and add the service connection
# https://dev.azure.com/msi-cie/Gridstone/_settings/adminservices

# 8. Get the kube.config and run some test commands
az aks get-credentials --resource-group "$resourceGroup" --name "$aksname" --overwrite-existing

# Get AKS resources
kubectl get nodes
kubectl get namespaces
kubectl get pods -A

#-----------------------------#
# Kured daemonset to auto apply OS patches --> only apply to non prod AKS
#-----------------------------#
kubectl apply -f ./kured/all-kured-daemonset.yaml
