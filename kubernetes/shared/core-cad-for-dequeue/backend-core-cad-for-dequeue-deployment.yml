---
apiVersion: v1
kind: Service
metadata:
  name: backend-__serviceName__-svc-__environmentName__
  namespace: __K8sNamespace__
  labels:
    tier: backend
    environment: __environmentName__
spec:
  ports:
    - port: 80
      targetPort: __ExposedPort__
      protocol: TCP
      name: http
  selector:
    app: backend-__serviceName__
---
apiVersion: v1
kind: ServiceAccount
metadata:
  namespace: __K8sNamespace__
  name: backend-__serviceName__
  labels:
    account: backend-__serviceName__
  annotations:
    azure.workload.identity/client-id: __WorkloadIdentityClientId__
---
apiVersion: v1
kind: Secret
metadata:
  name: __serviceName__-secret
  namespace: __K8sNamespace__
type: Opaque
stringData:
  CadCacheDbConnStr: "__CadCacheDbConnStr__"
  BasicAuthPassword: "__BasicAuthPassword__"
  MessagePublisherAzureMessageBusSettingsConnectionString: "__MessagePublisherAzureMessageBusSettingsConnectionString__"
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: backend-__serviceName__
  namespace: __K8sNamespace__
  labels:
    app: backend-__serviceName__
    tier: backend
    environment: __environmentName__
    version: v1
spec:
  maxReplicas: __maxReplicas__
  minReplicas: __minReplicas__
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: backend-__serviceName__-__environmentName__
  metrics:
    - type: Resource
      resource:
        name: cpu
        #container: backend-__serviceName__
        target:
          type: Utilization
          averageUtilization: __targetCPUAverageUtilization__
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-__serviceName__-__environmentName__
  namespace: __K8sNamespace__
  labels:
    tier: backend
    environment: __environmentName__
    #aadpodidbinding: __serviceName__
    version: v1
    releaseId: "__releaseId__"
    ciCommitId: "__ciCommitId__"
    cdCommitId: "__cdCommitId__"
spec:
  replicas: __NoOfReplica__
  selector:
    matchLabels:
      app: backend-__serviceName__
      tier: backend
      environment: __environmentName__
      version: v1
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: backend-__serviceName__
        tier: backend
        environment: __environmentName__
        #aadpodidbinding: __serviceName__
        version: v1
        releaseId: "__releaseId__"
        azure.workload.identity/use: "__workloadIdentityEnabled__"
      annotations:
        sidecar.istio.io/inject: "__istioSidecarInjection__"
        sidecar.istio.io/proxyCPU: "__istioSidecarInjectionProxyCPU__"
        sidecar.istio.io/proxyCPULimit: "__istioSidecarInjectionProxyCPULimit__"
        sidecar.istio.io/proxyMemory: "__istioSidecarInjectionProxyMemory__"
        sidecar.istio.io/proxyMemoryLimit: "__istioSidecarInjectionProxyMemoryLimit__"
        container.apparmor.security.beta.kubernetes.io/backend-__serviceName__: runtime/default
    spec:
      serviceAccountName: backend-__serviceName__
      containers:
        - name: backend-__serviceName__
          image: __ImageRepo__/__ImageName__
          imagePullPolicy: IfNotPresent
          env:
            - name: "ASPNETCORE_ENVIRONMENT"
              value: "__ASPEnvironment__"
            - name: "ASPNETCORE_URLS"
              value: "http://+:__ExposedPort__"
            - name: "ConnectionStrings__CadCacheConnection"
              valueFrom:
                secretKeyRef:
                  name: __serviceName__-secret
                  key: CadCacheDbConnStr
            - name: "PushNotifications__ServiceUri"
              value: "__PushNotificationServiceUri__"
            - name: "PushNotifications__Source"
              value: "__PushNotificationSource__"

            - name: "TenantSettings__Tenants__0__CadAdapterConfig__RequestUri"
              value: "__TenantSettingsTenants0CadAdapterConfigRequestUri__"
            - name: "TenantSettings__Tenants__0__CadAdapterConfig__RequestPaths__BookOff"
              value: "__TenantSettingsTenants0CadAdapterConfigRequestPathsBookOff__"
            - name: "TenantSettings__Tenants__0__CadAdapterConfig__RequestPaths__BookOn"
              value: "__TenantSettingsTenants0CadAdapterConfigRequestPathsBookOn__"
            - name: "TenantSettings__Tenants__0__CadAdapterConfig__RequestPaths__UpdateLocation"
              value: "__TenantSettingsTenants0CadAdapterConfigRequestPathsUpdateLocation__"
            - name: "TenantSettings__Tenants__0__CadAdapterConfig__RequestPaths__GetResourceDetails"
              value: "__TenantSettingsTenants0CadAdapterConfigRequestPathsGetResourceDetails__"
            - name: "TenantSettings__Tenants__0__CadAdapterConfig__RequestPaths__SetResourceStatus"
              value: "__TenantSettingsTenants0CadAdapterConfigRequestPathsSetResourceStatus__"
            - name: "TenantSettings__Tenants__0__CadAdapterConfig__RequestPaths__UpdateResource"
              value: "__TenantSettingsTenants0CadAdapterConfigRequestPathsUpdateResource__"
            - name: "TenantSettings__Tenants__0__CadAdapterConfig__RequestPaths__AssignIncident"
              value: "__TenantSettingsTenants0CadAdapterConfigRequestPathsAssignIncident__"
            - name: "TenantSettings__Tenants__0__CadAdapterConfig__RequestPaths__CreateIncident"
              value: "__TenantSettingsTenants0CadAdapterConfigRequestPathsCreateIncident__"
            - name: "TenantSettings__Tenants__0__CadAdapterConfig__RequestPaths__GetIncidentDetails"
              value: "__TenantSettingsTenants0CadAdapterConfigRequestPathsGetIncidentDetails__"
            - name: "TenantSettings__Tenants__0__CadAdapterConfig__RequestPaths__FinaliseIncident"
              value: "__TenantSettingsTenants0CadAdapterConfigRequestPathsFinaliseIncident__"
            - name: "TenantSettings__Tenants__0__CadAdapterConfig__RequestPaths__UpdateIncident"
              value: "__TenantSettingsTenants0CadAdapterConfigRequestPathsUpdateIncident__"
            - name: "TenantSettings__Tenants__0__CadAdapterConfig__ExtraHeaders__0"
              value: "__TenantSettingsTenants0CadAdapterConfigExtraHeaders0__"
            - name: "TenantSettings__Tenants__0__CadAdapterConfig__ExtraHeaders__1"
              value: "__TenantSettingsTenants0CadAdapterConfigExtraHeaders1__"
            - name: "TenantSettings__Tenants__0__CadAdapterConfig__ExtraHeaders__2"
              value: "__TenantSettingsTenants0CadAdapterConfigExtraHeaders2__"
            - name: "TenantSettings__Tenants__0__CadAdapterConfig__ExtraHeaders__3"
              value: "__TenantSettingsTenants0CadAdapterConfigExtraHeaders3__"
            - name: "TenantSettings__Tenants__0__CadAdapterConfig__HealthCheckEndpointUrl"
              value: "__TenantSettingsTenants0CadAdapterConfigHealthCheckEndpointUrl__"
            - name: "TenantSettings__Tenants__0__CadAdapterConfig__HealthCheckEnabled"
              value: "__TenantSettingsTenants0CadAdapterConfigHealthCheckEnabled__"
            - name: "TenantSettings__Tenants__0__TenancyIDs__0"
              value: "__TenantSettingsTenants0TenancyIDs0__"
            - name: "TenantSettings__Tenants__0__CadNotification__OnIncidentStatusUpdatedSetResourceStatus"
              value: "__TenantSettingsTenants0CadNotificationOnIncidentStatusUpdatedSetResourceStatus__"

            - name: "HealthCheckWorker__Enabled"
              value: "__HealthCheckWorkerEnabled__"
            - name: "NotificationNoiseGuard__Enabled"
              value: "__NotificationNoiseGuardEnabled__"
            - name: "Duress__Code"
              value: "__DuressCode__"
            - name: "PublishMessages"
              value: "__PublishMessages__"
            - name: "NotificationNoiseGuard__BatchPushNotificationSize"
              value: "__NotificationNoiseGuardBatchPushNotificationSize__"
            - name: "TenantSettings__Tenants__0__Sync__IncludeAllIncidentsAssignedToCallSign"
              value: "__TenantSettingsTenants0SyncIncludeAllIncidentsAssignedToCallSign__"
            - name: "TenantSettings__Tenants__0__Sync__IncludeUnassignedIncidents"
              value: "__TenantSettingsTenants0SyncIncludeUnassignedIncidents__"
            - name: "TenantSettings__Tenants__0__Sync__ClosedIncidentSyncThresholdInMinutes"
              value: "__TenantSettingsTenants0SyncClosedIncidentSyncThresholdInMinutes__"
            - name: "TenantSettings__Tenants__0__Sync__IncludeDefaultPatrolGroupInWhereCondition"
              value: "__TenantSettingsTenants0SyncIncludeDefaultPatrolGroupInWhereCondition__"
            - name: "TenantSettings__Tenants__0__Sync__IncludeStaticResources"
              value: "__TenantSettingsTenants0SyncIncludeStaticResources__"
            - name: "TenantSettings__Tenants__0__Events__PublishEventTypesSwitch__ShouldPublishDuressEvents"
              value: "__TenantSettingsTenants0EventsPublishEventTypesSwitchShouldPublishDuressEvents__"
            - name: "Database__CacheDb__UseQuerySplittingForResourcesSync"
              value: "__DatabaseCacheDbUseQuerySplittingForResourcesSync__"
            - name: "Auth__Username"
              value: "__BasicAuthUsername__"
            - name: "Auth__Password"
              valueFrom:
                secretKeyRef:
                  name: __serviceName__-secret
                  key: BasicAuthPassword
            - name: "Auth__SkipAuthentication"
              value: "__SkipAuthentication__"
            - name: "Auth__Enabled"
              value: "__Enabled__"
            #message publisher
            - name: "MessagePublisher__VerboseSendPipelineLogging"
              value: "__MessagePublisherVerboseSendPipelineLogging__"
            #azure messagebus
            - name: "MessagePublisher__AzureMessageBusSettings__PublishMessages"
              value: "__MessagePublisherAzureMessageBusSettingsPublishMessages__"
            - name: "MessagePublisher__AzureMessageBusSettings__ConnectionString"
              valueFrom:
                secretKeyRef:
                  name: __serviceName__-secret
                  key: MessagePublisherAzureMessageBusSettingsConnectionString
            - name: "MessagePublisher__AzureMessageBusSettings__EnvironmentName"
              value: "__MessagePublisherAzureMessageBusSettingsEnvironmentName__"
            #Push Notification Client
            - name: "PushNotificationClient__PushNotificationServiceUrl"
              value: "__PushNotificationClientPushNotificationServiceUrl__"
            - name: "PushNotificationClient__NotificationSourceName"
              value: "__PushNotificationClientNotificationSourceName__"
            - name: "PushNotificationClient__NotificationPayloadVersions__Android"
              value: "__PushNotificationClientNotificationPayloadVersionsAndroid__"
            - name: "PushNotificationClient__NotificationPayloadVersions__iOS"
              value: "__PushNotificationClientNotificationPayloadVersionsIOS__"
            - name: "PushNotificationClient__DefaultNotificationsToForeground"
              value: "__PushNotificationClientDefaultNotificationsToForeground__"
            - name: "PushNotificationClient__EnableNotificationGrouping"
              value: "__PushNotificationClientEnableNotificationGrouping__"
            #serilog
            - name: "Serilog__Using"
              value: "__SerilogUsing__"
            - name: "Serilog__MinimumLevel__Default"
              value: "__SerilogMinimumLevelDefault__"
            - name: "Serilog__MinimumLevel__Override__Microsoft"
              value: "__SerilogMinimumLevelOverrideMicrosoft__"
            - name: "Serilog__MinimumLevel__Override__System"
              value: "__SerilogMinimumLevelOverrideSystem__"
            - name: "Serilog__MinimumLevel__Override__Elastic.Apm"
              value: "__SerilogMinimumLevelOverrideElasticApm__"
            #elastic APM
            - name: "ElasticApm__Enabled"
              value: "__ElasticApmEnabled__"
            - name: "ElasticApm__ServerUrl"
              value: "__ElasticApmServerUrl__"
            - name: "ElasticApm__ServiceName"
              value: "__ElasticApmServiceName__"
            - name: "ElasticApm__ServiceVersion"
              value: "__ElasticApmServiceVersion__"
            - name: "ElasticApm__TransactionSampleRate"
              value: "__ElasticApmTransactionSampleRate__"
            - name: "ElasticApm__Environment"
              value: "__ElasticApmEnvironment__"
            #Logging Context
            - name: "StaticLoggingContext__ApplicationName"
              value: "__StaticLoggingContextApplicationName__"
            - name: "StaticLoggingContext__ApplicationVersion"
              value: "__StaticLoggingContextApplicationVersion__"
            - name: "StaticLoggingContext__KubernetesNamespace"
              value: "__StaticLoggingContextKubernetesNamespace__"
            - name: "StaticLoggingContext__ImageName"
              value: "__StaticLoggingContextImageName__"
            - name: "StaticLoggingContext__ImageTag"
              value: "__StaticLoggingContextImageTag__"
          resources:
            requests:
              cpu: "__CpuRequest__"
              memory: "__MemoryRequestinMB__M"
            limits:
              cpu: "__CpuLimit__"
              memory: "__MemoryLimitinMB__M"
          ports:
            - containerPort: __ExposedPort__
          readinessProbe:
            httpGet:
              path: /heartbeat
              port: __ExposedPort__
            initialDelaySeconds: __ReadinessProbeInitDelaySeconds__
            periodSeconds: __ReadinessProbePeriodSeconds__
            timeoutSeconds: __ReadinessProbeTimeoutSeconds__
          livenessProbe:
            httpGet:
              path: /heartbeat
              port: __ExposedPort__
            initialDelaySeconds: __LivenessProbeInitDelaySeconds__
            periodSeconds: __LivenessProbePeriodSeconds__
            timeoutSeconds: __LivenessProbeTimeoutSeconds__
      restartPolicy: Always
      #imagePullSecrets:
      # - name: __ImagePullSecretName__
