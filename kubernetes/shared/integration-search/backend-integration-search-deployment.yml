---
apiVersion: v1
kind: Service
metadata:
  name: backend-__serviceName__-svc-__environmentName__
  namespace: __K8sNamespace__
  labels:
    tier: backend
    environment: __environmentName__
spec:
  ports:
    - port: 80
      targetPort: __ExposedPort__
      protocol: TCP
      name: http
  selector:
    app: backend-__serviceName__
---
apiVersion: v1
kind: ServiceAccount
metadata:
  namespace: __K8sNamespace__
  name: backend-__serviceName__
  labels:
    account: backend-__serviceName__
---
apiVersion: v1
kind: Secret
metadata:
  name: __serviceName__-secret
  namespace: __K8sNamespace__
type: Opaque
stringData:
  appConfigurationwaPolApitoken: "__appConfigurationwaPolApitoken__"
  MicrosoftGraphApiclientSecret: "__MicrosoftGraphApiclientSecret__"
---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: backend-__serviceName__
  namespace: __K8sNamespace__
  labels:
    app: backend-__serviceName__
    tier: backend
    environment: __environmentName__
    version: v1
spec:
  maxReplicas: __maxReplicas__
  minReplicas: __minReplicas__
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: backend-__serviceName__-__environmentName__
  metrics:
    - type: Resource
      resource:
        name: cpu
        #container: backend-__serviceName__
        target:
          type: Utilization
          averageUtilization: __targetCPUAverageUtilization__
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-__serviceName__-__environmentName__
  namespace: __K8sNamespace__
  labels:
    tier: backend
    environment: __environmentName__
    version: v1
    releaseId: "__releaseId__"
    ciCommitId: "__ciCommitId__"
    cdCommitId: "__cdCommitId__"
spec:
  replicas: __NoOfReplica__
  selector:
    matchLabels:
      app: backend-__serviceName__
      tier: backend
      environment: __environmentName__
      version: v1
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        app: backend-__serviceName__
        tier: backend
        environment: __environmentName__
        version: v1
        releaseId: "__releaseId__"
        ciCommitId: "__ciCommitId__"
        cdCommitId: "__cdCommitId__"
      annotations:
        sidecar.istio.io/inject: "__istioSidecarInjection__"
        sidecar.istio.io/proxyCPU: "__istioSidecarInjectionProxyCPU__"
        sidecar.istio.io/proxyCPULimit: "__istioSidecarInjectionProxyCPULimit__"
        sidecar.istio.io/proxyMemory: "__istioSidecarInjectionProxyMemory__"
        sidecar.istio.io/proxyMemoryLimit: "__istioSidecarInjectionProxyMemoryLimit__"
        container.apparmor.security.beta.kubernetes.io/backend-__serviceName__: runtime/default
    spec:
      serviceAccountName: backend-__serviceName__
      containers:
        - name: backend-__serviceName__
          image: __ImageRepo__/__ImageName__
          imagePullPolicy: IfNotPresent
          env:
            - name: "ASPNETCORE_ENVIRONMENT"
              value: "__ASPEnvironment__"
            - name: "ASPNETCORE_URLS"
              value: "http://+:__ExposedPort__"

              # Log Configuration
            - name: "LogConfiguration__Environment"
              value: "__LogConfigurationEnvironment__"
            - name: "LogConfiguration__CleanData"
              value: "__LogConfigurationCleanData__"

              # App Configuration
            - name: "appConfiguration__waPolApi__baseUrl"
              value: "__appConfigurationwaPolApibaseUrl__"
            - name: "appConfiguration__waPolApi__token"
              valueFrom:
                secretKeyRef:
                  name: __serviceName__-secret
                  key: appConfigurationwaPolApitoken
            - name: "appConfiguration__waPolApi__returnClientRequestPayloadSent"
              value: "__appConfigurationwaPolApireturnClientRequestPayloadSent__"
            # mock response
            - name: "appConfiguration__MockResponse__Paths__0__Path"
              value: "__appConfigurationMockResponsePaths0Path__"
            - name: "appConfiguration__MockResponse__Paths__0__UseJsonFileResponse"
              value: "__appConfigurationMockResponsePaths0UseJsonFileResponse__"
            - name: "appConfiguration__MockResponse__Paths__0__JsonFilePath"
              value: "__appConfigurationMockResponsePaths0JsonFilePath__"

            - name: "appConfiguration__MockResponse__Paths__1__Path"
              value: "__appConfigurationMockResponsePaths1Path__"
            - name: "appConfiguration__MockResponse__Paths__1__UseJsonFileResponse"
              value: "__appConfigurationMockResponsePaths1UseJsonFileResponse__"
            - name: "appConfiguration__MockResponse__Paths__1__JsonFilePath"
              value: "__appConfigurationMockResponsePaths1JsonFilePath__"

            - name: "appConfiguration__MockResponse__Paths__2__Path"
              value: "__appConfigurationMockResponsePaths2Path__"
            - name: "appConfiguration__MockResponse__Paths__2__UseJsonFileResponse"
              value: "__appConfigurationMockResponsePaths2UseJsonFileResponse__"
            - name: "appConfiguration__MockResponse__Paths__2__JsonFilePath"
              value: "__appConfigurationMockResponsePaths2JsonFilePath__"

            - name: "appConfiguration__MockResponse__Paths__3__Path"
              value: "__appConfigurationMockResponsePaths3Path__"
            - name: "appConfiguration__MockResponse__Paths__3__UseJsonFileResponse"
              value: "__appConfigurationMockResponsePaths3UseJsonFileResponse__"
            - name: "appConfiguration__MockResponse__Paths__3__JsonFilePath"
              value: "__appConfigurationMockResponsePaths3JsonFilePath__"

            - name: "appConfiguration__MockResponse__Paths__4__Path"
              value: "__appConfigurationMockResponsePaths4Path__"
            - name: "appConfiguration__MockResponse__Paths__4__UseJsonFileResponse"
              value: "__appConfigurationMockResponsePaths4UseJsonFileResponse__"
            - name: "appConfiguration__MockResponse__Paths__4__JsonFilePath"
              value: "__appConfigurationMockResponsePaths4JsonFilePath__"

            - name: "appConfiguration__MockResponse__Paths__5__Path"
              value: "__appConfigurationMockResponsePaths5Path__"
            - name: "appConfiguration__MockResponse__Paths__5__UseJsonFileResponse"
              value: "__appConfigurationMockResponsePaths5UseJsonFileResponse__"
            - name: "appConfiguration__MockResponse__Paths__5__JsonFilePath"
              value: "__appConfigurationMockResponsePaths5JsonFilePath__"

            - name: "appConfiguration__MockResponse__Paths__6__Path"
              value: "__appConfigurationMockResponsePaths6Path__"
            - name: "appConfiguration__MockResponse__Paths__6__UseJsonFileResponse"
              value: "__appConfigurationMockResponsePaths6UseJsonFileResponse__"
            - name: "appConfiguration__MockResponse__Paths__6__JsonFilePath"
              value: "__appConfigurationMockResponsePaths6JsonFilePath__"

            - name: "appConfiguration__MockResponse__Paths__7__Path"
              value: "__appConfigurationMockResponsePaths7Path__"
            - name: "appConfiguration__MockResponse__Paths__7__UseJsonFileResponse"
              value: "__appConfigurationMockResponsePaths7UseJsonFileResponse__"
            - name: "appConfiguration__MockResponse__Paths__7__JsonFilePath"
              value: "__appConfigurationMockResponsePaths7JsonFilePath__"

            - name: "appConfiguration__MockResponse__Paths__8__Path"
              value: "__appConfigurationMockResponsePaths8Path__"
            - name: "appConfiguration__MockResponse__Paths__8__UseJsonFileResponse"
              value: "__appConfigurationMockResponsePaths8UseJsonFileResponse__"
            - name: "appConfiguration__MockResponse__Paths__8__JsonFilePath"
              value: "__appConfigurationMockResponsePaths8JsonFilePath__"

            - name: "appConfiguration__MockResponse__Paths__9__Path"
              value: "__appConfigurationMockResponsePaths9Path__"
            - name: "appConfiguration__MockResponse__Paths__9__UseJsonFileResponse"
              value: "__appConfigurationMockResponsePaths9UseJsonFileResponse__"
            - name: "appConfiguration__MockResponse__Paths__9__JsonFilePath"
              value: "__appConfigurationMockResponsePaths9JsonFilePath__"

            #graph API
            - name: "MicrosoftGraphApi__tenantId"
              value: "__MicrosoftGraphApitenantId__"
            - name: "MicrosoftGraphApi__clientId"
              value: "__MicrosoftGraphApiclientId__"
            - name: "MicrosoftGraphApi__clientSecret"
              valueFrom:
                secretKeyRef:
                  name: __serviceName__-secret
                  key: MicrosoftGraphApiclientSecret
            - name: "MicrosoftGraphApi__GraphEndpoint"
              value: "__MicrosoftGraphApiGraphEndpoint__"
            - name: "MicrosoftGraphApi__GraphInstance"
              value: "__MicrosoftGraphApiGraphInstance__"
            - name: "MicrosoftGraphApi__GraphVersion"
              value: "__MicrosoftGraphApiGraphVersion__"
            - name: "MicrosoftGraphApi__Caching__Enabled"
              value: "__MicrosoftGraphApiCachingEnabled__"
            - name: "MicrosoftGraphApi__Caching__SlidingExpirationInMinutes"
              value: "__MicrosoftGraphApiCachingSlidingExpirationInMinutes__"
            #serilog
            - name: "Serilog__Using"
              value: "__SerilogUsing__"
            - name: "Serilog__MinimumLevel"
              value: "__SerilogMinimumLevel__"
            - name: "Serilog__MinimumLevel__Default"
              value: "__SerilogMinimumLevelDefault__"
            - name: "Serilog__MinimumLevel__Override__Microsoft"
              value: "__SerilogMinimumLevelOverrideMicrosoft__"
            - name: "Serilog__MinimumLevel__Override__System"
              value: "__SerilogMinimumLevelOverrideSystem__"
            - name: "Serilog__MinimumLevel__Override__Elastic.Apm"
              value: "__SerilogMinimumLevelOverrideElasticApm__"
            #elastic APM
            - name: "ElasticApm__Enabled"
              value: "__ElasticApmEnabled__"
            - name: "ElasticApm__ServerUrl"
              value: "__ElasticApmServerUrl__"
            - name: "ElasticApm__ServiceName"
              value: "__ElasticApmServiceName__"
            - name: "ElasticApm__ServiceVersion"
              value: "__ElasticApmServiceVersion__"
            - name: "ElasticApm__TransactionSampleRate"
              value: "__ElasticApmTransactionSampleRate__"
            - name: "ElasticApm__Environment"
              value: "__ElasticApmEnvironment__"
            # Reference Data Client
            - name: "ReferenceDataClient__BaseUrl"
              value: "__ReferenceDataClientBaseUrl__"
            - name: "ReferenceDataClient__ProcessInBatches"
              value: "__ReferenceDataClientProcessInBatches__"
            - name: "ReferenceDataClient__BatchSize"
              value: "__ReferenceDataClientBatchSize__"
            - name: "ReferenceDataClient__MaxParallelThreads"
              value: "__ReferenceDataClientMaxParallelThreads__"
            - name: "ReferenceDataClient__Caching__Enabled"
              value: "__ReferenceDataClientCachingEnabled__"
            - name: "ReferenceDataClient__Caching__SlidingExpirationInMinutes"
              value: "__ReferenceDataClientCachingSlidingExpirationInMinutes__"
          resources:
            requests:
              cpu: "__CpuRequest__"
              memory: "__MemoryRequestinMB__M"
            limits:
              cpu: "__CpuLimit__"
              memory: "__MemoryLimitinMB__M"
          ports:
            - containerPort: __ExposedPort__
              protocol: TCP
          readinessProbe:
            httpGet:
              path: /heartbeat
              port: __ExposedPort__
            initialDelaySeconds: __ReadinessProbeInitDelaySeconds__
            periodSeconds: __ReadinessProbePeriodSeconds__
            timeoutSeconds: __ReadinessProbeTimeoutSeconds__
          livenessProbe:
            httpGet:
              path: /heartbeat
              port: __ExposedPort__
            initialDelaySeconds: __LivenessProbeInitDelaySeconds__
            periodSeconds: __LivenessProbePeriodSeconds__
            timeoutSeconds: __LivenessProbeTimeoutSeconds__
      restartPolicy: Always
      #imagePullSecrets:
      #  - name: __ImagePullSecretName__
