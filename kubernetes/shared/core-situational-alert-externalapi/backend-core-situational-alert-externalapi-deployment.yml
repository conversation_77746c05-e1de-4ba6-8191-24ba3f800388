---
apiVersion: v1
kind: Service
metadata:
  name: backend-__serviceName__-svc-__environmentName__
  namespace: __K8sNamespace__
  labels:
    tier: backend
    environment: __environmentName__
spec:
  ports:
    - port: 80
      targetPort: __ExposedPort__
      protocol: TCP
      name: http
  selector:
    app: backend-__serviceName__
---
apiVersion: v1
kind: ServiceAccount
metadata:
  namespace: __K8sNamespace__
  name: backend-__serviceName__
  labels:
    account: backend-__serviceName__
    azure.workload.identity/use: "__AzureWorkloadIdentityUse__"
  annotations:
    azure.workload.identity/client-id: __WorkloadIdentityClientId__
---
apiVersion: v1
kind: Secret
metadata:
  name: __serviceName__-secret
  namespace: __K8sNamespace__
type: Opaque
stringData:
  AzureMapsServicePrimaryKey: "__AzureMapsServicePrimaryKey__"
  AzureMapsServiceSecondaryKey: "__AzureMapsServiceSecondaryKey__"

---
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: backend-__serviceName__
  namespace: __K8sNamespace__
  labels:
    app: backend-__serviceName__
    tier: backend
    environment: __environmentName__
    version: v1
spec:
  maxReplicas: __maxReplicas__
  minReplicas: __minReplicas__
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: backend-__serviceName__-__environmentName__
  metrics:
    - type: Resource
      resource:
        name: cpu
        #container: backend-__serviceName__
        target:
          type: Utilization
          averageUtilization: __targetCPUAverageUtilization__
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: backend-__serviceName__-__environmentName__
  namespace: __K8sNamespace__
  labels:
    tier: backend
    environment: __environmentName__
    version: v1
spec:
  replicas: __NoOfReplica__
  selector:
    matchLabels:
      app: backend-__serviceName__
      tier: backend
      environment: __environmentName__
      version: v1
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      labels:
        azure.workload.identity/use: "__AzureWorkloadIdentityUse__"
        app: backend-__serviceName__
        tier: backend
        environment: __environmentName__
        version: v1
      annotations:
        sidecar.istio.io/inject: "__istioSidecarInjection__"
        container.apparmor.security.beta.kubernetes.io/backend-__serviceName__: runtime/default
        sidecar.istio.io/proxyCPU: "__istioSidecarInjectionProxyCPU__"
        sidecar.istio.io/proxyCPULimit: "__istioSidecarInjectionProxyCPULimit__"
        sidecar.istio.io/proxyMemory: "__istioSidecarInjectionProxyMemory__"
        sidecar.istio.io/proxyMemoryLimit: "__istioSidecarInjectionProxyMemoryLimit__"
        # Allow azwi-proxy sidecar to query Azure VM metadata APIs
        traffic.sidecar.istio.io/excludeOutboundIPRanges: ***************/32
    spec:
      serviceAccountName: backend-__serviceName__
      containers:
        - name: backend-__serviceName__
          image: __ImageRepo__/__ImageName__
          imagePullPolicy: IfNotPresent
          env:
            - name: "ASPNETCORE_ENVIRONMENT"
              value: "__ASPEnvironment__"
            - name: "ASPNETCORE_URLS"
              value: "http://+:__ExposedPort__"
            - name: "Auth__ApiKeyAuthEnabled"
              value: "__AuthApiKeyAuthEnabled__"
            - name: "App__Integration__ExternalAlert__ServiceUri"
              value: "__AppIntegrationExternalAlertServiceUri__"
            - name: "App__Integration__ExternalAlert__ApiKey"
              value: "__AppIntegrationExternalAlertApiKey__"
            - name: "App__Integration__ExternalAlert__ApiKeyAuthEnabled"
              value: "__AppIntegrationExternalAlertApiKeyAuthEnabled__"
            - name: "App__Integration__DeviceSearch__ServiceUri"
              value: "__AppIntegrationDeviceSearchServiceUri__"
            - name: "App__Integration__DeviceSearch__ExcludeBookedOffDevices"
              value: "__AppIntegrationDeviceSearchExcludeBookedOffDevices__"
            - name: "App__Integration__PushNotification__ServiceUri"
              value: "__AppIntegrationPushNotificationServiceUri__"
            - name: "App__Integration__CreateAlert__ServiceUri"
              value: "__AppIntegrationCreateAlertServiceUri__"
            - name: "App__Integration__AzureMaps__ServiceUri"
              value: "__AppIntegrationAzureMapsServiceUri__"
            - name: "App__Integration__AzureMaps__ServicePrimaryKey"
              valueFrom:
                secretKeyRef:
                  name: __serviceName__-secret
                  key: AzureMapsServicePrimaryKey
            - name: "App__Integration__AzureMaps__ServiceSecondaryKey"
              valueFrom:
                secretKeyRef:
                  name: __serviceName__-secret
                  key: AzureMapsServiceSecondaryKey
            - name: "App__Integration__AzureMaps__ClientId"
              value: "__AppIntegrationAzureMapsClientId__"
            - name: "Notifications__AlertTitleMaxLength"
              value: "__NotificationsAlertTitleMaxLength__"
            - name: "Notifications__AlertBodyMaxLength"
              value: "__NotificationsAlertBodyMaxLength__"
            - name: "Notifications__Sound"
              value: "__NotificationsSound__"
            - name: "Notifications__IsForeground"
              value: "__NotificationsIsForeground__"
            - name: "PushNotificationClient__PushNotificationServiceUrl"
              value: "__PushNotificationClientPushNotificationServiceUrl__"
            - name: "PushNotificationClient__NotificationSourceName"
              value: "__PushNotificationClientNotificationSourceName__"
            - name: "PushNotificationClient__NotificationPayloadVersions__Android"
              value: "__PushNotificationClientNotificationPayloadVersionsAndroid__"
            - name: "PushNotificationClient__NotificationPayloadVersions__iOS"
              value: "__PushNotificationClientNotificationPayloadVersionsIOS__"
            - name: "PushNotificationClient__DefaultNotificationsToForeground"
              value: "__PushNotificationClientDefaultNotificationsToForeground__"
            - name: "PushNotificationClient__EnableNotificationGrouping"
              value: "__PushNotificationClientEnableNotificationGrouping__"
            #serilog
            - name: "Serilog__Using"
              value: "__SerilogUsing__"
            - name: "Serilog__MinimumLevel__Default"
              value: "__SerilogMinimumLevelDefault__"
            - name: "Serilog__MinimumLevel__Override__Microsoft"
              value: "__SerilogMinimumLevelOverrideMicrosoft__"
            - name: "Serilog__MinimumLevel__Override__System"
              value: "__SerilogMinimumLevelOverrideSystem__"
            - name: "Serilog__MinimumLevel__Override__Elastic.Apm"
              value: "__SerilogMinimumLevelOverrideElasticApm__"
            #elastic APM
            - name: "ElasticApm__Enabled"
              value: "__ElasticApmEnabled__"
            - name: "ElasticApm__ServerUrl"
              value: "__ElasticApmServerUrl__"
            - name: "ElasticApm__ServiceName"
              value: "__ElasticApmServiceName__"
            - name: "ElasticApm__ServiceVersion"
              value: "__ElasticApmServiceVersion__"
            - name: "ElasticApm__TransactionSampleRate"
              value: "__ElasticApmTransactionSampleRate__"
            - name: "ElasticApm__Environment"
              value: "__ElasticApmEnvironment__"
            - name: "ElasticApm__TraceContinuationStrategy"
              value: "__ElasticApmTraceContinuationStrategy__"
            #Logging Context
            - name: "StaticLoggingContext__ApplicationName"
              value: "__StaticLoggingContextApplicationName__"
            - name: "StaticLoggingContext__ApplicationVersion"
              value: "__StaticLoggingContextApplicationVersion__"
            - name: "StaticLoggingContext__KubernetesNamespace"
              value: "__StaticLoggingContextKubernetesNamespace__"
            - name: "StaticLoggingContext__ImageName"
              value: "__StaticLoggingContextImageName__"
            - name: "StaticLoggingContext__ImageTag"
              value: "__StaticLoggingContextImageTag__"
          resources:
            requests:
              cpu: "__CpuRequest__"
              memory: "__MemoryRequestinMB__M"
            limits:
              cpu: "__CpuLimit__"
              memory: "__MemoryLimitinMB__M"
          ports:
            - containerPort: __ExposedPort__
          readinessProbe:
            httpGet:
              path: /heartbeat
              port: __ExposedPort__
            initialDelaySeconds: __ReadinessProbeInitDelaySeconds__
            periodSeconds: __ReadinessProbePeriodSeconds__
            timeoutSeconds: __ReadinessProbeTimeoutSeconds__
            failureThreshold: __ReadinessProbefailureThreshold__
          livenessProbe:
            httpGet:
              path: /heartbeat
              port: __ExposedPort__
            initialDelaySeconds: __LivenessProbeInitDelaySeconds__
            periodSeconds: __LivenessProbePeriodSeconds__
            timeoutSeconds: __LivenessProbeTimeoutSeconds__
            failureThreshold: __LivenessProbefailureThreshold__
      restartPolicy: Always
