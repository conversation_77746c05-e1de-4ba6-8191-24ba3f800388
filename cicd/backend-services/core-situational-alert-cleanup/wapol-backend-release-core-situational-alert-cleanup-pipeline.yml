name: $(date:yyyyMMdd)$(rev:.r)

parameters:
  - name: image
    displayName: Pool Image
    default: ubuntu-latest
    values:
      - windows-latest
      - ubuntu-latest
      - macOS-latest

trigger: none # will disable CI builds entirely

pr: none # will disable PR builds entirely; will not disable CI triggers

resources:
  pipelines:
    - pipeline: pscore-core-situationalalerts-cleanup # Name of the pipeline resource
      project: pscore-framework
      source: pscore-core-situationalalerts-cleanup # Name of the triggering pipeline
      branch: main
      trigger:
        branches:
          - refs/heads/main

variables:
  - group: Gridstone-WAPol-non-production
  - name: "isPullRequest"
    value: ${{ne(variables['Build.Reason'],'PullRequest')}}
  - name: ciCommitId
    value: "$(resources.pipeline.pscore-core-situationalalerts-cleanup.sourceCommit)"
  - name: cdCommitId
    value: "$(Build.SourceVersion)"

stages:
  - stage: dev
    displayName: dev
    dependsOn: []
    pool:
      vmImage: ${{ parameters.image }}
    variables:
      - template: ../../templates/variables-backend-aks.yml
      - template: variables-dev.yml
    jobs:
      - deployment: "Deploy_to_AKS"
        displayName: "Deploy to AKS"
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        environment: "wapol-wpm-dev"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: ../../templates/taskgroup-backend-aks-deployment.yml

  - stage: test #deploy_test
    displayName: test #deploy_test
    dependsOn: dev
    condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'))
    pool:
      vmImage: ${{ parameters.image }}
    variables:
      - template: ../../templates/variables-backend-aks.yml
      - template: variables-test.yml
    jobs:
      - deployment: "Deploy_to_AKS"
        displayName: "Deploy to AKS"
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        environment: "wapol-wpm-test"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: ../../templates/taskgroup-backend-aks-deployment.yml

  - stage: sys
    displayName: sys
    dependsOn: test
    condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'))
    pool:
      vmImage: ${{ parameters.image }}
    variables:
      - template: ../../templates/variables-backend-aks.yml
      - template: variables-sys.yml
    jobs:
      - deployment: "Deploy_to_AKS"
        displayName: "Deploy to AKS"
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        environment: "wapol-wpm-sys"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: ../../templates/taskgroup-backend-aks-deployment.yml

  - stage: usr
    displayName: usr
    dependsOn: sys
    condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'))
    pool:
      vmImage: ${{ parameters.image }}
    variables:
      - template: ../../templates/variables-backend-aks.yml
      - template: variables-usr.yml
    jobs:
      - deployment: "Deploy_to_AKS"
        displayName: "Deploy to AKS"
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        environment: "wapol-wpm-usr"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: ../../templates/taskgroup-pe-acr-import-image.yml
                - template: ../../templates/taskgroup-backend-aks-deployment.yml

  - stage: usr2
    displayName: usr2
    dependsOn: sys
    condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'))
    pool:
      vmImage: ${{ parameters.image }}
    variables:
      - template: ../../templates/variables-backend-aks.yml
      - template: variables-usr2.yml
    jobs:
      - deployment: "Deploy_to_AKS"
        displayName: "Deploy to AKS"
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        environment: "wapol-wpm-usr"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: ../../templates/taskgroup-pe-acr-import-image.yml
                - template: ../../templates/taskgroup-backend-aks-deployment.yml

  - stage: prod
    displayName: prod
    dependsOn: usr
    condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'))
    pool:
      vmImage: ${{ parameters.image }}
    variables:
      - template: ../../templates/variables-backend-aks.yml
      - template: variables-prod.yml
    jobs:
      - deployment: "Deploy_to_AKS"
        displayName: "Deploy to AKS"
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        environment: "wapol-wpm-prod"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: ../../templates/taskgroup-pe-acr-import-image.yml
                - template: ../../templates/taskgroup-backend-aks-deployment.yml
      - deployment: "Retain_Build_Pipeline"
        displayName: "Retain Build Pipeline"
        dependsOn: [Deploy_to_AKS]
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        variables:
          - template: variables-build-pipeline-retain-prod.yml
        environment: "wapol-wpm-prod"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: ../../templates/taskgroup-pe-pipeline-retention.yml
      - deployment: "Retain_Release_Pipeline"
        displayName: "Retain Release Pipeline"
        dependsOn: [Deploy_to_AKS]
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        variables:
          - template: variables-release-pipeline-retain-prod.yml
        environment: "wapol-wpm-prod"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: ../../templates/taskgroup-pe-pipeline-retention.yml
  - stage: training
    displayName: training
    dependsOn: usr
    condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'))
    pool:
      vmImage: ${{ parameters.image }}
    variables:
      - template: ../../templates/variables-backend-aks.yml
      - template: variables-training.yml
    jobs:
      - deployment: "Deploy_to_AKS"
        displayName: "Deploy to AKS"
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        environment: "wapol-wpm-training"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: ../../templates/taskgroup-pe-acr-import-image.yml
                - template: ../../templates/taskgroup-backend-aks-deployment.yml
  - stage: training2
    displayName: training2
    dependsOn: usr
    condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'))
    pool:
      vmImage: ${{ parameters.image }}
    variables:
      - template: ../../templates/variables-backend-aks.yml
      - template: variables-training2.yml
    jobs:
      - deployment: "Deploy_to_AKS"
        displayName: "Deploy to AKS"
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        environment: "wapol-wpm-training"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: ../../templates/taskgroup-pe-acr-import-image.yml
                - template: ../../templates/taskgroup-backend-aks-deployment.yml