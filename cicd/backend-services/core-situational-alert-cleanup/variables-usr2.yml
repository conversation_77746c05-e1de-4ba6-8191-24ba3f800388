variables:
  #pipeline variables
  #contains variables that required to run the pipeline.
  - name: serviceName
    value: "core-situational-alert-cleanup"
  - name: buildNumber
    value: "$(resources.pipeline.pscore-core-situationalalerts-cleanup.runName)"
  - name: environmentName
    value: "usr"
  - name: workingDir
    value: "$(System.DefaultWorkingDirectory)/kubernetes/shared/core-situational-alert-cleanup"

  #service variables
  #container variables that are unique to the service
  - name: ImageName
    value: "pscore-core-situationalalerts-cleanup:$(buildNumber)"
  - name: ImageRepo
    value: $(destinationAcr)/$(environmentName)
  - name: sourceAcr
    value: "mdcpscoreacrdev.azurecr.io"
  - name: destinationAcr
    value: "wapolwpmacrdev.azurecr.io"
  - name: acr_azAppID
    value: $(azAppID)
  - name: acr_azAppSecret
    value: $(azAppSecret)
  - name: acr_azSubId
    value: $(azSubId)
  - name: acr_azTenantID
    value: $(azTenantID)
  - name: k8sClusterName
    value: "wapol-wpm-aks-cluster-2-usr"
  - name: resourceGroup
    value: "wapol-wpm-rg-2-usr"
  - name: NoOfReplica #3 replicas in usr and prod
    value: "1"

  #env variables
  - name: ConnectionStringsSituationalAlertDb
    value: "Server=tcp:$(ServerName),1433;Initial Catalog=$(DatabaseName);Persist Security Info=False;User ID=$(AppSqlUsername);Password=$(AppSqlPassword);MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"
  - name: AppSqlUsername
    value: "app_user_usr"
  - name: AppSqlPassword
    value: "$(sql-passwd-app-user-usr)"
  - name: CleanupCronExpression
    value: "5 * * * *"
  - name: CleanupTimeZoneInfo
    value: "Australia/Melbourne"
  - name: CleanupExpiryTime
    value: "0"
  - name: CleanupBatchSize
    value: "1000"
  - name: CleanupBatchDelayIntervalMilliseconds
    value: "100"
  - name: CleanupSqlServerCommandTimeoutSeconds
    value: "30"

  #serilog
  - name: SerilogMinimumLevelDefault
    value: "Error"
  - name: SerilogMinimumLevelOverrideMicrosoft
    value: "Error"
  - name: SerilogMinimumLevelOverrideSystem
    value: "Error"
  - name: SerilogMinimumLevelOverrideElasticApm
    value: "Error"

  #Logging Context
  - name: StaticLoggingContextImageName
    value: "$(ImageName)"

  #elastic APM
  - name: ElasticApmEnabled
    value: "true"
  - name: ElasticApmServerUrl
    value: "http://apm-server-svc-usr.monitoring-services:8200/"
  - name: ElasticApmServiceName
    value: "$(serviceName)"
  - name: ElasticApmServiceVersion
    value: "$(buildNumber)"
  - name: ElasticApmTransactionSampleRate
    value: "1.0"
  - name: ElasticApmEnvironment
    value: "$(environmentName)"

  #database migration
  - name: ServerName
    value: "wapol-wpm-sql-2-usr.database.windows.net"
  - name: DatabaseName
    value: "situational-alert-$(environmentName)"
  - name: SqlFile
    value: '$(Pipeline.Workspace)\pscore-core-situationalalerts\db\migration.sql'
  - name: SqlUsername
    value: "devops_user_usr"
  - name: SqlPassword
    value: "$(sql-passwd-devops-user-usr)"

  #database app credentials
  - name: AppSqlUsername
    value: "app_user_usr"
  - name: AppSqlPassword
    value: "$(sql-passwd-app-user-usr)"
