variables:
  #pipeline variables
  #contains variables that required to run the pipeline.

  #azure access variables
  - name: azAppID
    value: $(wapol-azure-service-principal-app-id-usr)
  - name: azAppSecret
    value: $(wapol-azure-service-principal-app-secret-usr)
  - name: azSubId
    value: $(wapol-azure-service-principal-subscription-id-usr)

  #service variables
  - name: serviceName
    value: "integration-cadtocad-data-adapter"
  - name: buildNumber
    value: "$(resources.pipeline.integration-cadtocad-data-adapter.runName)"
  - name: environmentName
    value: "training"
  - name: workingDir
    value: "$(System.DefaultWorkingDirectory)/kubernetes/shared/integration-cadtocad-data-adapter"
  - name: workloadIdentityEnabled
    value: "true"
  #Container variables unique to service
  - name: ImageName
    value: "$(serviceName):$(buildNumber)"
  - name: ImageRepo
    value: $(destinationAcr)/$(environmentName)
  - name: sourceAcr
    value: "wapolwpmacrdev.azurecr.io"
  - name: destinationAcr
    value: "wapolwpmacrdev.azurecr.io"
  - name: acr_azAppID
    value: $(azAppID)
  - name: acr_azAppSecret
    value: $(azAppSecret)
  - name: acr_azSubId
    value: $(azSubId)
  - name: acr_azTenantID
    value: $(azTenantID)
  - name: k8sClusterName
    value: "wapol-wpm-aks-cluster-2-usr"
  - name: resourceGroup
    value: "wapol-wpm-rg-2-usr"
  - name: ExposedPortTCP
    value: "16716"
  - name: NoOfReplica #3 replicas in usr and prod
    value: "1"

  #env varaibles
  - name: ConfigurationSettingsCacheDBUrl #core-cad-svc url
    value: '"http://backend-core-cad-for-dequeue-svc-$(environmentName)/cad"'
  - name: ConfigurationSettingsDataAdapterUrl
    value: '"http://backend-integration-cad-data-adapter-svc-$(environmentName).backend-integration-$(environmentName).svc.cluster.local"'
  - name: ConfigurationSettingsQueueEndpoint
    value: "$(wapol-azure-servicebus-namespace2-connectionstring-usr-2)"
  - name: ConfigurationSettingsQueueName
    value: "queue-cad-$(environmentName)"
  - name: ConfigurationSettingsTracingEnabled
    value: "false"
  - name: ConfigurationSettingsTcpServerIp
    value: '"0.0.0.0"'
  - name: ConfigurationSettingsTcpServerPort
    value: '"16716"'

  # health checks
  - name: ReadinessProbeInitDelaySeconds
    value: "30"
  - name: ReadinessProbePeriodSeconds
    value: "30"
  - name: ReadinessProbeTimeoutSeconds
    value: "20"

  #log configuration
  - name: LogConfigurationEnvironment
    value: "training"
  - name: LogConfigurationCleanData
    value: "true"

  #istio configuration
  - name: istioSidecarInjection
    value: "false"

  #serilog
  - name: SerilogWriteTo1ArgsFilePath
    value: log.txt
  - name: SerilogMinimumLevelDefault
    value: "Warning"
  - name: SerilogMinimumLevelOverrideMicrosoft
    value: "Error"
  - name: SerilogMinimumLevelOverrideSystem
    value: "Error"
  - name: SerilogMinimumLevelOverrideElasticApm
    value: "Error"
  - name: SerilogUsing
    value: "Serilog.Sinks.Console"

  #elastic APM
  - name: ElasticApmEnabled
    value: "true"
  - name: ElasticApmEnvironment
    value: "$(environmentName)"
  - name: ElasticApmServerUrl
    value: "http://apm-server-svc-usr.monitoring-services:8200/"
  - name: ElasticApmServiceName
    value: "$(serviceName)"
  - name: ElasticApmServiceVersion
    value: "$(buildNumber)"
  - name: ElasticApmTransactionSampleRate
    value: "1.0"
