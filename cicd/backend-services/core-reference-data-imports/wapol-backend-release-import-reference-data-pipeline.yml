parameters:
  - name: alwaysRunImport
    displayName: Always Run Import
    type: boolean
    default: false

trigger:
  branches:
    include:
      - develop
  paths:
    include:
      - cicd/backend-services/core-reference-data-imports/*

pr: none

variables:
  - group: "Gridstone-WAPol-non-production"

stages:
  - stage: Dev
    variables:
      - template: variables-dev.yml
    jobs:
      - deployment: "Deploy_to_Dev"
        displayName: Dev Deployment
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        environment: ${{ variables.deploymentEnvironment }}
        pool:
          name: WAPol Management VMs
          demands:
            - environment -equals ${{ variables.vmEnvironment }}
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: taskgroup-data-import.yml
                  parameters:
                    environment: ${{ variables.environment }}
                    alwaysRunImport: ${{ parameters.alwaysRunImport }}
  - stage: Test
    dependsOn: [Dev]
    variables:
      - template: variables-test.yml
    jobs:
      - deployment: "Deploy_to_Test"
        displayName: Test Deployment
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        environment: ${{ variables.deploymentEnvironment }}
        pool:
          name: WAPol Management VMs
          demands:
            - environment -equals ${{ variables.vmEnvironment }}
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: taskgroup-data-import.yml
                  parameters:
                    environment: ${{ variables.environment }}
                    alwaysRunImport: ${{ parameters.alwaysRunImport }}
  - stage: Sys
    dependsOn: [Test]
    variables:
      - template: variables-sys.yml
    jobs:
      - deployment: "Deploy_to_SYS"
        displayName: SYS Deployment
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        environment: ${{ variables.deploymentEnvironment }}
        pool:
          name: WAPol Management VMs
          demands:
            - environment -equals ${{ variables.vmEnvironment }}
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: taskgroup-data-import.yml
                  parameters:
                    environment: ${{ variables.environment }}
                    alwaysRunImport: ${{ parameters.alwaysRunImport }}
  - stage: USR
    dependsOn: [Sys]
    variables:
      - template: variables-usr.yml
    jobs:
      - deployment: "Deploy_to_USR"
        displayName: USR Deployment
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        environment: ${{ variables.deploymentEnvironment }}
        pool:
          name: WAPol Management VMs
          demands:
            - environment -equals ${{ variables.vmEnvironment }}
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: taskgroup-data-import.yml
                  parameters:
                    environment: ${{ variables.environment }}
                    alwaysRunImport: ${{ parameters.alwaysRunImport }}

  - stage: USR2
    dependsOn: [Sys]
    variables:
      - template: variables-usr2.yml
    jobs:
      - deployment: "Deploy_to_USR"
        displayName: USR Deployment
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        environment: ${{ variables.deploymentEnvironment }}
        pool:
          name: WAPol Management VMs
          demands:
            - environment -equals ${{ variables.vmEnvironment }}
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: taskgroup-data-import.yml
                  parameters:
                    environment: ${{ variables.environment }}
                    alwaysRunImport: ${{ parameters.alwaysRunImport }}

  - stage: TRAINING
    dependsOn: [USR]
    variables:
      - template: variables-training.yml
    jobs:
      - deployment: "Deploy_to_TRAINING"
        displayName: TRAINING Deployment
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        environment: ${{ variables.deploymentEnvironment }}
        pool:
          name: WAPol Management VMs
          demands:
            - environment -equals ${{ variables.vmEnvironment }}
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: taskgroup-data-import.yml
                  parameters:
                    environment: ${{ variables.environment }}
                    alwaysRunImport: ${{ parameters.alwaysRunImport }}

  - stage: TRAINING2
    dependsOn: [USR]
    variables:
      - template: variables-training2.yml
    jobs:
      - deployment: "Deploy_to_TRAINING"
        displayName: TRAINING Deployment
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        environment: ${{ variables.deploymentEnvironment }}
        pool:
          name: WAPol Management VMs
          demands:
            - environment -equals ${{ variables.vmEnvironment }}
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: taskgroup-data-import.yml
                  parameters:
                    environment: ${{ variables.environment }}
                    alwaysRunImport: ${{ parameters.alwaysRunImport }}

  - stage: PROD
    dependsOn: [USR]
    variables:
      - template: variables-prod.yml
    jobs:
      - deployment: "Deploy_to_PROD"
        displayName: PROD Deployment
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        environment: ${{ variables.deploymentEnvironment }}
        pool:
          name: WAPol Management VMs
          demands:
            - environment -equals ${{ variables.vmEnvironment }}
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: taskgroup-data-import.yml
                  parameters:
                    environment: ${{ variables.environment }}
                    alwaysRunImport: ${{ parameters.alwaysRunImport }}
