variables:
  - name: azAppID
    value: $(wapol-azure-service-principal-app-id-usr)
  - name: azAppSecret
    value: $(wapol-azure-service-principal-app-secret-usr)
  - name: azSubId
    value: $(wapol-azure-service-principal-subscription-id-usr)

  #Horizontal Pod Autoscaler
  - name: minReplicas
    value: "2"
  - name: maxReplicas
    value: "3"
  - name: targetCPUAverageUtilization
    value: "90"

  #pipeline variables that are required to run the pipeline.
  - name: serviceName
    value: "core-situationalalerts-subscriber"
  - name: buildNumber
    value: "$(resources.pipeline.pscore-core-situationalalerts-subscriber.runName)"
  - name: environmentName
    value: "training"
  - name: workingDir
    value: "$(System.DefaultWorkingDirectory)/kubernetes/shared/core-situational-alerts-subscriber"
  - name: workloadIdentityEnabled
    value: "false"
  #container variables that are unique to the service
  - name: ImageName
    value: "pscore-core-situationalalerts-subscriber:$(buildNumber)"
  - name: ImageRepo
    value: $(destinationAcr)/$(environmentName)
  - name: sourceAcr
    value: "mdcpscoreacrdev.azurecr.io"
  - name: destinationAcr
    value: "wapolwpmacrdev.azurecr.io"
  - name: acr_azAppID
    value: $(azAppID)
  - name: acr_azAppSecret
    value: $(azAppSecret)
  - name: acr_azSubId
    value: $(azSubId)
  - name: acr_azTenantID
    value: $(azTenantID)
  - name: k8sClusterName
    value: "wapol-wpm-aks-cluster-2-usr"
  - name: resourceGroup
    value: "wapol-wpm-rg-2-usr"
  - name: NoOfReplica #2 replicas in usr and prod
    value: "2"

  #env variables
  - name: "AzureMessageBusSettingsConnectionString"
    value: "$(wapol-azure-servicebus-namespace2-connectionstring-usr-2)"
  - name: "SubscriberConfigurationUseRabbit"
    value: "false"
  - name: "SubscriberConfigurationUseAzureMessageBus"
    value: "true"
  - name: "SubscriberConfigurationVerboseSendPipelineLogging"
    value: "true"
  - name: "SubscriberConfigurationNumberOfReceiveWorkers"
    value: "10"
  - name: "SubscriberConfigurationMaxParallelism"
    value: "10"
  - name: "SubscriberConfigurationAzureMessageBusSettingsSubscriptionName"
    value: "$(serviceName)-$(environmentName)"
  - name: "SubscriberConfigurationAzureMessageBusSettingsEnvironmentName"
    value: "$(environmentName)"
  - name: "SubscriberConfigurationAzureMessageBusSettingsMessagePeekLockDurationInSeconds"
    value: "60"
  - name: "CadCacheApiBaseUri"
    value: "http://backend-core-cad-svc-$(environmentName)"
  - name: "DeviceApiBaseUri"
    value: "http://backend-core-cad-svc-$(environmentName)"
  - name: "SituationalAlertConfigurationBaseUri"
    value: "http://backend-core-situationalalerts-svc-$(environmentName)"
  - name: "SituationalAlertConfigurationExcludeBookedOffDevices"
    value: "true"
  - name: "SituationalAlertConfigurationNotificationSound"
    value: "chime"
  - name: "AlertGenerationGenerationSource"
    value: "internal"
  - name: "AlertGenerationDuressIsForeground"
    value: "true"
  - name: "AlertGenerationDuressTargetType"
    value: "Radius"
  - name: "AlertGenerationDuressRadiusInM"
    value: "5000"
  - name: "AlertGenerationDuressExpiryTimeInMinutes"
    value: "30"
  - name: "AlertGenerationDuressExcludeCallsignDevicesOutsideSearchRadius"
    value: "false"
  - name: "AlertGenerationEntityMaxAlertLevel"
    value: "20"
  - name: "AlertGenerationEntityTargetType"
    value: "Resource"
  - name: "AlertGenerationEntityRadiusInM"
    value: "5000"
  - name: "AlertGenerationEntityExcludeTriggeringOfficer"
    value: "false"
  - name: "AlertGenerationEntityExcludeCallsignDevicesOutsideSearchRadius"
    value: "false"
  - name: "AlertGenerationDuressEnabled"
    value: "true"
  - name: "AlertGenerationEntityEnabled"
    value: "true"

  # push notifications
  - name: "PushNotificationClientPushNotificationServiceUrl"
    value: "http://backend-core-push-notification-svc-$(environmentName)/push"
  - name: "PushNotificationClientNotificationSourceName"
    value: "pscore"
  - name: "PushNotificationClientNotificationPayloadVersionsAndroid"
    value: 2
  - name: "PushNotificationClientNotificationPayloadVersionsIOS"
    value: 1
  - name: "PushNotificationClientDefaultNotificationsToForeground"
    value: true

  #serilog
  - name: SerilogMinimumLevelDefault
    value: "Error"
  - name: SerilogMinimumLevelOverrideMicrosoft
    value: "Error"
  - name: SerilogMinimumLevelOverrideSystem
    value: "Error"
  - name: SerilogMinimumLevelOverrideElasticApm
    value: "Error"

  #Logging Context
  - name: StaticLoggingContextImageName
    value: "$(ImageName)"

  #elastic APM
  - name: ElasticApmEnabled
    value: "true"
  - name: ElasticApmServerUrl
    value: "http://apm-server-svc-usr.monitoring-services:8200/"
  - name: ElasticApmServiceName
    value: "$(serviceName)"
  - name: ElasticApmServiceVersion
    value: "$(resources.pipeline.pscore-core-situationalalerts-subscriber.runName)"
  - name: ElasticApmTransactionSampleRate
    value: "1.0"
  - name: ElasticApmEnvironment
    value: "$(environmentName)"
