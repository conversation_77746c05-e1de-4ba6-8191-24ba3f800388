variables:
  #pipeline variables that are required to run the pipeline.
  - name: serviceName
    value: "core-situationalalerts-subscriber"
  - name: buildNumber
    value: "$(resources.pipeline.pscore-core-situationalalerts-subscriber.runName)"
  - name: environmentName
    value: "dev"
  - name: workingDir
    value: "$(System.DefaultWorkingDirectory)/kubernetes/shared/core-situational-alerts-subscriber"

  #container variables that are unique to the service
  - name: ImageRepo
    value: "mdcpscoreacrdev.azurecr.io"
  - name: ImageName
    value: "pscore-core-situationalalerts-subscriber:$(buildNumber)"
  - name: k8sClusterName
    value: "wapol-wpm-aks-cluster-dev"
  - name: resourceGroup
    value: "wapol-wpm-rg-dev"

  #env variables
  - name: workloadIdentityEnabled
    value: "true"
  - name: WorkloadIdentityClientId
    value: "$(wapol-azure-aks-workloadidentity-clientid-dev)"
  - name: "AzureMessageBusSettingsConnectionString"
    value: "$(azure-service-bus-conn-str-dev)"
  - name: "SubscriberConfigurationUseRabbit"
    value: "false"
  - name: "SubscriberConfigurationUseAzureMessageBus"
    value: "true"
  - name: "SubscriberConfigurationVerboseSendPipelineLogging"
    value: "true"
  - name: "SubscriberConfigurationNumberOfReceiveWorkers"
    value: "10"
  - name: "SubscriberConfigurationMaxParallelism"
    value: "10"
  - name: "SubscriberConfigurationAzureMessageBusSettingsSubscriptionName"
    value: "$(serviceName)-$(environmentName)"
  - name: "SubscriberConfigurationAzureMessageBusSettingsEnvironmentName"
    value: "$(environmentName)"
  - name: "SubscriberConfigurationAzureMessageBusSettingsMessagePeekLockDurationInSeconds"
    value: "60"
  - name: "CadCacheApiBaseUri"
    value: "http://backend-core-cad-svc-$(environmentName)"
  - name: "DeviceApiBaseUri"
    value: "http://backend-core-cad-svc-$(environmentName)"
  - name: "SituationalAlertConfigurationBaseUri"
    value: "http://backend-core-situationalalerts-svc-$(environmentName)"
  - name: "SituationalAlertConfigurationExcludeBookedOffDevices"
    value: "true"
  - name: "SituationalAlertConfigurationNotificationSound"
    value: "chime"
  - name: "AlertGenerationGenerationSource"
    value: "internal"
  - name: "AlertGenerationDuressIsForeground"
    value: "true"
  - name: "AlertGenerationDuressTargetType"
    value: "Radius"
  - name: "AlertGenerationDuressRadiusInM"
    value: "5000"
  - name: "AlertGenerationDuressExpiryTimeInMinutes"
    value: "30"
  - name: "AlertGenerationDuressExcludeCallsignDevicesOutsideSearchRadius"
    value: "false"
  - name: "AlertGenerationEntityMaxAlertLevel"
    value: "20"
  - name: "AlertGenerationEntityTargetType"
    value: "Resource"
  - name: "AlertGenerationEntityRadiusInM"
    value: "5000"
  - name: "AlertGenerationEntityExcludeTriggeringOfficer"
    value: "false"
  - name: "AlertGenerationEntityExcludeCallsignDevicesOutsideSearchRadius"
    value: "false"
  - name: "AlertGenerationDuressEnabled"
    value: "true"
  - name: "AlertGenerationEntityEnabled"
    value: "true"

  # push notifications
  - name: "PushNotificationClientPushNotificationServiceUrl"
    value: "http://backend-core-push-notification-svc-$(environmentName)/push"
  - name: "PushNotificationClientNotificationSourceName"
    value: "pscore"
  - name: "PushNotificationClientNotificationPayloadVersionsAndroid"
    value: 2
  - name: "PushNotificationClientNotificationPayloadVersionsIOS"
    value: 1
  - name: "PushNotificationClientDefaultNotificationsToForeground"
    value: true

  #serilog
  - name: SerilogMinimumLevelDefault
    value: "Debug"
  - name: SerilogMinimumLevelOverrideMicrosoft
    value: "Error"
  - name: SerilogMinimumLevelOverrideSystem
    value: "Error"
  - name: SerilogMinimumLevelOverrideElasticApm
    value: "Error"

  #Logging Context
  - name: StaticLoggingContextImageName
    value: "$(ImageName)"

  #elastic APM
  - name: ElasticApmEnabled
    value: "true"
  - name: ElasticApmServerUrl
    value: "$(es-apm-url-dev)"
  - name: ElasticApmServiceName
    value: "$(serviceName)"
  - name: ElasticApmServiceVersion
    value: "$(resources.pipeline.pscore-core-situationalalerts-subscriber.runName)"
  - name: ElasticApmTransactionSampleRate
    value: "1.0"
  - name: ElasticApmEnvironment
    value: "$(environmentName)"
