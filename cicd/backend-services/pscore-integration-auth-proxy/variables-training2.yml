variables:
  #pipeline variables that are required to run the pipeline.

  #azure access variables
  - name: azAppID
    value: $(wapol-azure-service-principal-app-id-usr)
  - name: azAppSecret
    value: $(wapol-azure-service-principal-app-secret-usr)
  - name: azSubId
    value: $(wapol-azure-service-principal-subscription-id-usr)

  #service variables
  - name: serviceName
    value: "pscore-integration-auth-proxy"
  - name: buildNumber
    value: "$(resources.pipeline.pscore-integration-auth-proxy.runName)"
  - name: environmentName
    value: "training"
  - name: workingDir
    value: "$(System.DefaultWorkingDirectory)/kubernetes/shared/pscore-integration-auth-proxy"

  #Horizontal Pod Autoscaler
  - name: minReplicas
    value: "2"
  - name: maxReplicas
    value: "3"
  - name: targetCPUAverageUtilization
    value: "80"

  #container variables that are unique to the service
  - name: ImageRepo
    value: "mdcpscoreacrdev.azurecr.io"
  - name: ImageName
    value: "pscore-integration-auth-proxy:$(buildNumber)"
  - name: sourceAcr
    value: "mdcpscoreacrdev.azurecr.io"
  - name: destinationAcr
    value: "wapolwpmacrdev.azurecr.io"
  - name: acr_azAppID
    value: $(azAppID)
  - name: acr_azAppSecret
    value: $(azAppSecret)
  - name: acr_azSubId
    value: $(azSubId)
  - name: acr_azTenantID
    value: $(azTenantID)
  - name: k8sClusterName
    value: "wapol-wpm-aks-cluster-2-usr"
  - name: resourceGroup
    value: "wapol-wpm-rg-2-usr"
  - name: NoOfReplica #2 replicas in usr and prod
    value: "2"

  #env variables
  - name: appConfigurationwaPolApibaseUrl
    value: "https://motorolapscoregateway-trn.police.wa.gov.au"
  - name: appConfigurationwaPolApitoken
    value: "$(wapol-api-token-training)"

  #serilog
  - name: SerilogMinimumLevel
    value: "Error"
  - name: SerilogMinimumLevelDefault
    value: "Error"
  - name: SerilogMinimumLevelOverrideMicrosoft
    value: "Error"
  - name: SerilogMinimumLevelOverrideSystem
    value: "Error"
  - name: SerilogMinimumLevelOverrideElasticApm
    value: "Error"
  - name: SerilogUsing
    value: "Serilog.Sinks.Console"

  #Logging Context
  - name: StaticLoggingContextImageName
    value: "$(ImageName)"

  #elastic APM
  - name: ElasticApmEnabled
    value: "true"
  - name: ElasticApmServerUrl
    value: "$(wapol-wpm-pe-apm-url-usr)"
  - name: ElasticApmServiceName
    value: "$(serviceName)"
  - name: ElasticApmServiceVersion
    value: "$(buildNumber)"
  - name: ElasticApmTransactionSampleRate
    value: "1.0"
  - name: ElasticApmEnvironment
    value: "$(environmentName)"

  # reports lite
  - name: ProxyRoutesReportSubmitDestinationUrl
    value: "$(appConfigurationwaPolApibaseUrl)/OneForceCore/v1/forms"
  - name: ProxyRoutesReportSubmitOutputMethod
    value: "POST"
  - name: ProxyRoutesReportSubmitCopyAllHeaders
    value: "false"
  - name: ProxyRoutesReportSubmitUnwrapRequestPayload
    value: "true"
  - name: ProxyRoutesReportSubmitAuthMode
    value: "ApiKey"
  - name: ProxyRoutesReportSubmitApiKeySettingsApiKeyHeaderName
    value: "X-Api-Key"
  - name: ProxyRoutesReportSubmitApiKeySettingsSchemaName
    value: "Apikey"
  - name: ProxyRoutesReportSubmitApiKeySettingsApiKeyValue
    value: "$(wapol-api-token-training)"
  #reports lite headers
  - name: ProxyRoutesReportSubmitHeaderMappingsUserIdMappingStyle
    value: "Copy"
  - name: ProxyRoutesReportSubmitHeaderMappingsUserIdSourceHeaderName
    value: "X-User-ID"
  - name: ProxyRoutesReportSubmitHeaderMappingsLongitudeMappingStyle
    value: "Copy"
  - name: ProxyRoutesReportSubmitHeaderMappingsTimestampMappingStyle
    value: "Copy"
  - name: ProxyRoutesReportSubmitHeaderMappingsTimestampSourceHeaderName
    value: "X-GPS-Timestamp"
  - name: ProxyRoutesReportSubmitHeaderMappingsLongitudeSourceHeaderName
    value: "X-GPS-Longitude"
  - name: ProxyRoutesReportSubmitHeaderMappingsLatitudeMappingStyle
    value: "Copy"
  - name: ProxyRoutesReportSubmitHeaderMappingsLatitudeSourceHeaderName
    value: "X-GPS-Latitude"
  - name: ProxyRoutesReportSubmitHeaderMappingsSourceAppFunctionMappingStyle
    value: "Copy"
  - name: ProxyRoutesReportSubmitHeaderMappingsSourceAppFunctionSourceHeaderName
    value: "X-Device-View-Context"
  - name: ProxyRoutesReportSubmitHeaderMappingsOcpApimSubscriptionKeyMappingStyle
    value: "Static"
  - name: ProxyRoutesReportSubmitHeaderMappingsOcpApimSubscriptionKeyStaticValue
    value: "$(wapol-api-token-training)"
  - name: ProxyRoutesReportSubmitHeaderMappingsCorrelationIdMappingStyle
    value: "Copy"
  - name: ProxyRoutesReportSubmitHeaderMappingsCorrelationIdSourceHeaderName
    value: "X-Transaction-ID"
  - name: ProxyRoutesReportSubmitHeaderMappingsDeviceIpMappingStyle
    value: "SourceIP"
  - name: ProxyRoutesReportSubmitHeaderMappingsDeviceNameMappingStyle
    value: "Copy"
  - name: ProxyRoutesReportSubmitHeaderMappingsDeviceNameSourceHeaderName
    value: "X-Device-ID"
  - name: ProxyRoutesReportSubmitHeaderMappingsSystemMappingStyle
    value: "Static"
  - name: ProxyRoutesReportSubmitHeaderMappingsSystemStaticValue
    value: "OFCORE"
  - name: ProxyRoutesReportSubmitHeaderMappingsAuthorizationMappingStyle
    value: "Copy"
  - name: ProxyRoutesReportSubmitHeaderMappingsAuthorizationSourceHeaderName
    value: "Authorization"
  - name: ProxyRoutesReportSubmitHeaderMappingsCallsignMappingStyle
    value: "Copy"
  - name: ProxyRoutesReportSubmitHeaderMappingsCallsignSourceHeaderName
    value: "X-Callsign"
  - name: ProxyRoutesReportSubmitResponseHeaders
    value: "X-Status"
