variables:
  #pipeline variables
  #contains variables that required to run the pipeline.

  #azure access variables
  - name: azAppID
    value: $(wapol-azure-service-principal-app-id-usr)
  - name: azAppSecret
    value: $(wapol-azure-service-principal-app-secret-usr)
  - name: azSubId
    value: $(wapol-azure-service-principal-subscription-id-usr)

  #service variables
  - name: serviceName
    value: "integration-audit-subscriber"
  - name: buildNumber
    value: "$(resources.pipeline.integration-audit-subscriber.runName)"
  - name: environmentName
    value: "usr"
  - name: workingDir
    value: "$(System.DefaultWorkingDirectory)/kubernetes/shared/$(serviceName)"
  - name: azAppID
    value: $(wapol-azure-service-principal-app-id-usr)
  - name: azAppSecret
    value: $(wapol-azure-service-principal-app-secret-usr)
  - name: azSubId
    value: $(wapol-azure-service-principal-subscription-id-usr)

  #Horizontal Pod Autoscaler
  - name: minReplicas
    value: "2"
  - name: maxReplicas
    value: "3"
  - name: targetCPUAverageUtilization
    value: "80"

  #container variables that are unique to the service
  - name: ImageName
    value: "$(serviceName):$(buildNumber)"
  - name: ImageRepo
    value: $(destinationAcr)/$(environmentName)
  - name: sourceAcr
    value: "mdcpscoreacrdev.azurecr.io"
  - name: destinationAcr
    value: "wapolwpmacrdev.azurecr.io"
  - name: acr_azAppID
    value: $(azAppID)
  - name: acr_azAppSecret
    value: $(azAppSecret)
  - name: acr_azSubId
    value: $(azSubId)
  - name: acr_azTenantID
    value: $(azTenantID)
  - name: k8sClusterName
    value: "wapol-wpm-aks-cluster-2-usr"
  - name: resourceGroup
    value: "wapol-wpm-rg-2-usr"
  - name: NoOfReplica #2 replicas in usr and prod
    value: "2"

  - name: AppConfigurationAzureSubscriberVerboseSendPipelineLogging
    value: "false"
  - name: AppConfigurationAzureSubscriberAzureMessageBusSettingsConnectionString
    value: "$(wapol-azure-servicebus-namespace2-connectionstring-usr-2)"
  - name: AppConfigurationAzureSubscriberAzureMessageBusSettingsEnvironmentName
    value: "$(environmentName)"
  - name: AppConfigurationAzureSubscriberAzureMessageBusSettingsSubscriptionName
    value: "subscription-audit-aks-$(environmentName)"
  - name: AppConfigurationWaPolApibaseUrl
    value: "https://motorolapscoregateway-usr.police.wa.gov.au"
  - name: AppConfigurationWaPolApitoken
    value: "$(wapol-auditrak-api-token-usr)"
  - name: AppConfigurationWaPolApibasicAuthusername
    value: "$(wapol-auditrak-api-user-usr)"
  - name: AppConfigurationWaPolApibasicAuthpassword
    value: "$(wapol-auditrak-api-password-usr)"
  - name: AppConfigurationWaPolApireturnClientRequestPayloadSent
    value: "false"
  - name: AppConfigurationEnvironment
    value: "usr"

  #istio configuration
  - name: istioSidecarInjection
    value: "false"

  #log configuration
  - name: LogConfigurationEnvironment
    value: "usr"
  - name: LogConfigurationCleanData
    value: "true"

  #serilog
  - name: SerilogMinimumLevelDefault
    value: "Error"
  - name: SerilogMinimumLevelOverrideMicrosoft
    value: "Error"
  - name: SerilogMinimumLevelOverrideSystem
    value: "Error"
  - name: SerilogMinimumLevelOverrideElasticApm
    value: "Error"

  #elastic APM
  - name: ElasticApmEnabled
    value: "true"
  - name: ElasticApmServerUrl
    value: "http://apm-server-svc-usr.monitoring-services:8200/"
  - name: ElasticApmServiceName
    value: "$(serviceName)"
  - name: ElasticApmServiceVersion
    value: "$(buildNumber)"
  - name: ElasticApmTransactionSampleRate
    value: "1.0"
  - name: ElasticApmEnvironment
    value: "$(environmentName)"
