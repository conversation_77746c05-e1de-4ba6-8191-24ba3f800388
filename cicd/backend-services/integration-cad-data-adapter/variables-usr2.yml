variables:
  #pipeline variables
  #contains variables that required to run the pipeline.

  #azure access variables
  - name: azAppID
    value: $(wapol-azure-service-principal-app-id-usr)
  - name: azAppSecret
    value: $(wapol-azure-service-principal-app-secret-usr)
  - name: azSubId
    value: $(wapol-azure-service-principal-subscription-id-usr)

  #service variables
  - name: serviceName
    value: "integration-cad-data-adapter"
  - name: buildNumber
    value: "$(resources.pipeline.integration-cad-data-adapter.runName)"
  - name: environmentName
    value: "usr"
  - name: workingDir
    value: "$(System.DefaultWorkingDirectory)/kubernetes/shared/integration-cad-data-adapter"

  #Container variables unique to service
  - name: ImageName
    value: "$(serviceName):$(buildNumber)"
  - name: ImageRepo
    value: $(destinationAcr)/$(environmentName)
  - name: sourceAcr
    value: "wapolwpmacrdev.azurecr.io"
  - name: destinationAcr
    value: "wapolwpmacrdev.azurecr.io"
  - name: acr_azAppID
    value: $(azAppID)
  - name: acr_azAppSecret
    value: $(azAppSecret)
  - name: acr_azSubId
    value: $(azSubId)
  - name: acr_azTenantID
    value: $(azTenantID)
  - name: k8sClusterName
    value: "wapol-wpm-aks-cluster-2-usr"
  - name: resourceGroup
    value: "wapol-wpm-rg-2-usr"
  - name: K8sNamespace
    value: "backend-integration-$(environmentName)"

  - name: ConfigurationSettingsCachedDbUrl #core-cad-svc url
    value: '"http://backend-core-cad-for-dequeue-svc-$(environmentName).backend-$(environmentName).svc.cluster.local"'
  - name: ConfigurationSettingsCadDefaultCallsign
    value: "USRTEST"
  - name: ConfigurationSettingsQueryTimeoutInSeconds
    value: '"60"'
  - name: ConfigurationSettingsServiceBusConnectionString
    value: "$(wapol-azure-servicebus-namespace2-connectionstring-usr-2)"
  - name: ConfigurationSettingsServiceBusQueueName
    value: "queue-cad-$(environmentName)"
  - name: ConfigurationSettingsTcpServerIp
    value: '"***********"'
  - name: ConfigurationSettingsTcpServerPort
    value: '"10049"'
  - name: ConfigurationSettingsTimeZoneId
    value: "Australia/Perth"
  - name: ConfigurationSettingsCadPollingIntervalInSeconds
    value: '"30"'
  - name: NoOfReplica
    value: "1"

  # health checks
  - name: ReadinessProbeInitDelaySeconds
    value: "30"
  - name: ReadinessProbePeriodSeconds
    value: "30"
  - name: ReadinessProbeTimeoutSeconds
    value: "20"

  #istio configuration
  - name: istioSidecarInjection
    value: "false"

  #log configuration
  - name: LogConfigurationEnvironment
    value: "usr"
  - name: LogConfigurationCleanData
    value: "true"

  #graph API
  - name: MicrosoftGraphApiclientId
    value: "$(microsoft-graph-api-client-id-usr)"
  - name: MicrosoftGraphApiclientSecret
    value: "$(microsoft-graph-api-client-secret-usr)"
  - name: MicrosoftGraphApiGraphEndpoint
    value: "https://graph.microsoft.com"
  - name: MicrosoftGraphApiGraphVersion
    value: "v1.0"
  - name: MicrosoftGraphApiInstance
    value: "https://login.microsoftonline.com"
  - name: MicrosoftGraphApitenantId
    value: "$(microsoft-graph-api-tenant-id-usr)"
  - name: MicrosoftGraphApiCachingEnabled
    value: "true"
  - name: MicrosoftGraphApiCachingSlidingExpirationInMinutes
    value: "240"

  #serilog
  - name: SerilogMinimumLevelDefault
    value: "Information"
  - name: SerilogMinimumLevelOverrideMicrosoft
    value: "Error"
  - name: SerilogMinimumLevelOverrideSystem
    value: "Error"
  - name: SerilogMinimumLevelOverrideElasticApm
    value: "Error"
  - name: SerilogUsing
    value: "Serilog.Sinks.Console"

  #elastic APM
  - name: ElasticApmEnabled
    value: "true"
  - name: ElasticApmEnvironment
    value: "$(environmentName)"
  - name: ElasticApmServiceVersion
    value: "$(buildNumber)"
  - name: ElasticApmServerUrl
    value: "http://apm-server-svc-usr.monitoring-services:8200/"
  - name: ElasticApmServiceName
    value: "$(serviceName)"
  - name: ElasticApmTransactionSampleRate
    value: "1.0"
