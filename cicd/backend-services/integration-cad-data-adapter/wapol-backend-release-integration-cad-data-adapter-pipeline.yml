name: $(date:yyyyMMdd)$(rev:.r)

parameters:
  - name: image
    displayName: Pool Image
    default: ubuntu-latest
    values:
      - windows-latest
      - windows-2022
      - ubuntu-latest
      - ubuntu-20.04
      - macOS-latest
      - macOS-10.14
  - name: test
    displayName: Run Tests?
    type: boolean
    default: false

trigger: #trigger for self repo (build yml repo)
  batch: true
  branches:
    include:
      - develop
  paths:
    include:
      - cicd/backend-services/integration-cad-data-adapter/*
      - cicd/backend-services/templates/variables-backend-aks.yml
      - cicd/backend-services/templates/taskgroup-backend-aks-deployment.yml
      - kubernetes/shared/integration-cad-data-adapter/*
    exclude:
      - cicd/README

pr:
  branches:
    include:
      - develop
  paths:
    include:
      - cicd/backend-services/integration-cad-data-adapter/*
      - cicd/backend-services/templates/variables-backend-aks.yml
      - cicd/backend-services/templates/taskgroup-backend-aks-deployment.yml
      - kubernetes/shared/integration-cad-data-adapter/*
    exclude:
      - cicd/README

resources:
  pipelines:
    - pipeline: integration-cad-data-adapter # Name of the pipeline resource
      project: Gridstone
      source: wapol-backend-build-integration-cad-data-adapter # Name of the triggering pipeline
      branch: development
      trigger:
        branches:
          include:
            - refs/heads/development
            - refs/heads/staging
            - refs/heads/production
            - refs/heads/hotfix/*
            - refs/heads/maintenance

variables:
  - group: Gridstone-WAPol-non-production
  - name: "isPullRequest"
    value: ${{ne(variables['Build.Reason'],'PullRequest')}}
  - name: ciCommitId
    value: "$(resources.pipeline.integration-cad-data-adapter.sourceCommit)"
  - name: cdCommitId
    value: "$(Build.SourceVersion)"

#main branch
stages:
  - stage: dev
    displayName: dev
    dependsOn: []
    pool:
      vmImage: ${{ parameters.image }}
    variables:
      - template: ../../templates/variables-backend-aks.yml
      - template: variables-dev.yml
    jobs:
      - deployment: "Deploy_to_AKS"
        displayName: "Deploy to AKS"
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        environment: "wapol-wpm-dev"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: ../../templates/taskgroup-backend-aks-deployment.yml

  - stage: test
    displayName: test
    dependsOn: dev
    condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'))
    pool:
      vmImage: ${{ parameters.image }}
    variables:
      - template: ../../templates/variables-backend-aks.yml
      - template: variables-test.yml
    jobs:
      - deployment: "Deploy_to_AKS"
        displayName: "Deploy to AKS"
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        environment: "wapol-wpm-test"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: ../../templates/taskgroup-backend-aks-deployment.yml

  - stage: sys
    displayName: sys
    dependsOn: test
    condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'), ne(variables['resources.pipeline.integration-cad-data-adapter.sourceBranch'], 'refs/heads/maintenance'))
    pool:
      vmImage: ${{ parameters.image }}
    variables:
      - template: ../../templates/variables-backend-aks.yml
      - template: variables-sys.yml
    jobs:
      - deployment: "Deploy_to_AKS"
        displayName: "Deploy to AKS"
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        environment: "wapol-wpm-sys"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: ../../templates/taskgroup-backend-aks-deployment.yml

  - stage: usr
    displayName: usr
    dependsOn: sys
    condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'), ne(variables['resources.pipeline.integration-cad-data-adapter.sourceBranch'], 'refs/heads/maintenance'))
    pool:
      vmImage: ${{ parameters.image }}
    variables:
      - template: ../../templates/variables-backend-aks.yml
      - template: variables-usr.yml
    jobs:
      - deployment: "Deploy_to_AKS"
        displayName: "Deploy to AKS"
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        environment: "wapol-wpm-usr"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: ../../templates/taskgroup-pe-acr-import-image.yml
                - template: ../../templates/taskgroup-backend-aks-deployment.yml

  - stage: usr2
    displayName: usr2
    dependsOn: sys
    condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'), ne(variables['resources.pipeline.integration-cad-data-adapter.sourceBranch'], 'refs/heads/maintenance'))
    pool:
      vmImage: ${{ parameters.image }}
    variables:
      - template: ../../templates/variables-backend-aks.yml
      - template: variables-usr2.yml
    jobs:
      - deployment: "Deploy_to_AKS"
        displayName: "Deploy to AKS"
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        environment: "wapol-wpm-usr"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: ../../templates/taskgroup-pe-acr-import-image.yml
                - template: ../../templates/taskgroup-backend-aks-deployment.yml

  - stage: prod
    displayName: prod
    dependsOn: usr
    condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'), ne(variables['resources.pipeline.integration-cad-data-adapter.sourceBranch'], 'refs/heads/maintenance'))
    pool:
      vmImage: ${{ parameters.image }}
    variables:
      - template: ../../templates/variables-backend-aks.yml
      - template: variables-prod.yml
    jobs:
      - deployment: "Deploy_to_AKS"
        displayName: "Deploy to AKS"
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        environment: "wapol-wpm-prod"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: ../../templates/taskgroup-pe-acr-import-image.yml
                - template: ../../templates/taskgroup-backend-aks-deployment.yml
      - deployment: "Retain_Build_Pipeline"
        displayName: "Retain Build Pipeline"
        dependsOn: [Deploy_to_AKS]
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        variables:
          - template: variables-build-pipeline-retain-prod.yml
        environment: "wapol-wpm-prod"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: ../../templates/taskgroup-pe-pipeline-retention.yml
      - deployment: "Retain_Release_Pipeline"
        displayName: "Retain Release Pipeline"
        dependsOn: [Deploy_to_AKS]
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        variables:
          - template: variables-release-pipeline-retain-prod.yml
        environment: "wapol-wpm-prod"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: ../../templates/taskgroup-pe-pipeline-retention.yml

  - stage: training
    displayName: training
    dependsOn: usr
    condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'), ne(variables['resources.pipeline.integration-cad-data-adapter.sourceBranch'], 'refs/heads/maintenance'))
    pool:
      vmImage: ${{ parameters.image }}
    variables:
      - template: ../../templates/variables-backend-aks.yml
      - template: variables-training.yml
    jobs:
      - deployment: "Deploy_to_AKS"
        displayName: "Deploy to AKS"
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        environment: "wapol-wpm-training"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: ../../templates/taskgroup-pe-acr-import-image.yml
                - template: ../../templates/taskgroup-backend-aks-deployment.yml

  - stage: training2
    displayName: training2
    dependsOn: usr
    condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'), ne(variables['resources.pipeline.integration-cad-data-adapter.sourceBranch'], 'refs/heads/maintenance'))
    pool:
      vmImage: ${{ parameters.image }}
    variables:
      - template: ../../templates/variables-backend-aks.yml
      - template: variables-training2.yml
    jobs:
      - deployment: "Deploy_to_AKS"
        displayName: "Deploy to AKS"
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        environment: "wapol-wpm-training"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: ../../templates/taskgroup-pe-acr-import-image.yml
                - template: ../../templates/taskgroup-backend-aks-deployment.yml

  #staging branch
  - stage: sys_staging
    displayName: sys_staging
    dependsOn: []
    condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'),eq(variables['resources.pipeline.integration-cad-data-adapter.sourceBranch'], 'refs/heads/staging'))
    pool:
      vmImage: ${{ parameters.image }}
    variables:
      - template: ../../templates/variables-backend-aks.yml
      - template: variables-sys.yml
    jobs:
      - deployment: "Deploy_to_AKS"
        displayName: "Deploy to AKS"
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        environment: "wapol-wpm-sys"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: ../../templates/taskgroup-backend-aks-deployment.yml

  - stage: usr_staging
    displayName: usr_staging
    dependsOn: [sys_staging]
    condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'),eq(variables['resources.pipeline.integration-cad-data-adapter.sourceBranch'], 'refs/heads/staging'))
    pool:
      vmImage: ${{ parameters.image }}
    variables:
      - template: ../../templates/variables-backend-aks.yml
      - template: variables-usr.yml
    jobs:
      - deployment: "Deploy_to_AKS"
        displayName: "Deploy to AKS"
        variables:
          - template: variables-usr-staging.yml
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        environment: "wapol-wpm-usr"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: ../../templates/taskgroup-backend-aks-deployment.yml

  #hotfix branch
  - stage: sys_hotfix
    displayName: sys_hotfix
    dependsOn: []
    condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'),startsWith(variables['resources.pipeline.integration-cad-data-adapter.sourceBranch'], 'refs/heads/hotfix/'))
    pool:
      vmImage: ${{ parameters.image }}
    variables:
      - template: ../../templates/variables-backend-aks.yml
      - template: variables-sys.yml
    jobs:
      - deployment: "Deploy_to_AKS"
        displayName: "Deploy to AKS"
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        environment: "wapol-wpm-sys"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: ../../templates/taskgroup-backend-aks-deployment.yml

  - stage: usr_hotfix
    displayName: usr_hotfix
    dependsOn: []
    condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'),startsWith(variables['resources.pipeline.integration-cad-data-adapter.sourceBranch'], 'refs/heads/hotfix/'))
    pool:
      vmImage: ${{ parameters.image }}
    variables:
      - template: ../../templates/variables-backend-aks.yml
      - template: variables-usr.yml
    jobs:
      - deployment: "Deploy_to_AKS"
        displayName: "Deploy to AKS"
        variables:
          - template: variables-usr-hotfix.yml
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        environment: "wapol-wpm-usr"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: ../../templates/taskgroup-backend-aks-deployment.yml

  #production branch
  - stage: usr_production
    displayName: usr_production
    dependsOn: []
    condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'),eq(variables['resources.pipeline.integration-cad-data-adapter.sourceBranch'], 'refs/heads/production'))
    pool:
      vmImage: ${{ parameters.image }}
    variables:
      - template: ../../templates/variables-backend-aks.yml
      - template: variables-usr.yml
    jobs:
      - deployment: "Deploy_to_AKS"
        displayName: "Deploy to AKS"
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        environment: "wapol-wpm-usr"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: ../../templates/taskgroup-pe-acr-import-image.yml
                - template: ../../templates/taskgroup-backend-aks-deployment.yml

  - stage: prod_production
    displayName: prod_production
    dependsOn: []
    condition: and(succeeded(), ne(variables['Build.Reason'], 'PullRequest'),eq(variables['resources.pipeline.integration-cad-data-adapter.sourceBranch'], 'refs/heads/production'))
    pool:
      vmImage: ${{ parameters.image }}
    variables:
      - template: ../../templates/variables-backend-aks.yml
      - template: variables-prod.yml
    jobs:
      - deployment: "Deploy_to_AKS"
        displayName: "Deploy to AKS"
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        environment: "wapol-wpm-prod"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: ../../templates/taskgroup-pe-acr-import-image.yml
                - template: ../../templates/taskgroup-backend-aks-deployment.yml
      - deployment: "Retain_Build_Pipeline"
        displayName: "Retain Build Pipeline"
        dependsOn: [Deploy_to_AKS]
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        variables:
          - template: variables-build-pipeline-retain-prod.yml
        environment: "wapol-wpm-prod"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: ../../templates/taskgroup-pe-pipeline-retention.yml
      - deployment: "Retain_Release_Pipeline"
        displayName: "Retain Release Pipeline"
        dependsOn: [Deploy_to_AKS]
        continueOnError: false
        timeoutInMinutes: "15"
        cancelTimeoutInMinutes: "15"
        variables:
          - template: variables-release-pipeline-retain-prod.yml
        environment: "wapol-wpm-prod"
        strategy:
          runOnce:
            deploy:
              steps:
                - checkout: self
                - template: ../../templates/taskgroup-pe-pipeline-retention.yml
