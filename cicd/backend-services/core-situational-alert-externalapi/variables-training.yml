variables:
  #pipeline variables that are required to run the pipeline.
  - name: environmentName
    value: "training"
  - name: serviceName
    value: "core-situational-alert-externalapi"
  - name: buildNumber
    value: "$(resources.pipeline.pscore-core-situationalalerts-externalapi.runName)"
  - name: workingDir
    value: "$(System.DefaultWorkingDirectory)/kubernetes/shared/core-situational-alert-externalapi"

  #service variables
  #container variables that are unique to the service
  - name: ImageName
    value: "pscore-core-situationalalerts-externalapi:$(buildNumber)"
  - name: ImageRepo
    value: $(destinationAcr)/$(environmentName)
  - name: sourceAcr
    value: "mdcpscoreacrdev.azurecr.io"
  - name: destinationAcr
    value: "wapolwpmacrdev.azurecr.io"
  - name: acr_azAppID
    value: $(azAppID)
  - name: acr_azAppSecret
    value: $(azAppSecret)
  - name: acr_azSubId
    value: $(azSubId)
  - name: acr_azTenantID
    value: $(azTenantID)
  - name: k8sClusterName
    value: "wapol-wpm-aks-cluster-usr"
  - name: resourceGroup
    value: "wapol-wpm-rg-usr"
  - name: NoOfReplica #3 replicas in usr and prod
    value: "1"

  #env variables
  - name: AppIntegrationExternalAlertServiceUri
    value: ""
  - name: AppIntegrationExternalAlertApiKey
    value: ""
  - name: AppIntegrationExternalAlertApiKeyAuthEnabled
    value: "true"
  - name: AppIntegrationDeviceSearchServiceUri
    value: "http://backend-core-cad-svc-$(environmentName)"
  - name: AppIntegrationDeviceSearchExcludeBookedOffDevices
    value: "true"
  - name: AppIntegrationPushNotificationServiceUri
    value: "http://backend-core-push-notification-svc-$(environmentName)/push"
  - name: AppIntegrationCreateAlertServiceUri
    value: "http://backend-core-situationalalerts-svc-$(environmentName)"
  - name: AuthApiKeyAuthEnabled
    value: "false"
  - name: NotificationsAlertTitleMaxLength
    value: "150"
  - name: NotificationsAlertBodyMaxLength
    value: "500"
  - name: NotificationsSound
    value: "chime"
  - name: NotificationsIsForeground
    value: "true"
  - name: AzureWorkloadIdentityUse
    value: "false"
  - name: WorkloadIdentityClientId
    value: "$(wapol-azure-aks-workloadidentity-clientid-usr)"

  # push notifications
  - name: "PushNotificationClientPushNotificationServiceUrl"
    value: "http://backend-core-push-notification-svc-$(environmentName)/push"
  - name: "PushNotificationClientNotificationSourceName"
    value: "sit-alert-external"
  - name: "PushNotificationClientNotificationPayloadVersionsAndroid"
    value: 2
  - name: "PushNotificationClientNotificationPayloadVersionsIOS"
    value: 1
  - name: "PushNotificationClientDefaultNotificationsToForeground"
    value: true
  - name: "PushNotificationClientEnableNotificationGrouping"
    value: true

  #serilog
  - name: SerilogMinimumLevelDefault
    value: "Error"
  - name: SerilogMinimumLevelOverrideMicrosoft
    value: "Error"
  - name: SerilogMinimumLevelOverrideSystem
    value: "Error"
  - name: SerilogMinimumLevelOverrideElasticApm
    value: "Error"

  #Logging Context
  - name: StaticLoggingContextImageName
    value: "$(ImageName)"

  #elastic APM
  - name: ElasticApmEnabled
    value: "true"
  - name: ElasticApmServerUrl
    value: "http://apm-server-svc-usr.monitoring-services:8200/"
  - name: ElasticApmServiceName
    value: "$(serviceName)"
  - name: ElasticApmServiceVersion
    value: "$(buildNumber)"
  - name: ElasticApmTransactionSampleRate
    value: "1.0"
  - name: ElasticApmEnvironment
    value: "$(environmentName)"
  - name: ElasticApmTraceContinuationStrategy
    value: "restart_external"
