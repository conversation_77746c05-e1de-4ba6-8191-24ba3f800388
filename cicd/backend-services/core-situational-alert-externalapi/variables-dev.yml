variables:
  #pipeline variables that are required to run the pipeline.
  - name: environmentName
    value: "dev"
  - name: serviceName
    value: "core-situational-alert-externalapi"
  - name: buildNumber
    value: "$(resources.pipeline.pscore-core-situationalalerts-externalapi.runName)"
  - name: workingDir
    value: "$(System.DefaultWorkingDirectory)/kubernetes/shared/core-situational-alert-externalapi"

  #container variables that are unique to the service
  - name: ImageRepo
    value: "mdcpscoreacrdev.azurecr.io"
  - name: ImageName
    value: "pscore-core-situationalalerts-externalapi:$(buildNumber)"
  - name: k8sClusterName
    value: "wapol-wpm-aks-cluster-dev"
  - name: resourceGroup
    value: "wapol-wpm-rg-dev"

  #env variables
  - name: AppIntegrationExternalAlertServiceUri
    value: ""
  - name: AppIntegrationExternalAlertApiKey
    value: ""
  - name: AppIntegrationExternalAlertApiKeyAuthEnabled
    value: "true"
  - name: AppIntegrationDeviceSearchServiceUri
    value: "http://backend-core-cad-svc-$(environmentName)"
  - name: AppIntegrationDeviceSearchExcludeBookedOffDevices
    value: "true"
  - name: AppIntegrationPushNotificationServiceUri
    value: "http://backend-core-push-notification-svc-$(environmentName)/push"
  - name: AppIntegrationCreateAlertServiceUri
    value: "http://backend-core-situationalalerts-svc-$(environmentName)"
  - name: AuthApiKeyAuthEnabled
    value: "false"
  - name: NotificationsAlertTitleMaxLength
    value: "150"
  - name: NotificationsAlertBodyMaxLength
    value: "500"
  - name: NotificationsSound
    value: "chime"
  - name: NotificationsIsForeground
    value: "true"
  - name: AzureWorkloadIdentityUse
    value: "false"
  - name: WorkloadIdentityClientId
    value: "$(wapol-azure-aks-workloadidentity-clientid-dev)"

  # push notifications
  - name: "PushNotificationClientPushNotificationServiceUrl"
    value: "http://backend-core-push-notification-svc-$(environmentName)/push"
  - name: "PushNotificationClientNotificationSourceName"
    value: "sit-alert-external"
  - name: "PushNotificationClientNotificationPayloadVersionsAndroid"
    value: 2
  - name: "PushNotificationClientNotificationPayloadVersionsIOS"
    value: 1
  - name: "PushNotificationClientDefaultNotificationsToForeground"
    value: true
  - name: "PushNotificationClientEnableNotificationGrouping"
    value: true

  #serilog
  - name: SerilogMinimumLevelDefault
    value: "Debug"
  - name: SerilogMinimumLevelOverrideMicrosoft
    value: "Error"
  - name: SerilogMinimumLevelOverrideSystem
    value: "Error"
  - name: SerilogMinimumLevelOverrideElasticApm
    value: "Error"

  #Logging Context
  - name: StaticLoggingContextImageName
    value: "$(ImageName)"

  #elastic APM
  - name: ElasticApmEnabled
    value: "true"
  - name: ElasticApmServerUrl
    value: "$(es-apm-url-dev)"
  - name: ElasticApmServiceName
    value: "$(serviceName)"
  - name: ElasticApmServiceVersion
    value: "$(buildNumber)"
  - name: ElasticApmTransactionSampleRate
    value: "1.0"
  - name: ElasticApmEnvironment
    value: "$(environmentName)"
  - name: ElasticApmTraceContinuationStrategy
    value: "restart_external"
