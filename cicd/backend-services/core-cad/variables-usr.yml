variables:
  #pipeline variables
  #contains variables that required to run the pipeline.

  #azure access variables
  - name: azAppID
    value: $(wapol-azure-service-principal-app-id-usr)
  - name: azAppSecret
    value: $(wapol-azure-service-principal-app-secret-usr)
  - name: azSubId
    value: $(wapol-azure-service-principal-subscription-id-usr)

  #service variables
  - name: serviceName
    value: "core-cad"
  - name: buildNumber
    value: "$(resources.pipeline.pscore-core-cad.runName)"
  - name: environmentName
    value: "usr"
  - name: workingDir
    value: "$(System.DefaultWorkingDirectory)/kubernetes/shared/core-cad"

  #container variables that are unique to the service
  - name: ImageName
    value: "pscore-core-cad:$(buildNumber)"
  - name: ImageRepo
    value: $(destinationAcr)/$(environmentName)
  - name: sourceAcr
    value: "mdcpscoreacrdev.azurecr.io"
  - name: destinationAcr
    value: "wapolwpmacrdev.azurecr.io"
  - name: acr_azAppID
    value: $(azAppID)
  - name: acr_azAppSecret
    value: $(azAppSecret)
  - name: acr_azSubId
    value: $(azSubId)
  - name: acr_azTenantID
    value: $(azTenantID)
  - name: k8sClusterName
    value: "wapol-wpm-aks-cluster-usr"
  - name: resourceGroup
    value: "wapol-wpm-rg-usr"
  - name: NoOfReplica #2 replicas in usr and prod
    value: "2"

  #Horizontal Pod Autoscaler
  - name: minReplicas
    value: "2"
  - name: maxReplicas
    value: "3"
  - name: targetCPUAverageUtilization
    value: "90"

  #env variables
  - name: CadCacheDbConnStr
    value: "Server=tcp:$(ServerName),1433;Initial Catalog=$(DatabaseName);Persist Security Info=False;User ID=$(AppSqlUsername);Password=$(AppSqlPassword);MultipleActiveResultSets=False;Encrypt=True;TrustServerCertificate=False;Connection Timeout=30;"
  - name: AppSqlUsername
    value: "app_user_usr"
  - name: AppSqlPassword
    value: "$(sql-passwd-app-user-usr)"
  - name: TenantSettingsTenants0SyncIncludeAllIncidentsAssignedToCallSign
    value: "true"
  - name: TenantSettingsTenants0SyncIncludeUnassignedIncidents
    value: "true"
  - name: TenantSettingsTenants0SyncClosedIncidentSyncThresholdInMinutes
    value: "0"
  - name: TenantSettingsTenants0SyncIncludeDefaultPatrolGroupInWhereCondition
    value: "false"
  - name: TenantSettingsTenants0SyncIncludeStaticResources
    value: "false"
  - name: TenantSettingsTenants0EventsPublishEventTypesSwitchShouldPublishDuressEvents
    value: "true"
  - name: NotificationNoiseGuardEnabled
    value: "false"
  - name: PushNotificationSource
    value: "cad"
  - name: PushNotificationServiceUri
    value: "http://backend-core-push-notification-svc-$(environmentName)/push"
  - name: ASPEnvironment
    value: "NotSet"
  - name: DatabaseCacheDbUseQuerySplittingForResourcesSync
    value: "true"

  - name: TenantSettingsTenants0CadAdapterConfigRequestUri
    value: http://backend-integration-cad-data-adapter-svc-$(environmentName).backend-integration-$(environmentName).svc.cluster.local
  - name: TenantSettingsTenants0CadAdapterConfigRequestPathsBookOff
    value: "resource/bookoff"
  - name: TenantSettingsTenants0CadAdapterConfigRequestPathsBookOn
    value: "resource/bookon"
  - name: TenantSettingsTenants0CadAdapterConfigRequestPathsUpdateLocation
    value: "device/location"
  - name: TenantSettingsTenants0CadAdapterConfigRequestPathsGetResourceDetails
    value: "resource/details"
  - name: TenantSettingsTenants0CadAdapterConfigRequestPathsSetResourceStatus
    value: "resource/status"
  - name: TenantSettingsTenants0CadAdapterConfigRequestPathsUpdateResource
    value: "resource/update"
  - name: TenantSettingsTenants0CadAdapterConfigRequestPathsAssignIncident
    value: "incident/assign"
  - name: TenantSettingsTenants0CadAdapterConfigRequestPathsCreateIncident
    value: "incident/create"
  - name: TenantSettingsTenants0CadAdapterConfigRequestPathsGetIncidentDetails
    value: "incident/details"
  - name: TenantSettingsTenants0CadAdapterConfigRequestPathsFinaliseIncident
    value: "incident/finalise"
  - name: TenantSettingsTenants0CadAdapterConfigRequestPathsUpdateIncident
    value: "incident/update"
  - name: TenantSettingsTenants0CadAdapterConfigExtraHeaders0
    value: "X-GPS-Latitude"
  - name: TenantSettingsTenants0CadAdapterConfigExtraHeaders1
    value: "X-GPS-Longitude"
  - name: TenantSettingsTenants0CadAdapterConfigExtraHeaders2
    value: "X-GPS-Speed"
  - name: TenantSettingsTenants0CadAdapterConfigExtraHeaders3
    value: "X-GPS-Timestamp"
  - name: TenantSettingsTenants0CadAdapterConfigHealthCheckEndpointUrl
    value: "http://backend-integration-cad-data-adapter-svc-$(environmentName).backend-integration-$(environmentName).svc.cluster.local/health"
  - name: TenantSettingsTenants0CadAdapterConfigHealthCheckEnabled
    value: "true"
  - name: TenantSettingsTenants0CadAdapterConfigDuressOverrideCode

    value: "EMER"
  - name: TenantSettingsTenants0TenancyIDs0
    value: ""
  - name: TenantSettingsTenants0CadNotificationOnIncidentStatusUpdatedSetResourceStatus
    value: "false"

  - name: HealthCheckWorkerEnabled
    value: "true"
  - name: BasicAuthUsername
    value: "CoreCadServiceAccount-$(environmentName)"
  - name: BasicAuthPassword
    value: "$(cad-basic-auth-passwd-usr)"
  - name: SkipAuthentication
    value: "false"
  - name: Enabled
    value: "true"

  - name: DuressCode
    value: "EMER"

  #messagebus
  - name: PublishMessages
    value: "true"
  - name: MessagePublisherVerboseSendPipelineLogging
    value: "false"
  - name: MessagePublisherAzureMessageBusSettingsPublishMessages
    value: "true"
  - name: MessagePublisherAzureMessageBusSettingsConnectionString
    value: "$(wapol-azure-servicebus-namespace2-connectionstring-usr)"
  - name: MessagePublisherAzureMessageBusSettingsEnvironmentName
    value: "$(environmentName)"
  - name: workloadIdentityEnabled
    value: "false"

  # push notifications
  - name: "PushNotificationClientPushNotificationServiceUrl"
    value: "http://backend-core-push-notification-svc-$(environmentName)/push"
  - name: "PushNotificationClientNotificationSourceName"
    value: "core-cad"
  - name: "PushNotificationClientNotificationPayloadVersionsAndroid"
    value: 2
  - name: "PushNotificationClientNotificationPayloadVersionsIOS"
    value: 1
  - name: "PushNotificationClientDefaultNotificationsToForeground"
    value: true
  - name: "PushNotificationClientEnableNotificationGrouping"
    value: true

  #serilog
  - name: SerilogMinimumLevelDefault
    value: "Error"
  - name: SerilogMinimumLevelOverrideMicrosoft
    value: "Error"
  - name: SerilogMinimumLevelOverrideSystem
    value: "Error"
  - name: SerilogMinimumLevelOverrideElasticApm
    value: "Error"

  #Logging Context
  - name: StaticLoggingContextImageName
    value: "$(ImageName)"

  #elastic APM
  - name: ElasticApmEnabled
    value: "true"
  - name: ElasticApmServerUrl
    value: "http://apm-server-svc-usr.monitoring-services:8200/"
  - name: ElasticApmServiceName
    value: "$(serviceName)"
  - name: ElasticApmServiceVersion
    value: "$(buildNumber)"
  - name: ElasticApmTransactionSampleRate
    value: "1.0"
  - name: ElasticApmEnvironment
    value: "$(environmentName)"

  #database migration
  - name: azureSubscription
    value: "WAPol-WPM-Non-Production"
  - name: ServerName
    value: "wapol-wpm-sql-usr.database.windows.net"
  - name: DatabaseName
    value: "cadcache-$(environmentName)"
  - name: SqlUsername
    value: "devops_user_usr"
  - name: SqlPassword
    value: "$(sql-passwd-devops-user-usr)"
  - name: DacpacFile
    value: "$(PIPELINE.WORKSPACE)/pscore-core-cad/db/src/PSCore.Core.Cad.Cache.Database/bin/Release/PSCore.Core.Cad.Cache.Database.dacpac"
