resource "azurerm_kubernetes_cluster" "kubernetes_cluster" {
  name                = var.name
  location            = var.location
  dns_prefix          = var.dns_prefix
  resource_group_name = var.resource_group_name
  tags                            = var.tags
  node_resource_group             = var.node_resource_group
  node_os_upgrade_channel   = var.os_upgrade_channel
  image_cleaner_interval_hours = var.image_cleaner_interval_hours

  dynamic "default_node_pool" {
    for_each = var.zone_redundant_enabled == "Enabled" ? [1] : []
    content {
      temporary_name_for_rotation = "tempvmss"
      name                        = var.node_pool_name
      vm_size                     = var.node_pool_vm_size
      auto_scaling_enabled        = var.node_pool_enable_auto_scaling
      node_count                  = var.node_pool_node_count
      max_count                   = var.node_pool_max_count
      min_count                   = var.node_pool_min_count
      max_pods                    = var.node_pool_max_pods
      vnet_subnet_id              = var.node_pool_vnet_subnet_id
      tags                        = var.tags
      zones                       = ["1", "2", "3"]
    }
  }

  dynamic "default_node_pool" {
    for_each = var.zone_redundant_enabled == "Disabled" ? [1] : []
    content {
      name                = var.node_pool_name
      vm_size             = var.node_pool_vm_size
      auto_scaling_enabled = var.node_pool_enable_auto_scaling
      node_count          = var.node_pool_node_count
      max_count           = var.node_pool_max_count
      min_count           = var.node_pool_min_count
      max_pods            = var.node_pool_max_pods
      vnet_subnet_id      = var.node_pool_vnet_subnet_id
      tags                = var.tags
    }
  }

  linux_profile {
    admin_username = var.linux_admin_username

    ssh_key {
      key_data = var.key_data
    }
  }

  identity {
    type = "SystemAssigned"
  }

  network_profile {
    network_plugin    = var.network_plugin
    service_cidr      = var.service_cidr #k8 services cidr
    dns_service_ip    = var.dns_service_ip
    load_balancer_sku = "standard"
    load_balancer_profile {
      outbound_ip_address_ids = var.lb_outbound_ip_address_ids
    }
  }

  dynamic "key_vault_secrets_provider" {
    for_each = var.secret_rotation_enabled == true ? toset([1]) : toset([])
    content {
      secret_rotation_enabled  = var.secret_rotation_enabled
      secret_rotation_interval = var.secret_rotation_interval
    }
  }

  oms_agent {
    log_analytics_workspace_id      = var.log_analytics_workspace_id
    msi_auth_for_monitoring_enabled = var.msi_auth_for_monitoring_enabled
  }

  role_based_access_control_enabled = var.rbac_enabled
  azure_active_directory_role_based_access_control {
    admin_group_object_ids = var.admin_group_object_ids
  }

  workload_identity_enabled = var.workload_identity_enabled
  oidc_issuer_enabled       = var.oidc_issuer_enabled

  key_vault_secrets_provider {
    secret_rotation_interval = "2m"
  }
}
