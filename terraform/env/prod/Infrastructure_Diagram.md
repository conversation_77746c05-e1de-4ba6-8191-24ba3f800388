# WAPOL Production Infrastructure Diagram

## Current vs Target Architecture

### Network Address Space Allocation

#### Australia Southeast (Current AGW Location)
```
Gateway VNet: *************/24
├── AGW Subnet: *************/26 (Current Application Gateway)
└── Available: **************/26 - ***************/26
```

#### Australia East (Target Location + Current AKS)
```
Application VNet: ***********/24, ***********/24
├── AKS Subnet: ***********/24 (Current AKS Cluster)
├── AKS Service CIDR: ***********/24
└── Available: ***********/24

Gateway VNet AU East: TBD (Need to define)
├── Proposed: *************/24
└── New AGW Subnet: *************/26
```

#### Shared Networks
```
Management VNet: *************/24
VPN VNet: *************/24
```

### Current Traffic Flow Issues
```
Internet → Australia Southeast AGW → Cross-Region → Australia East AKS
         (192.168.207.x)                           (10.35.251.x)
```

### Target Traffic Flow (Optimized)
```
Internet → Australia East AGW → Same Region → Australia East AKS
         (192.168.209.x)                      (10.35.251.x)
```

## Detailed Component Mapping

### Application Gateway Configuration
| Component | Current (AU Southeast) | Target (AU East) |
|-----------|----------------------|------------------|
| **Name** | wapol-wpm-agw-prod | wapol-wpm-agw-au-east-prod |
| **Public IP** | wapol-wpm-agw-public-ip-prod | wapol-wpm-agw-au-east-public-ip-prod |
| **Subnet** | *************/26 | *************/26 |
| **VNet** | wapol-wpm-gateway-vnet-prod | wapol-wpm-gateway-vnet-au-east-prod |
| **Domain** | wapol-agw-prod.pscore.cloud | wapol-agw-prod.pscore.cloud (same) |

### Backend Services (No Change Required)
| Service | FQDN | Location | Port |
|---------|------|----------|------|
| **API Service** | wapol-api-internal-prod.pscore.cloud | Australia East AKS | 80 |
| **Kibana Service** | wapol-kibana-internal-prod.pscore.cloud | Australia East AKS | 5601 |

### Health Probes Configuration
| Service | Probe Path | Port | Host |
|---------|------------|------|------|
| **API** | /healthz/ready | 15021 | 127.0.0.1 |
| **Kibana** | / | 5601 | 127.0.0.1 |

### SSL Certificate Configuration
- **Certificate Name**: pscore.cloud.crt
- **Key Vault**: wapol-wpm-kv-main-prod.vault.azure.net
- **Secret ID**: /secrets/pscoreCloudCrt
- **Identity**: User Assigned Identity (to be created for new AGW)

### WAF Policies to Replicate
1. **Global WAF Policy**: wapol-wpm-agw-waf-policy-prod
2. **API WAF Policy**: wapol-wpm-agw-waf-policy-api-prod
   - IP Allowlist for Starlink: 65.181.x.x/32 ranges
   - Geo-blocking (AU only)
   - Backend path allowlist
3. **Kibana WAF Policy**: wapol-wpm-agw-waf-policy-kibana-prod
   - IP restriction: ***********/16
   - Host header validation

### Network Security Groups
#### Current AGW NSG Rules
```
Priority 100: Allow HTTPS (443) from Any
Priority 110: Allow AGW Management (65200-65535) from Any
```

#### New AGW NSG Rules (To Be Created)
```
Priority 100: Allow HTTPS (443) from Any
Priority 110: Allow AGW Management (65200-65535) from Any
```

### VNet Peering Requirements
```
Gateway VNet AU East ↔ Application VNet AU East
├── Allow Virtual Network Access: true
├── Allow Forwarded Traffic: true
└── Use Remote Gateways: false

Application VNet AU East ↔ Management VNet
├── Allow Virtual Network Access: true
├── Allow Forwarded Traffic: true
└── Use Remote Gateways: false

Application VNet AU East ↔ VPN VNet
├── Allow Virtual Network Access: true
├── Allow Forwarded Traffic: true
└── Use Remote Gateways: true
```

## Migration Strategy Visualization

### Phase 1: Parallel Deployment
```
Internet Traffic → DNS: wapol-agw-prod.pscore.cloud
                   Points to: AU Southeast AGW (Current)
                   
AU Southeast AGW (ACTIVE) ──Cross-Region──┐
                                          │
AU East AGW (NEW/STANDBY) ──Same-Region───┤
                                          │
                                          ▼
                                   Australia East AKS
                                   ├── API Service
                                   └── Kibana Service

Host File Testing: <NEW_AGW_IP> wapol-agw-prod.pscore.cloud
```

### Phase 2: DNS Cutover
```
Internet Traffic → DNS: wapol-agw-prod.pscore.cloud
                   Points to: AU East AGW (NEW)
                   
AU Southeast AGW (STANDBY) ──No Traffic────┐
                                          │
AU East AGW (ACTIVE) ──Same-Region────────┤
                                          │
                                          ▼
                                   Australia East AKS
                                   ├── API Service
                                   └── Kibana Service
```

## Resource Naming Convention

### New Resources to Create
```
Resource Group: wapol-wpm-gateway-rg-prod (existing in AU East)
Virtual Network: wapol-wpm-gateway-vnet-au-east-prod
Subnet: wapol-wpm-agw-au-east-subnet-prod
NSG: wapol-wpm-agw-au-east-nsg-prod
Public IP: wapol-wpm-agw-au-east-public-ip-prod
Application Gateway: wapol-wpm-agw-au-east-prod
User Identity: wapol-wpm-agw-au-east-id-prod
WAF Policies:
  - wapol-wpm-agw-au-east-waf-policy-prod
  - wapol-wpm-agw-au-east-waf-policy-api-prod
  - wapol-wpm-agw-au-east-waf-policy-kibana-prod
```

## Performance Benefits Expected

### Latency Improvements
- **Current**: AU Southeast AGW → AU East AKS (~20-30ms cross-region)
- **Target**: AU East AGW → AU East AKS (~1-5ms same-region)
- **Improvement**: 80-90% latency reduction

### Bandwidth and Cost
- **Reduced cross-region data transfer costs**
- **Improved throughput for large payloads**
- **Better user experience for API consumers**

## Security Considerations

### Network Isolation
- New AGW will be in separate subnet with dedicated NSG
- Same WAF policies applied for consistent security posture
- SSL certificate management through existing Key Vault

### Access Control
- User Assigned Identity for Key Vault access
- Same IP allowlists and geo-blocking rules
- Consistent monitoring and alerting setup

## Next Steps

1. **Review and validate the migration plan**
2. **Define specific CIDR blocks for new Gateway VNet in AU East**
3. **Create Terraform configurations for new infrastructure**
4. **Set up testing procedures with host file modifications**
5. **Plan maintenance windows for deployment and cutover**
