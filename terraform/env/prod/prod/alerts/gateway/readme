

#Declare Variables
$SUBSCRIPTION_ID=""
$TENANT_ID = ""
$CLIENT_SECRET = ""
$CLIENT_ID = ""
$ACCESS_KEY = ""
$STORAGE_ACCOUNT = ""

#Login using service principal
az login --service-principal -u $CLIENT_ID -p $CLIENT_SECRET --tenant $TENANT_ID

#Terraform init
terraform init -plugin-dir="../../../../plugins" --backend-config=backend.tfvars -backend-config="storage_account_name=$STORAGE_ACCOUNT" -backend-config="access_key=$ACCESS_KEY"

#Terraform plan
terraform plan -var-file="env.local.tfvars" -var-file="../../../../../../global/global_env.tfvars" -out out.plan

#Apply Terraform plan
terraform apply "out.plan"


