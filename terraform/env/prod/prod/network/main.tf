provider "azurerm" {
  subscription_id = var.subscription_id
  client_id       = var.client_id
  client_secret   = var.client_secret
  tenant_id       = var.tenant_id
  features {}
}

terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "=2.38.0"
    }
  }
}

#Azure Backend
terraform {
  backend "azurerm" {
  }
}

#Define Azure resource group
module "Application_Vnet_Resource_Group" {
  source   = "../../../../modules/base_resources/resource_group"
  name     = var.resource_group_name
  location = var.location_prod_app_rg
  tags     = local.common_tags
}

#Define Azure Application Vnet
module "Application_Vnet" {
  source              = "../../../../modules/network_resources/virtual_network"
  name                = var.application_vnet_name
  location            = var.location_prod_app_rg   #var.location
  resource_group_name = module.Application_Vnet_Resource_Group.name
  address_space       = var.application_vnet_address_space
  tags                = local.common_tags
}

#Define Gateway resource group
module "Gateway_Vnet_Resource_Group" {
  source   = "../../../../modules/base_resources/resource_group"
  name     = var.gateway_resource_group_name
  location = var.location
  tags     = local.common_tags
}

#Define Gateway Vnet
module "Gateway_Vnet" {
  source              = "../../../../modules/network_resources/virtual_network"
  name                = var.gateway_vnet_name
  location            = var.location
  resource_group_name = module.Gateway_Vnet_Resource_Group.name
  address_space       = var.gateway_vnet_address_space
  tags                = local.common_tags
}

#Define Vnet Peering Between Gateway Vnet And Application Vnet
module "Gateway_Application_Vnet_Peering" {
  source                       = "../../../../modules/network_resources/virtual_network_peering"
  name                         = "Gateway-to-Application-${var.environment}"
  resource_group_name          = module.Gateway_Vnet_Resource_Group.name
  virtual_network_name         = module.Gateway_Vnet.name
  remote_virtual_network_id    = module.Application_Vnet.id
  allow_virtual_network_access = "true"
  allow_forwarded_traffic      = "true"
}

#Define Vnet Peering Between Gateway Vnet And Application Vnet
module "Application_Gateway_Vnet_Peering" {
  source                       = "../../../../modules/network_resources/virtual_network_peering"
  name                         = "Application-to-Gateway-${var.environment}"
  resource_group_name          = module.Application_Vnet_Resource_Group.name
  virtual_network_name         = module.Application_Vnet.name
  remote_virtual_network_id    = module.Gateway_Vnet.id
  allow_virtual_network_access = "true"
  allow_forwarded_traffic      = "true"
}

#Access Existing Shared Services resource group
data "azurerm_resource_group" "Management_Vnet_Resource_Group" {
	name = var.management_resource_group_name
}

#Define Management Vnet
module "Management_Vnet" {
  source              = "../../../../modules/network_resources/virtual_network"
  name                = var.management_vnet_name
  location            = var.location
  resource_group_name = data.azurerm_resource_group.Management_Vnet_Resource_Group.name
  address_space       = var.management_vnet_address_space
  tags                = local.common_tags
}

#Define Vnet Peering Between Management Vnet And Application Vnet
module "Management_Application_Vnet_Peering" {
  source                       = "../../../../modules/network_resources/virtual_network_peering"
  name                         = "Management-to-Application-${var.environment}"
  resource_group_name          = data.azurerm_resource_group.Management_Vnet_Resource_Group.name
  virtual_network_name         = module.Management_Vnet.name
  remote_virtual_network_id    = module.Application_Vnet.id
  allow_virtual_network_access = "true"
  allow_forwarded_traffic      = "true"
}

#Define Vnet Peering Between Management Vnet And Application Vnet
module "Application_Management_Vnet_Peering" {
  source                       = "../../../../modules/network_resources/virtual_network_peering"
  name                         = "Application-to-Management-${var.environment}"
  resource_group_name          = module.Application_Vnet_Resource_Group.name
  virtual_network_name         = module.Application_Vnet.name
  remote_virtual_network_id    = module.Management_Vnet.id
  allow_virtual_network_access = "true"
  allow_forwarded_traffic      = "true"
}

#Define Vnet Peering Between Management Vnet And Gateway Vnet
module "Management_Gateway_Vnet_Peering" {
  source                       = "../../../../modules/network_resources/virtual_network_peering"
  name                         = "Management-to-Gateway-${var.environment}"
  resource_group_name          = data.azurerm_resource_group.Management_Vnet_Resource_Group.name
  virtual_network_name         = module.Management_Vnet.name
  remote_virtual_network_id    = module.Gateway_Vnet.id
  allow_virtual_network_access = "true"
  allow_forwarded_traffic      = "true"
}

#Define Vnet Peering Between Management Vnet And Gateway Vnet
module "Gateway_Management_Vnet_Peering" {
  source                       = "../../../../modules/network_resources/virtual_network_peering"
  name                         = "Gateway-to-Management-${var.environment}"
  resource_group_name          = module.Gateway_Vnet_Resource_Group.name
  virtual_network_name         = module.Gateway_Vnet.name
  remote_virtual_network_id    = module.Management_Vnet.id
  allow_virtual_network_access = "true"
  allow_forwarded_traffic      = "true"
}



#######
#VPN
#######

#Define Azure resource group
module "VPN_Vnet_Resource_Group" {
  source   = "../../../../modules/base_resources/resource_group"
  name     = var.vpn_resource_group_name
  location = var.location
  tags     = local.common_tags
}

#Define VPN Vnet
module "VPN_Vnet" {
  source              = "../../../../modules/network_resources/virtual_network"
  name                = var.vpn_vnet_name
  location            = var.location
  resource_group_name = module.VPN_Vnet_Resource_Group.name
  address_space       = var.vpn_vnet_address_space
  tags                = local.common_tags
}


#Define Vnet Peering Between Gateway Vnet And Prod Application Vnet
module "VPN_Application_Vnet_Peering" {
  source                       = "../../../../modules/network_resources/virtual_network_peering"
  name                         = "vpn-to-application-${var.environment}"
  resource_group_name          = module.VPN_Vnet_Resource_Group.name
  virtual_network_name         = module.VPN_Vnet.name
  remote_virtual_network_id    = module.Application_Vnet.id
  allow_virtual_network_access = "true"
  allow_forwarded_traffic      = "true"
  allow_gateway_transit        = "true"
}

#Define Vnet Peering Between Prod Application Vnet and Gateway Vnet
module "Application_VPN_Vnet_Peering" {
  source                       = "../../../../modules/network_resources/virtual_network_peering"
  name                         = "application-to-vpn-${var.environment}"
  resource_group_name          = module.Application_Vnet_Resource_Group.name
  virtual_network_name         = module.Application_Vnet.name
  remote_virtual_network_id    = module.VPN_Vnet.id
  allow_virtual_network_access = "true"
  allow_forwarded_traffic      = "true"
  use_remote_gateways          = "true"
}

# Declare Resource in AU East
#Define Gateway Vnet
module "Gateway_Vnet_AU_East" {
  source              = "../../../../modules/network_resources/virtual_network"
  name                = "${var.customer}-${var.project}-gateway-vnet-2-${var.environment}"
  location            = var.location_au_east
  resource_group_name = module.Gateway_Vnet_Resource_Group_AU_East.name
  address_space       = var.gateway_vnet_address_space
  tags                = local.common_tags
}

#Define Vnet Peering Between Gateway Vnet And Application Vnet
module "Gateway_Application_Vnet_Peering_AU_East" {
  source                       = "../../../../modules/network_resources/virtual_network_peering"
  name                         = "Gateway-to-Application-2-${var.environment}"
  resource_group_name          = module.Gateway_Vnet_Resource_Group_AU_East.name
  virtual_network_name         = module.Gateway_Vnet_AU_East.name
  remote_virtual_network_id    = module.Application_Vnet_AU_East.id
  allow_virtual_network_access = "true"
  allow_forwarded_traffic      = "true"
}

#Define Vnet Peering Between Gateway Vnet And Application Vnet
module "Application_Gateway_Vnet_Peering_AU_East" {
  source                       = "../../../../modules/network_resources/virtual_network_peering"
  name                         = "Application-to-Gateway-2-${var.environment}"
  resource_group_name          = module.Application_Vnet_Resource_Group_AU_East.name
  virtual_network_name         = module.Application_Vnet_AU_East.name
  remote_virtual_network_id    = module.Gateway_Vnet_AU_East.id
  allow_virtual_network_access = "true"
  allow_forwarded_traffic      = "true"
}



