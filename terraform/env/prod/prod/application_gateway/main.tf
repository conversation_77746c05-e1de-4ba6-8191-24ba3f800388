provider "azurerm" {
  subscription_id = var.subscription_id
  client_id       = var.client_id
  client_secret   = var.client_secret
  tenant_id       = var.tenant_id
  features {}
}

terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "=4.6.0"
    }
  }
}

#Azure Backend
terraform {
  backend "azurerm" {
  }
}

#Access Existing App Prod resource group
data "azurerm_resource_group" "WAPol_Gateway_Resource_Group" {
  name = "${var.customer}-${var.project}-gateway-rg-${var.environment}"
}

#Access Existing Azure Virtual Network
data "azurerm_virtual_network" "WAPol_Gateway_Virtual_Network" {
  name                = "${var.customer}-${var.project}-gateway-vnet-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Gateway_Resource_Group.name
}

#Create API GW Subnet
module "Application_Gateway_Subnet" {
  source               = "../../../../modules/network_resources/subnet"
  name                 = "${var.customer}-${var.project}-agw-subnet-${var.environment}"
  resource_group_name  = data.azurerm_resource_group.WAPol_Gateway_Resource_Group.name
  virtual_network_name = data.azurerm_virtual_network.WAPol_Gateway_Virtual_Network.name
  address_prefixes     = var.application_gateway_subnet_cidr
  service_endpoints    = ["Microsoft.KeyVault", "Microsoft.Storage"]
}

#Create Network Security Group
module "Application_Gateway_Network_Security_Group" {
  source              = "../../../../modules/network_resources/network_security_group"
  name                = "${var.customer}-${var.project}-agw-nsg-${var.environment}"
  location            = var.location
  resource_group_name = data.azurerm_resource_group.WAPol_Gateway_Resource_Group.name
  tags                = local.common_tags
}

#Create Network Security Group Association
module "Application_Gateway_Network_Security_Group_Association" {
  source                    = "../../../../modules/network_resources/network_security_group_association"
  subnet_id                 = module.Application_Gateway_Subnet.id
  network_security_group_id = module.Application_Gateway_Network_Security_Group.id
}

#Add rule for ssh access from motorola ip range
module "Application_Gateway_NSG_Rule_SSH_From_Mot" {
  source                      = "../../../../modules/network_resources/network_security_rule"
  name                        = "${var.customer}-${var.project}-data-nsg-rule-1-${var.environment}"
  resource_group_name         = data.azurerm_resource_group.WAPol_Gateway_Resource_Group.name
  network_security_group_name = module.Application_Gateway_Network_Security_Group.name
  protocol                    = "Tcp"
  priority                    = "100"
  direction                   = "Inbound"
  access                      = "Allow"
  source_port_range           = "*"
  destination_port_ranges     = ["443"]
  destination_address_prefix  = "*"
  source_address_prefix       = "*"
}

#Add rule for default ports 65200 - 65535
module "Application_Gateway_NSG_Rule_Default_Ports" {
  source                      = "../../../../modules/network_resources/network_security_rule"
  name                        = "${var.customer}-${var.project}-data-nsg-rule-2-${var.environment}"
  resource_group_name         = data.azurerm_resource_group.WAPol_Gateway_Resource_Group.name
  network_security_group_name = module.Application_Gateway_Network_Security_Group.name
  protocol                    = "Tcp"
  priority                    = "110"
  direction                   = "Inbound"
  access                      = "Allow"
  source_port_range           = "*"
  destination_port_ranges     = ["65200-65535"]
  destination_address_prefix  = "*"
  source_address_prefix       = "*"
}

#Create a public ip to assign to application gateway
module "Application_Gateway_Public_IP" {
  source              = "../../../../modules/network_resources/public_ip"
  name                = "${var.customer}-${var.project}-agw-public-ip-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Gateway_Resource_Group.name
  location            = var.location
  sku                 = "Standard"
  domain_name_label   = "${var.customer}-${var.project}-agw-${var.environment}"
  tags                = local.common_tags
}

#&nbsp;since these variables are re-used - a locals block makes this more maintainable
locals {
  backend_address_pool_name      = "${var.customer}-${var.project}-agw-beap-${var.environment}"
  frontend_port_name             = "${var.customer}-${var.project}-agw-feport-${var.environment}"
  frontend_ip_configuration_name = "${var.customer}-${var.project}-agw-feip-${var.environment}"
  http_setting_name              = "${var.customer}-${var.project}-agw-be-htst-${var.environment}"
  listener_name                  = "${var.customer}-${var.project}-agw-httplstn-${var.environment}"
  request_routing_rule_name      = "${var.customer}-${var.project}-agw-rqrt-${var.environment}"
  health_probe_name              = "${var.customer}-${var.project}-agw-probe-${var.environment}"

  rewrite_rule_set_name = "${var.customer}-${var.project}-agw-rrs-${var.environment}"

  kibana_backend_address_pool_name_prod = "${var.customer}-${var.project}-kibana-beap-${var.environment}"
  kibana_http_setting_name_prod         = "${var.customer}-${var.project}-kibana-be-htst-${var.environment}"
  kibana_listener_name_prod             = "${var.customer}-${var.project}-kibana-httplstn-${var.environment}"
  kibana_request_routing_rule_name_prod = "${var.customer}-${var.project}-kibana-rqrt-${var.environment}"
  kibana_health_probe_name_prod         = "${var.customer}-${var.project}-kibana-probe-${var.environment}"
}

#Create application gateway
resource "azurerm_application_gateway" "application_gateway_prod" {
  name                = "${var.customer}-${var.project}-agw-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Gateway_Resource_Group.name
  location            = var.location

  sku {
    name     = "WAF_v2"
    tier     = "WAF_v2"
    capacity = 2
  }

  gateway_ip_configuration {
    name      = "${var.customer}-${var.project}-agw-ip-configuration-${var.environment}"
    subnet_id = module.Application_Gateway_Subnet.id
  }

  frontend_port {
    name = local.frontend_port_name
    port = 443
  }

  frontend_ip_configuration {
    name                 = local.frontend_ip_configuration_name
    public_ip_address_id = module.Application_Gateway_Public_IP.id
  }

  backend_address_pool {
    name  = local.backend_address_pool_name
    fqdns = ["${var.customer}-api-internal-${var.environment}.pscore.cloud"]
  }

  backend_http_settings {
    name                  = local.http_setting_name
    cookie_based_affinity = "Disabled"
    port                  = 80
    protocol              = "Http"
    request_timeout       = 60
    probe_name            = local.health_probe_name
  }

  probe {
    name                = local.health_probe_name
    protocol            = "Http"
    host                = "127.0.0.1"
    path                = "/healthz/ready"
    interval            = 30
    timeout             = 30
    unhealthy_threshold = 3
    port                = 15021
  }

  http_listener {
    name                           = local.listener_name
    frontend_ip_configuration_name = local.frontend_ip_configuration_name
    frontend_port_name             = local.frontend_port_name
    protocol                       = "Https"
    ssl_certificate_name           = "pscore.cloud.crt"
    host_names                     = ["wapol-agw-prod.pscore.cloud"]
    firewall_policy_id             = azurerm_web_application_firewall_policy.web_application_firewall_policy_api.id
  }

  request_routing_rule {
    name                       = local.request_routing_rule_name
    rule_type                  = "Basic"
    http_listener_name         = local.listener_name
    backend_address_pool_name  = local.backend_address_pool_name
    backend_http_settings_name = local.http_setting_name
    rewrite_rule_set_name      = local.rewrite_rule_set_name
    priority                   = "1"
  }

  backend_address_pool {
    name  = local.kibana_backend_address_pool_name_prod
    fqdns = ["${var.customer}-kibana-internal-${var.environment}.pscore.cloud"]
  }

  backend_http_settings {
    name                  = local.kibana_http_setting_name_prod
    cookie_based_affinity = "Disabled"
    port                  = 5601
    protocol              = "Http"
    request_timeout       = 60
    probe_name            = local.kibana_health_probe_name_prod
  }

  probe {
    name                = local.kibana_health_probe_name_prod
    protocol            = "Http"
    host                = "127.0.0.1"
    path                = "/"
    interval            = 30
    timeout             = 30
    unhealthy_threshold = 3
    port                = 5601
    match {
      status_code = ["200-399"]
    }
  }

  http_listener {
    name                           = local.kibana_listener_name_prod
    frontend_ip_configuration_name = local.frontend_ip_configuration_name
    frontend_port_name             = local.frontend_port_name
    protocol                       = "Https"
    ssl_certificate_name           = "pscore.cloud.crt"
    host_names                     = ["wapol-kibana-prod.pscore.cloud"]
    firewall_policy_id             = azurerm_web_application_firewall_policy.web_application_firewall_policy_kibana.id
  }

  request_routing_rule {
    name                       = local.kibana_request_routing_rule_name_prod
    rule_type                  = "Basic"
    http_listener_name         = local.kibana_listener_name_prod
    backend_address_pool_name  = local.kibana_backend_address_pool_name_prod
    backend_http_settings_name = local.kibana_http_setting_name_prod
    priority                   = "11"
  }


  ssl_certificate {
    name                = "pscore.cloud.crt"
    key_vault_secret_id = "https://wapol-wpm-kv-main-prod.vault.azure.net/secrets/pscoreCloudCrt"
  }

  identity {
    type         = "UserAssigned"
    identity_ids = [azurerm_user_assigned_identity.application_gateway_identity.id]
  }
  rewrite_rule_set {
    name = local.rewrite_rule_set_name

    rewrite_rule {
      name          = "rewrite-rule-1"
      rule_sequence = 100

      condition {
        variable = "var_host"
        pattern  = "wapol-agw-${var.environment}.pscore.cloud"
      }
      condition {
        variable = "var_uri_path"
        pattern  = "/referencedata/(.*)/item"
      }

      condition {
        variable = "var_query_string"
        pattern  = "lastUpdated=(.*)&skip=0&take=10000"
      }
      url {
        path         = "/referencedata/{var_uri_path_1}/item"
        query_string = "lastUpdated={var_query_string_1}"
      }
    }

    rewrite_rule {
      name          = "rewrite-rule-2"
      rule_sequence = 101

      condition {
        variable = "var_host"
        pattern  = "wapol-agw-${var.environment}.pscore.cloud"
      }
      condition {
        variable = "var_uri_path"
        pattern  = "/referencedata/(.*)/item"
      }

      condition {
        variable = "var_query_string"
        pattern  = "skip=0&take=10000"
      }
      url {
        path         = "/referencedata/{var_uri_path_1}/item"
        query_string = ""
      }
    }
  }

  #waf_configuration {
  #  enabled          = "true"
  #  firewall_mode    = "Prevention"
  #  rule_set_type    = "OWASP"
  #  rule_set_version = "3.2"
  #}

  firewall_policy_id = azurerm_web_application_firewall_policy.web_application_firewall_policy.id

  depends_on = [module.Application_Gateway_NSG_Rule_Default_Ports, azurerm_web_application_firewall_policy.web_application_firewall_policy]
  tags       = local.common_tags
}
resource "azurerm_user_assigned_identity" "application_gateway_identity" {
  location            = var.location
  name                = "${var.customer}-${var.project}-agw-id-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Gateway_Resource_Group.name
  tags                = local.common_tags
}
#create WAF policy
#global waf rule
resource "azurerm_web_application_firewall_policy" "web_application_firewall_policy" {
  name                = "${var.customer}-${var.project}-agw-waf-policy-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Gateway_Resource_Group.name
  location            = var.location

  policy_settings {
    enabled                     = true
    mode                        = "Prevention"
    request_body_check          = false
    file_upload_limit_in_mb     = 100
    max_request_body_size_in_kb = 128
  }

  managed_rules {
    managed_rule_set {
      type    = "OWASP"
      version = "3.2"
    }
  }

  tags = local.common_tags
}

#per-site waf rule - api
resource "azurerm_web_application_firewall_policy" "web_application_firewall_policy_api" {
  name                = "${var.customer}-${var.project}-agw-waf-policy-api-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Gateway_Resource_Group.name
  location            = var.location

  custom_rules {
    name      = "allowSpecificIpForStarlink"
    priority  = 1
    rule_type = "MatchRule"
    match_conditions {
      match_variables {
        variable_name = "RemoteAddr"
      }
      operator           = "IPMatch"
      negation_condition = false
      match_values       = ["*************/32", "*************/32", "************/32", "*************/32", "************/32", "*************/32", "*************/32"]
    }

    action = "Allow"
  }

  custom_rules {
    name      = "allowSpecificIpForSituationalAlertExternalApi"
    priority  = 2
    rule_type = "MatchRule"
    match_conditions {
      match_variables {
        variable_name = "RemoteAddr"
      }
      operator           = "IPMatch"
      negation_condition = false
      match_values       = ["***********/32", "***********/32"]
    }
    match_conditions {
      match_variables {
        variable_name = "RequestUri"
      }

      operator           = "BeginsWith"
      negation_condition = false
      match_values       = ["/situational-alert-external-api/v1/external"]
    }
    match_conditions {
      match_variables {
        variable_name = "RequestHeaders"
        selector      = "X-Api-Key"
      }

      operator           = "Regex"
      negation_condition = true
      match_values       = ["^$"]
    }

    action = "Allow"
  }

  custom_rules {
    name      = "geoAllowAU"
    priority  = 3
    rule_type = "MatchRule"

    match_conditions {
      match_variables {
        variable_name = "RemoteAddr"
      }

      operator           = "GeoMatch"
      negation_condition = true
      match_values       = ["AU"]
    }

    action = "Block"
  }

  custom_rules {
    name      = "allowBackendPath"
    priority  = 4
    rule_type = "MatchRule"

    match_conditions {
      match_variables {
        variable_name = "RequestUri"
      }

      operator           = "BeginsWith"
      negation_condition = false
      match_values       = ["/cad/", "/entity/", "/event-publisher/", "/persistence/", "/referencedata/", "/reference-data/", "/situational-alert-external-api/", "/situational-alert/", "/reporting/v1/anpr/user/connection"]
    }

    match_conditions {
      match_variables {
        variable_name = "RequestHeaders"
        selector      = "X-User-ID"
      }

      operator           = "Regex"
      negation_condition = false
      match_values       = ["[a-zA-Z0-9]"]
    }

    action = "Allow"
  }

  custom_rules {
    name      = "allowTransactionWithoutXUserIdHeader"
    priority  = 5
    rule_type = "MatchRule"

    match_conditions {
      match_variables {
        variable_name = "RequestUri"
      }

      operator           = "BeginsWith"
      negation_condition = false
      match_values       = ["/cad/p1/device/deregister", "/persistence/v1/data"]
    }

    action = "Allow"
  }

  custom_rules {
    enabled   = false
    name      = "blockAllMethodAsDefault"
    priority  = 6
    rule_type = "MatchRule"

    match_conditions {
      match_variables {
        variable_name = "RequestMethod"
      }

      operator           = "Regex"
      negation_condition = false
      match_values       = ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD", "TRACE", "CONNECT"]
    }

    action = "Block"
  }

  policy_settings {
    enabled                     = true
    mode                        = "Prevention"
    request_body_check          = false
    file_upload_limit_in_mb     = 100
    max_request_body_size_in_kb = 128
  }

  managed_rules {
    managed_rule_set {
      type    = "OWASP"
      version = "3.2"
    }
  }

  tags = local.common_tags
}

#per-site waf rule - kibana
resource "azurerm_web_application_firewall_policy" "web_application_firewall_policy_kibana" {
  name                = "${var.customer}-${var.project}-agw-waf-policy-kibana-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Gateway_Resource_Group.name
  location            = var.location


  custom_rules {
    name      = "allowSpecificIpAndDomain"
    priority  = 1
    rule_type = "MatchRule"
    match_conditions {
      match_variables {
        variable_name = "RemoteAddr"
      }
      operator           = "IPMatch"
      negation_condition = false
      match_values       = ["***********/16"]
    }
    match_conditions {
      match_variables {
        variable_name = "RequestHeaders"
        selector      = "host"
      }

      operator           = "Contains"
      negation_condition = false
      match_values       = ["wapol-kibana-prod.pscore.cloud"]
    }

    action = "Allow"
  }

  custom_rules {
    name      = "blockAllMethodAsDefault"
    priority  = 2
    rule_type = "MatchRule"

    match_conditions {
      match_variables {
        variable_name = "RequestMethod"
      }

      operator           = "Regex"
      negation_condition = false
      match_values       = ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD", "TRACE", "CONNECT"]
    }

    action = "Block"
  }

  policy_settings {
    enabled                     = true
    mode                        = "Prevention"
    request_body_check          = false
    file_upload_limit_in_mb     = 100
    max_request_body_size_in_kb = 128
  }

  managed_rules {
    managed_rule_set {
      type    = "OWASP"
      version = "3.2"
    }
  }

  tags = local.common_tags
}
