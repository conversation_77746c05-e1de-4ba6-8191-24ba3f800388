# Azure Application Gateway Migration Plan
## From Australia Southeast to Australia East

### Executive Summary
This plan outlines the migration of the Azure Application Gateway from Australia Southeast to Australia East to co-locate it with the existing AKS cluster, reducing latency and improving performance.

### Current State Analysis
- **Application Gateway**: Australia Southeast (wapol-wpm-agw-prod)
- **AKS Cluster**: Australia East (wapol-wpm-aks-cluster-prod)
- **Issue**: Cross-region traffic between AGW and AKS causing latency
- **Domain**: wapol-agw-prod.pscore.cloud

### Target State
- **New Application Gateway**: Australia East (parallel deployment)
- **AKS Cluster**: Australia East (no change)
- **Traffic**: Same-region routing for optimal performance
- **Domain**: Same domain with host file testing capability

---

## Migration Checklist

### Phase 1: Infrastructure Preparation
#### 1.1 Network Infrastructure Assessment
- [ ] **Review existing Australia East VNet configuration**
  - [ ] Verify `wapol-wpm-gateway-vnet-2-prod` exists in Australia East
  - [ ] Confirm address space doesn't conflict with existing networks
  - [ ] Document current peering relationships

- [ ] **Plan new subnet allocation**
  - [ ] Identify available CIDR block for new AGW subnet
  - [ ] Ensure minimum /26 subnet for Application Gateway
  - [ ] Avoid conflicts with existing subnets (***********/24, ***********/24)

#### 1.2 Resource Group Verification
- [ ] **Confirm target resource group exists**
  - [ ] Verify `wapol-wpm-gateway-rg-prod` in Australia East
  - [ ] Check permissions and access policies
  - [ ] Validate resource group tags and compliance

### Phase 2: Terraform Configuration Development
#### 2.1 Create New Terraform Module
- [ ] **Duplicate existing AGW configuration**
  - [ ] Copy `terraform/env/prod/prod/application_gateway/` structure
  - [ ] Create `application_gateway_au_east/` directory
  - [ ] Modify resource names with `-au-east` suffix

- [ ] **Update variable definitions**
  - [ ] Add `location_au_east` variable usage
  - [ ] Define new subnet CIDR variables
  - [ ] Update resource naming conventions

#### 2.2 Network Configuration Updates
- [ ] **Create new AGW subnet in Australia East**
  - [ ] Define subnet module for new AGW subnet
  - [ ] Configure NSG rules (ports 443, 65200-65535)
  - [ ] Set up subnet association

- [ ] **Configure VNet peering**
  - [ ] Ensure Gateway VNet AU East peers with Application VNet
  - [ ] Verify bidirectional peering configuration
  - [ ] Test connectivity between regions

#### 2.3 Application Gateway Configuration
- [ ] **Create new public IP**
  - [ ] Provision Standard SKU public IP in Australia East
  - [ ] Configure DNS label: `wapol-wpm-agw-au-east-prod`
  - [ ] Document new public IP address

- [ ] **Configure Application Gateway**
  - [ ] Set up WAF_v2 SKU with capacity 2
  - [ ] Configure frontend IP with new public IP
  - [ ] Set up SSL certificate from Key Vault
  - [ ] Configure backend pools pointing to same FQDNs

- [ ] **Configure listeners and rules**
  - [ ] Create HTTP listener for `wapol-agw-prod.pscore.cloud`
  - [ ] Set up routing rules to backend pools
  - [ ] Configure health probes (port 15021 for API, 5601 for Kibana)
  - [ ] Apply WAF policies (copy from existing AGW)

### Phase 3: Security and Compliance
#### 3.1 WAF Policy Configuration
- [ ] **Replicate existing WAF policies**
  - [ ] Copy API WAF policy with IP allowlists
  - [ ] Copy Kibana WAF policy with IP restrictions
  - [ ] Verify geo-blocking rules (AU only)
  - [ ] Test custom rules for backend paths

#### 3.2 SSL Certificate Management
- [ ] **Configure SSL certificate access**
  - [ ] Verify Key Vault access from new AGW identity
  - [ ] Test certificate retrieval
  - [ ] Configure managed identity permissions

#### 3.3 Network Security
- [ ] **Configure NSG rules**
  - [ ] Allow HTTPS traffic (port 443)
  - [ ] Allow AGW management ports (65200-65535)
  - [ ] Restrict source IP ranges as needed
  - [ ] Document security group associations

### Phase 4: Testing and Validation
#### 4.1 Infrastructure Testing
- [ ] **Deploy new AGW via Terraform**
  - [ ] Run `terraform plan` and review changes
  - [ ] Deploy in maintenance window
  - [ ] Verify all resources created successfully
  - [ ] Check resource tags and compliance

#### 4.2 Connectivity Testing
- [ ] **Test backend connectivity**
  - [ ] Verify AGW can reach API service (wapol-api-internal-prod.pscore.cloud)
  - [ ] Verify AGW can reach Kibana service (wapol-kibana-internal-prod.pscore.cloud)
  - [ ] Test health probe responses
  - [ ] Validate SSL termination

#### 4.3 Host File Testing
- [ ] **Configure local testing**
  - [ ] Update local hosts file: `<NEW_AGW_IP> wapol-agw-prod.pscore.cloud`
  - [ ] Test API endpoints through new AGW
  - [ ] Test Kibana access through new AGW
  - [ ] Verify WAF rules are working
  - [ ] Test SSL certificate validation

### Phase 5: Parallel Operation
#### 5.1 Monitor Both AGWs
- [ ] **Set up monitoring**
  - [ ] Configure Azure Monitor for new AGW
  - [ ] Set up alerts for health and performance
  - [ ] Monitor traffic patterns
  - [ ] Compare performance metrics

#### 5.2 Gradual Traffic Migration
- [ ] **DNS preparation**
  - [ ] Document current DNS TTL settings
  - [ ] Prepare DNS change procedures
  - [ ] Plan rollback procedures
  - [ ] Coordinate with DNS administrators

### Phase 6: Cutover Planning
#### 6.1 Production Cutover
- [ ] **Schedule maintenance window**
  - [ ] Coordinate with stakeholders
  - [ ] Prepare communication plan
  - [ ] Set up monitoring dashboards
  - [ ] Prepare rollback procedures

#### 6.2 DNS Cutover
- [ ] **Update DNS records**
  - [ ] Change A record for wapol-agw-prod.pscore.cloud
  - [ ] Monitor DNS propagation
  - [ ] Verify traffic routing to new AGW
  - [ ] Monitor application performance

### Phase 7: Post-Migration
#### 7.1 Validation and Monitoring
- [ ] **Performance validation**
  - [ ] Compare latency metrics (before/after)
  - [ ] Monitor error rates
  - [ ] Validate all application functions
  - [ ] Check security logs

#### 7.2 Cleanup (After Successful Migration)
- [ ] **Decommission old AGW**
  - [ ] Stop traffic to old AGW
  - [ ] Monitor for 48 hours
  - [ ] Remove old AGW resources
  - [ ] Update Terraform configurations
  - [ ] Clean up unused public IPs

---

## Risk Mitigation

### High-Risk Items
1. **DNS Propagation Delays**: Plan for TTL reduction before cutover
2. **SSL Certificate Issues**: Test certificate access thoroughly
3. **WAF Rule Conflicts**: Validate all custom rules in new environment
4. **Cross-Region Latency**: Monitor during parallel operation

### Rollback Procedures
1. **Immediate Rollback**: Update DNS back to original AGW
2. **Terraform Rollback**: Keep original configuration intact
3. **Monitoring**: Set up alerts for automatic detection of issues

### Success Criteria
- [ ] Reduced latency between AGW and AKS (target: <5ms)
- [ ] All applications accessible via wapol-agw-prod.pscore.cloud
- [ ] No security policy violations
- [ ] Zero downtime during cutover
- [ ] All monitoring and alerting functional

---

## Timeline Estimate
- **Phase 1-2**: 2-3 days (Infrastructure prep and Terraform development)
- **Phase 3**: 1 day (Security configuration)
- **Phase 4**: 2-3 days (Testing and validation)
- **Phase 5**: 1-2 weeks (Parallel operation and monitoring)
- **Phase 6**: 1 day (Cutover)
- **Phase 7**: 1 week (Post-migration validation)

**Total Estimated Duration**: 3-4 weeks
