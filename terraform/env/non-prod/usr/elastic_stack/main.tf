provider "azurerm" {
  subscription_id = var.subscription_id
  client_id       = var.client_id
  client_secret   = var.client_secret
  tenant_id       = var.tenant_id
  features {}
}

terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "=3.117.0"
    }
  }
}

# Azure Backend
terraform {
  backend "azurerm" {}
}

# Access Existing App Usr resource group
data "azurerm_resource_group" "WAPol_Data_Resource_Group" {
  name = "wapol-wpm-rg-${var.environment}"
}
# Access Existing resource group
data "azurerm_resource_group" "WAPol_RSV_Resource_Group" {
  name = "${var.customer}-${var.project}-rg-dev"
}
data "azurerm_automation_account" "automation_account" {
  name                = "wapol-wpm-aa-usr"
  resource_group_name = "wapol-wpm-shared-services-rg-usr"
}

# Access Existing Azure Virtual Network
data "azurerm_virtual_network" "WAPol_Data_Virtual_Network" {
  name                = "wapol-wpm-app-vnet-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Data_Resource_Group.name
}

# Create Data Subnet
module "Data_Subnet" {
  source               = "../../../../modules/network_resources/subnet"
  name                 = "${var.customer}-${var.project}-data-subnet-${var.environment}"
  resource_group_name  = data.azurerm_resource_group.WAPol_Data_Resource_Group.name
  virtual_network_name = data.azurerm_virtual_network.WAPol_Data_Virtual_Network.name
  address_prefixes     = var.data_subnet_cidr
}

# Create Network Security Group
module "Data_Network_Security_Group" {
  source              = "../../../../modules/network_resources/network_security_group"
  name                = "${var.customer}-${var.project}-data-nsg-${var.environment}"
  location            = var.location
  resource_group_name = data.azurerm_resource_group.WAPol_Data_Resource_Group.name
  tags                = local.common_tags
}

# Create Network Security Group Association
module "Data_Network_Security_Group_Association" {
  source                    = "../../../../modules/network_resources/network_security_group_association"
  subnet_id                 = module.Data_Subnet.id
  network_security_group_id = module.Data_Network_Security_Group.id
}

# Add rule for SSH access from Motorola IP range
module "Data_NSG_Rule_SSH_From_Mot" {
  source                      = "../../../../modules/network_resources/network_security_rule"
  name                        = "${var.customer}-${var.project}-data-nsg-rule-1-${var.environment}"
  resource_group_name         = data.azurerm_resource_group.WAPol_Data_Resource_Group.name
  network_security_group_name = module.Data_Network_Security_Group.name
  protocol                    = "Tcp"
  priority                    = "100"
  direction                   = "Inbound"
  access                      = "Allow"
  source_port_range           = "*"
  destination_port_ranges     = ["22", "443", "80", "9200", "5601"]
  destination_address_prefix  = "*"
  source_address_prefixes     = local.motorola_ip_ranges
}

# Add rule for SSH access from HK Bastion
module "Data_NSG_Rule_SSH_From_HKBastion" {
  source                      = "../../../../modules/network_resources/network_security_rule"
  name                        = "${var.customer}-${var.project}-data-nsg-rule-2-${var.environment}"
  resource_group_name         = data.azurerm_resource_group.WAPol_Data_Resource_Group.name
  network_security_group_name = module.Data_Network_Security_Group.name
  protocol                    = "Tcp"
  priority                    = "110"
  direction                   = "Inbound"
  access                      = "Allow"
  source_port_range           = "*"
  destination_port_ranges     = ["22"]
  destination_address_prefix  = "*"
  source_address_prefixes     = ["**************/32"]
}

########################
# ES VMs * 3
########################

# Create public IPs to assign to ES Linux machines
module "ES_Public_IP" {
  count               = 3
  source              = "../../../../modules/network_resources/public_ip"
  name                = "${var.customer}-${var.project}-es-public-ip-${count.index + 1}-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Data_Resource_Group.name
  location            = var.location
  sku                 = "Standard"
  domain_name_label   = "${var.customer}-${var.project}-es-${count.index + 1}-${var.environment}"
  tags                = local.common_tags
}

# Create Network Interfaces to assign to ES Linux Machines
module "ES_Network_Interface" {
  count                         = 3
  source                        = "../../../../modules/network_resources/network_interface"
  name                          = "${var.customer}_${var.project}_es_network_interface_${count.index + 1}_${var.environment}"
  location                      = var.location
  resource_group_name           = data.azurerm_resource_group.WAPol_Data_Resource_Group.name
  subnet_id                     = module.Data_Subnet.id
  private_ip_address_allocation = "Static"
  private_ip_address            = var.es_private_ips[count.index]
  public_ip_address_id          = element(module.ES_Public_IP.*.id, count.index)
  tags                          = local.common_tags
}
locals {
  es_ssh_public_key = <<-EOT
    ${var.es_ssh_pub_key}
  EOT
}
# Create ES Linux Machines
resource "azurerm_linux_virtual_machine" "es_linux_virtual_machine" {
  count               = 3
  name                = "${var.customer}-${var.project}-es-vm-${count.index + 1}-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Data_Resource_Group.name
  location            = var.location
  computer_name       = "${var.customer}${var.project}es${count.index + 1}${var.environment}"
  size                = "Standard_A4_v2"
  admin_username      = "gsdevops"
  # encryption_at_host_enabled = "true"
  patch_assessment_mode                                  = "AutomaticByPlatform"
  patch_mode                                             = "AutomaticByPlatform"
  bypass_platform_safety_checks_on_user_schedule_enabled = true

  network_interface_ids = [
    element(module.ES_Network_Interface.*.id, count.index)
  ]

  admin_ssh_key {
    username   = "gsdevops"
    public_key = local.es_ssh_public_key
  }

  os_disk {
    caching              = "ReadWrite"
    storage_account_type = "Standard_LRS"
    disk_size_gb         = "256"
  }

  source_image_reference {
    publisher = "Canonical"
    offer     = "UbuntuServer"
    sku       = "18.04-LTS"
    version   = "latest"
  }

  identity {
    type = "SystemAssigned"
  }

  tags = local.common_tags
}

resource "azurerm_virtual_machine_extension" "es_linux_virtual_machine_extension_gc" {
  count                     = 3
  name                      = "AzurePolicyForLinux"
  virtual_machine_id        = element(azurerm_linux_virtual_machine.es_linux_virtual_machine.*.id, count.index)
  publisher                 = "Microsoft.GuestConfiguration"
  type                      = "ConfigurationForLinux"
  type_handler_version      = "1.26"
  automatic_upgrade_enabled = true
}

resource "azurerm_virtual_machine_extension" "es_linux_virtual_machine_extension_am" {
  count                     = 3
  name                      = "AzureMonitorLinuxAgent"
  virtual_machine_id        = element(azurerm_linux_virtual_machine.es_linux_virtual_machine.*.id, count.index)
  publisher                 = "Microsoft.Azure.Monitor"
  type                      = "AzureMonitorLinuxAgent"
  type_handler_version      = "1.33"
  automatic_upgrade_enabled = true
}

#######################################
# Kibana VM
#######################################

# Create a public IP to assign to Kibana Linux machine
module "Kibana_Public_IP" {
  source              = "../../../../modules/network_resources/public_ip"
  name                = "${var.customer}-${var.project}-kibana-public-ip-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Data_Resource_Group.name
  location            = var.location
  sku                 = "Standard"
  domain_name_label   = "${var.customer}-${var.project}-kibana-${var.environment}"
  tags                = local.common_tags
}

# Create Network Interface to assign to Kibana ES Linux Machine
module "Kibana_Network_Interface" {
  source                        = "../../../../modules/network_resources/network_interface"
  name                          = "${var.customer}_${var.project}_kibana_network_interface_${var.environment}"
  location                      = var.location
  resource_group_name           = data.azurerm_resource_group.WAPol_Data_Resource_Group.name
  subnet_id                     = module.Data_Subnet.id
  private_ip_address_allocation = "Static"
  private_ip_address            = var.kibana_private_ip
  public_ip_address_id          = module.Kibana_Public_IP.id
  tags                          = local.common_tags
}
locals {
  kibana_ssh_public_key = <<-EOT
    ${var.kibana_ssh_pub_key}
  EOT
}
# Create Kibana Linux Machine
resource "azurerm_linux_virtual_machine" "kibana_linux_virtual_machine" {
  name                = "${var.customer}-${var.project}-kibana-vm-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Data_Resource_Group.name
  location            = var.location
  computer_name       = "${var.customer}${var.project}kibana${var.environment}"
  size                = "Standard_A2_v2"
  admin_username      = "gsdevops"
  tags                = local.common_tags

  patch_assessment_mode                                  = "AutomaticByPlatform"
  patch_mode                                             = "AutomaticByPlatform"
  bypass_platform_safety_checks_on_user_schedule_enabled = true

  network_interface_ids = [
    module.Kibana_Network_Interface.id,
  ]

  admin_ssh_key {
    username   = "gsdevops"
    public_key = local.kibana_ssh_public_key
  }

  os_disk {
    caching              = "ReadWrite"
    storage_account_type = "Standard_LRS"
    disk_size_gb         = "30"
  }

  source_image_reference {
    publisher = "Canonical"
    offer     = "UbuntuServer"
    sku       = "18.04-LTS"
    version   = "latest"
  }

  identity {
    type = "SystemAssigned"
  }
}

resource "azurerm_virtual_machine_extension" "kibana_linux_virtual_machine_extension_gc" {
  name                      = "AzurePolicyForLinux"
  virtual_machine_id        = azurerm_linux_virtual_machine.kibana_linux_virtual_machine.id
  publisher                 = "Microsoft.GuestConfiguration"
  type                      = "ConfigurationForLinux"
  type_handler_version      = "1.26"
  automatic_upgrade_enabled = true
}

resource "azurerm_virtual_machine_extension" "kibana_linux_virtual_machine_extension_am" {
  name                      = "AzureMonitorLinuxAgent"
  virtual_machine_id        = azurerm_linux_virtual_machine.kibana_linux_virtual_machine.id
  publisher                 = "Microsoft.Azure.Monitor"
  type                      = "AzureMonitorLinuxAgent"
  type_handler_version      = "1.33"
  automatic_upgrade_enabled = true
}

output "es_vm_resource_ids" {
  value = azurerm_linux_virtual_machine.es_linux_virtual_machine[*].id
  description = "The resource IDs of the Elasticsearch VMs"
}

output "kibana_vm_resource_id" {
  value = azurerm_linux_virtual_machine.kibana_linux_virtual_machine.id
  description = "The resource ID of the Kibana VM"
}

# Create a dedicated USR recovery services vault instead of using dev's
resource "azurerm_recovery_services_vault" "es_vm_recovery_services_vault" {
  name                = "${var.customer}-${var.project}-rsv-${var.environment}"
  location            = var.location
  resource_group_name = data.azurerm_resource_group.WAPol_Data_Resource_Group.name
  sku                 = "Standard"
  soft_delete_enabled = true
  tags                = local.common_tags
}

resource "azurerm_backup_policy_vm" "backup_policy" {
  name                = "${var.customer}-${var.project}-bkpol-vm-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Data_Resource_Group.name
  recovery_vault_name = azurerm_recovery_services_vault.es_vm_recovery_services_vault.name

  timezone = "UTC"

  backup {
    frequency = "Daily"
    time      = "22:00" # 06:00 am Perth Time
  }

  retention_daily {
    count = 7
  }
}

# Replace the data reference to dev vault
resource "azurerm_backup_protected_vm" "assign_backup_policy_kibana_vm" {
  resource_group_name = data.azurerm_resource_group.WAPol_Data_Resource_Group.name
  recovery_vault_name = azurerm_recovery_services_vault.es_vm_recovery_services_vault.name
  source_vm_id        = azurerm_linux_virtual_machine.kibana_linux_virtual_machine.id
  backup_policy_id    = azurerm_backup_policy_vm.backup_policy.id
}

resource "azurerm_backup_protected_vm" "assign_backup_policy_es_vm" {
  count               = 3
  resource_group_name = data.azurerm_resource_group.WAPol_Data_Resource_Group.name
  recovery_vault_name = azurerm_recovery_services_vault.es_vm_recovery_services_vault.name
  source_vm_id        = azurerm_linux_virtual_machine.es_linux_virtual_machine[count.index].id
  backup_policy_id    = azurerm_backup_policy_vm.backup_policy.id
}


# Declare Resource in AU East
# Access Existing App Usr resource group
data "azurerm_resource_group" "WAPol_Data_Resource_Group_AU_East" {
  name = "${var.customer}-${var.project}-rg-2-${var.environment}"
}
# Access Existing resource group
# data "azurerm_resource_group" "WAPol_RSV_Resource_Group_AU_East" {
#   name = "${var.customer}-${var.project}-rg-dev"
# }
# data "azurerm_automation_account" "automation_account_au_east" {
#   name                = "wapol-wpm-aa-2-usr"
#   resource_group_name = "wapol-wpm-shared-services-rg-2-usr"
# }

# Access Existing Azure Virtual Network
data "azurerm_virtual_network" "WAPol_Data_Virtual_Network_AU_East" {
  name                = "wapol-wpm-app-vnet-2-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Data_Resource_Group_AU_East.name
}

# Create Data Subnet
module "Data_Subnet_AU_East" {
  source               = "../../../../modules/network_resources/subnet"
  name                 = "${var.customer}-${var.project}-data-subnet-2-${var.environment}"
  resource_group_name  = data.azurerm_resource_group.WAPol_Data_Resource_Group_AU_East.name
  virtual_network_name = data.azurerm_virtual_network.WAPol_Data_Virtual_Network_AU_East.name
  address_prefixes     = var.data_subnet_cidr
}

# Create Network Security Group
module "Data_Network_Security_Group_AU_East" {
  source              = "../../../../modules/network_resources/network_security_group"
  name                = "${var.customer}-${var.project}-data-nsg-${var.environment}-au-east"
  location            = var.location_au_east
  resource_group_name = data.azurerm_resource_group.WAPol_Data_Resource_Group_AU_East.name
  tags                = local.common_tags
}

# Create Network Security Group Association
module "Data_Network_Security_Group_Association_AU_East" {
  source                    = "../../../../modules/network_resources/network_security_group_association"
  subnet_id                 = module.Data_Subnet_AU_East.id
  network_security_group_id = module.Data_Network_Security_Group_AU_East.id
}

# Add rule for SSH access from Motorola IP range
module "Data_NSG_Rule_SSH_From_Mot_AU_East" {
  source                      = "../../../../modules/network_resources/network_security_rule"
  name                        = "${var.customer}-${var.project}-data-nsg-rule-1-${var.environment}-au-east"
  resource_group_name         = data.azurerm_resource_group.WAPol_Data_Resource_Group_AU_East.name
  network_security_group_name = module.Data_Network_Security_Group_AU_East.name
  protocol                    = "Tcp"
  priority                    = "100"
  direction                   = "Inbound"
  access                      = "Allow"
  source_port_range           = "*"
  destination_port_ranges     = ["22", "443", "80", "9200", "5601"]
  destination_address_prefix  = "*"
  source_address_prefixes     = local.motorola_ip_ranges
}

########################
# ES VMs * 3
########################

# Create public IPs to assign to ES Linux machines
module "ES_Public_IP_AU_East" {
  count               = 3
  source              = "../../../../modules/network_resources/public_ip"
  name                = "${var.customer}-${var.project}-es-public-ip-${count.index + 1}-${var.environment}-au-east"
  resource_group_name = data.azurerm_resource_group.WAPol_Data_Resource_Group_AU_East.name
  location            = var.location_au_east
  sku                 = "Standard"
  domain_name_label   = "${var.customer}-${var.project}-es-${count.index + 1}-${var.environment}-2"
  tags                = local.common_tags
}

# Create Network Interfaces to assign to ES Linux Machines
module "ES_Network_Interface_AU_East" {
  count                         = 3
  source                        = "../../../../modules/network_resources/network_interface"
  name                          = "${var.customer}_${var.project}_es_network_interface_${count.index + 1}_${var.environment}_au_east"
  location                      = var.location_au_east
  resource_group_name           = data.azurerm_resource_group.WAPol_Data_Resource_Group_AU_East.name
  subnet_id                     = module.Data_Subnet_AU_East.id
  private_ip_address_allocation = "Static"
  private_ip_address            = var.es_private_ips[count.index]
  public_ip_address_id          = element(module.ES_Public_IP_AU_East.*.id, count.index)
  tags                          = local.common_tags
}
# locals {
#   es_ssh_public_key = <<-EOT
#     ${var.es_ssh_pub_key}
#   EOT
# }
# Create ES Linux Machines
resource "azurerm_linux_virtual_machine" "es_linux_virtual_machine_au_east" {
  count               = 3
  name                = "${var.customer}-${var.project}-es-vm-${count.index + 1}-${var.environment}-au-east"
  resource_group_name = data.azurerm_resource_group.WAPol_Data_Resource_Group_AU_East.name
  location            = var.location_au_east
  computer_name       = "${var.customer}${var.project}es${count.index + 1}${var.environment}aueast"
  size                = "Standard_A4_v2"
  admin_username      = "gsdevops"
  # encryption_at_host_enabled = "true"
  patch_assessment_mode                                  = "AutomaticByPlatform"
  patch_mode                                             = "AutomaticByPlatform"
  bypass_platform_safety_checks_on_user_schedule_enabled = true

  network_interface_ids = [
    element(module.ES_Network_Interface_AU_East.*.id, count.index)
  ]

  admin_ssh_key {
    username   = "gsdevops"
    public_key = local.es_ssh_public_key
  }

  os_disk {
    caching              = "ReadWrite"
    storage_account_type = "Standard_LRS"
    disk_size_gb         = "256"
  }

  source_image_reference {
    publisher = "Canonical"
    offer     = "UbuntuServer"
    sku       = "18.04-LTS"
    version   = "latest"
  }

  identity {
    type = "SystemAssigned"
  }

  tags = local.common_tags
}

resource "azurerm_virtual_machine_extension" "es_linux_virtual_machine_extension_gc_au_east" {
  count                     = 3
  name                      = "AzurePolicyForLinux"
  virtual_machine_id        = element(azurerm_linux_virtual_machine.es_linux_virtual_machine_au_east.*.id, count.index)
  publisher                 = "Microsoft.GuestConfiguration"
  type                      = "ConfigurationForLinux"
  type_handler_version      = "1.26"
  automatic_upgrade_enabled = true
}

resource "azurerm_virtual_machine_extension" "es_linux_virtual_machine_extension_am_au_east" {
  count                     = 3
  name                      = "AzureMonitorLinuxAgent"
  virtual_machine_id        = element(azurerm_linux_virtual_machine.es_linux_virtual_machine_au_east.*.id, count.index)
  publisher                 = "Microsoft.Azure.Monitor"
  type                      = "AzureMonitorLinuxAgent"
  type_handler_version      = "1.33"
  automatic_upgrade_enabled = true
}

#######################################
# Kibana VM
#######################################

# Create a public IP to assign to Kibana Linux machine
module "Kibana_Public_IP_AU_East" {
  source              = "../../../../modules/network_resources/public_ip"
  name                = "${var.customer}-${var.project}-kibana-public-ip-${var.environment}-au-east"
  resource_group_name = data.azurerm_resource_group.WAPol_Data_Resource_Group_AU_East.name
  location            = var.location_au_east
  sku                 = "Standard"
  domain_name_label   = "${var.customer}-${var.project}-kibana-${var.environment}-2"
  tags                = local.common_tags
}

# Create Network Interface to assign to Kibana ES Linux Machine
module "Kibana_Network_Interface_AU_East" {
  source                        = "../../../../modules/network_resources/network_interface"
  name                          = "${var.customer}_${var.project}_kibana_network_interface_${var.environment}_au_east"
  location                      = var.location_au_east
  resource_group_name           = data.azurerm_resource_group.WAPol_Data_Resource_Group_AU_East.name
  subnet_id                     = module.Data_Subnet_AU_East.id
  private_ip_address_allocation = "Static"
  private_ip_address            = var.kibana_private_ip
  public_ip_address_id          = module.Kibana_Public_IP_AU_East.id
  tags                          = local.common_tags
}
# locals {
#   kibana_ssh_public_key = <<-EOT
#     ${var.kibana_ssh_pub_key}
#   EOT
# }
# Create Kibana Linux Machine
resource "azurerm_linux_virtual_machine" "kibana_linux_virtual_machine_au_east" {
  name                = "${var.customer}-${var.project}-kibana-vm-${var.environment}-au-east"
  resource_group_name = data.azurerm_resource_group.WAPol_Data_Resource_Group_AU_East.name
  location            = var.location_au_east
  computer_name       = "${var.customer}${var.project}kibana${var.environment}aueast"
  size                = "Standard_A2_v2"
  admin_username      = "gsdevops"
  tags                = local.common_tags

  patch_assessment_mode                                  = "AutomaticByPlatform"
  patch_mode                                             = "AutomaticByPlatform"
  bypass_platform_safety_checks_on_user_schedule_enabled = true

  network_interface_ids = [
    module.Kibana_Network_Interface_AU_East.id,
  ]

  admin_ssh_key {
    username   = "gsdevops"
    public_key = local.kibana_ssh_public_key
  }

  os_disk {
    caching              = "ReadWrite"
    storage_account_type = "Standard_LRS"
    disk_size_gb         = "30"
  }

  source_image_reference {
    publisher = "Canonical"
    offer     = "UbuntuServer"
    sku       = "18.04-LTS"
    version   = "latest"
  }

  identity {
    type = "SystemAssigned"
  }
}

resource "azurerm_virtual_machine_extension" "kibana_linux_virtual_machine_extension_gc_au_east" {
  name                      = "AzurePolicyForLinux"
  virtual_machine_id        = azurerm_linux_virtual_machine.kibana_linux_virtual_machine_au_east.id
  publisher                 = "Microsoft.GuestConfiguration"
  type                      = "ConfigurationForLinux"
  type_handler_version      = "1.26"
  automatic_upgrade_enabled = true
}

resource "azurerm_virtual_machine_extension" "kibana_linux_virtual_machine_extension_am_au_east" {
  name                      = "AzureMonitorLinuxAgent"
  virtual_machine_id        = azurerm_linux_virtual_machine.kibana_linux_virtual_machine_au_east.id
  publisher                 = "Microsoft.Azure.Monitor"
  type                      = "AzureMonitorLinuxAgent"
  type_handler_version      = "1.33"
  automatic_upgrade_enabled = true
}

output "es_vm_resource_ids_au_east" {
  value = azurerm_linux_virtual_machine.es_linux_virtual_machine_au_east[*].id
  description = "The resource IDs of the Elasticsearch VMs"
}

output "kibana_vm_resource_id_au_east" {
  value = azurerm_linux_virtual_machine.kibana_linux_virtual_machine_au_east.id
  description = "The resource ID of the Kibana VM"
}

# Create a dedicated USR recovery services vault instead of using dev's
resource "azurerm_recovery_services_vault" "es_vm_recovery_services_vault_au_east" {
  name                = "${var.customer}-${var.project}-rsv-2-${var.environment}"
  location            = var.location_au_east
  resource_group_name = data.azurerm_resource_group.WAPol_Data_Resource_Group_AU_East.name
  sku                 = "Standard"
  soft_delete_enabled = true
  tags                = local.common_tags
}

resource "azurerm_backup_policy_vm" "backup_policy_au_east" {
  name                = "${var.customer}-${var.project}-bkpol-vm-2-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Data_Resource_Group_AU_East.name
  recovery_vault_name = azurerm_recovery_services_vault.es_vm_recovery_services_vault_au_east.name

  timezone = "UTC"

  backup {
    frequency = "Daily"
    time      = "22:00" # 06:00 am Perth Time
  }

  retention_daily {
    count = 7
  }
}

# Replace the data reference to dev vault
resource "azurerm_backup_protected_vm" "assign_backup_policy_kibana_vm_au_east" {
  resource_group_name = data.azurerm_resource_group.WAPol_Data_Resource_Group_AU_East.name
  recovery_vault_name = azurerm_recovery_services_vault.es_vm_recovery_services_vault_au_east.name
  source_vm_id        = azurerm_linux_virtual_machine.kibana_linux_virtual_machine_au_east.id
  backup_policy_id    = azurerm_backup_policy_vm.backup_policy_au_east.id
}

resource "azurerm_backup_protected_vm" "assign_backup_policy_es_vm_au_east" {
  count               = 3
  resource_group_name = data.azurerm_resource_group.WAPol_Data_Resource_Group_AU_East.name
  recovery_vault_name = azurerm_recovery_services_vault.es_vm_recovery_services_vault_au_east.name
  source_vm_id        = azurerm_linux_virtual_machine.es_linux_virtual_machine_au_east[count.index].id
  backup_policy_id    = azurerm_backup_policy_vm.backup_policy_au_east.id
}
