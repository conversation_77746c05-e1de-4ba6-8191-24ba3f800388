locals {
  common_tags = {
    owner       = var.owner
    customer    = var.customer
    environment = var.environment
    project     = var.project
  }
}

locals {
  motorola_ip_ranges = [
    "***************/28",
    "*************/24",
    "***************/28"
  ]
}

variable "owner" {
  type        = string
  description = "Owner Name"
}

variable "customer" {
  type        = string
  description = "Owner Name"
}

variable "project" {
  type        = string
  description = "Project Name"
}

variable "location" {
  type        = string
  description = "Location"
}

variable "environment" {
  type        = string
  description = "Environment"
}

variable "client_id" {
  type        = string
  description = "Azure Service Principal ID"
}

variable "client_secret" {
  type        = string
  description = "Azure Service Principal Secret"
}

variable "tenant_id" {
  type        = string
  description = "Azure Service Principal Tenant ID"
}

variable "subscription_id" {
  type        = string
  description = "Azure Subscription ID"
}

variable "data_subnet_cidr" {
  type        = list(string)
  description = "Virtual Network Address Range"
}

variable "es_private_ips" {
  description = "Create VMs with these IPs"
  type        = list(string)
  default     = ["***********", "***********", "***********"]
}

variable "kibana_private_ip" {
  description = "Create VM with this IP"
  type        = string
  default     = "***********"
}
variable "es_ssh_pub_key" {
  type        = string
  description = "SSH Public Key"
}

variable "kibana_ssh_pub_key" {
  type        = string
  description = "SSH Public Key"
}

variable "location_au_east" {
  type        = string
  description = "AU East Location"
}
