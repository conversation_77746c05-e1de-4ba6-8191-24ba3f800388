provider "azurerm" {
  features {}
}

terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "=4.14.0"
    }
  }
}

#Azure Backend
terraform {
  backend "azurerm" {
  }
}

#Define Azure resource group
module "Application_Vnet_Resource_Group" {
  source   = "../../../../modules/base_resources/resource_group"
  name     = var.resource_group_name
  location = var.location
  tags     = local.common_tags
}

#Define Azure Application Vnet
module "Application_Vnet" {
  source              = "../../../../modules/network_resources/virtual_network"
  name                = var.application_vnet_name
  location            = var.location
  resource_group_name = module.Application_Vnet_Resource_Group.name
  address_space       = var.application_vnet_address_space
  tags                = local.common_tags
}

#Define Gateway resource group
module "Gateway_Vnet_Resource_Group" {
  source   = "../../../../modules/base_resources/resource_group"
  name     = var.gateway_resource_group_name
  location = var.location
  tags     = local.common_tags
}

#Define Gateway Vnet
module "Gateway_Vnet" {
  source              = "../../../../modules/network_resources/virtual_network"
  name                = var.gateway_vnet_name
  location            = var.location
  resource_group_name = module.Gateway_Vnet_Resource_Group.name
  address_space       = var.gateway_vnet_address_space
  tags                = local.common_tags
}

#Define Vnet Peering Between Gateway Vnet And Application Vnet
module "Gateway_Application_Vnet_Peering" {
  source                       = "../../../../modules/network_resources/virtual_network_peering"
  name                         = "Gateway-to-Application-${var.environment}"
  resource_group_name          = module.Gateway_Vnet_Resource_Group.name
  virtual_network_name         = module.Gateway_Vnet.name
  remote_virtual_network_id    = module.Application_Vnet.id
  allow_virtual_network_access = "true"
  allow_forwarded_traffic      = "true"
}

#Define Vnet Peering Between Gateway Vnet And Application Vnet
module "Application_Gateway_Vnet_Peering" {
  source                       = "../../../../modules/network_resources/virtual_network_peering"
  name                         = "Application-to-Gateway-${var.environment}"
  resource_group_name          = module.Application_Vnet_Resource_Group.name
  virtual_network_name         = module.Application_Vnet.name
  remote_virtual_network_id    = module.Gateway_Vnet.id
  allow_virtual_network_access = "true"
  allow_forwarded_traffic      = "true"
}

#Access Existing Shared Services resource group
data "azurerm_resource_group" "Management_Vnet_Resource_Group" {
  name = var.management_resource_group_name
}

#Define Management Vnet
module "Management_Vnet" {
  source              = "../../../../modules/network_resources/virtual_network"
  name                = var.management_vnet_name
  location            = var.location
  resource_group_name = data.azurerm_resource_group.Management_Vnet_Resource_Group.name
  address_space       = var.management_vnet_address_space
  tags                = local.common_tags
}

#Define Vnet Peering Between Management Vnet And Application Vnet
module "Management_Application_Vnet_Peering" {
  source                       = "../../../../modules/network_resources/virtual_network_peering"
  name                         = "Management-to-Application-${var.environment}"
  resource_group_name          = data.azurerm_resource_group.Management_Vnet_Resource_Group.name
  virtual_network_name         = module.Management_Vnet.name
  remote_virtual_network_id    = module.Application_Vnet.id
  allow_virtual_network_access = "true"
  allow_forwarded_traffic      = "true"
}

#Define Vnet Peering Between Management Vnet And Application Vnet
module "Application_Management_Vnet_Peering" {
  source                       = "../../../../modules/network_resources/virtual_network_peering"
  name                         = "Application-to-Management-${var.environment}"
  resource_group_name          = module.Application_Vnet_Resource_Group.name
  virtual_network_name         = module.Application_Vnet.name
  remote_virtual_network_id    = module.Management_Vnet.id
  allow_virtual_network_access = "true"
  allow_forwarded_traffic      = "true"
}

# Declare Network Resources in AU East Region
#Define Azure resource group
module "Application_Vnet_Resource_Group_AU_East" {
  source   = "../../../../modules/base_resources/resource_group"
  name     = "${var.customer}-${var.project}-rg-2-${var.environment}"
  location = var.location_au_east
  tags     = local.common_tags
}

#Define Azure Application Vnet
module "Application_Vnet_AU_East" {
  source              = "../../../../modules/network_resources/virtual_network"
  name                = "${var.customer}-${var.project}-app-vnet-2-${var.environment}"
  location            = var.location_au_east
  resource_group_name = module.Application_Vnet_Resource_Group_AU_East.name
  address_space       = var.application_vnet_address_space
  tags                = local.common_tags
}

#Define Gateway resource group
module "Gateway_Vnet_Resource_Group_AU_East" {
  source   = "../../../../modules/base_resources/resource_group"
  name     = "${var.customer}-${var.project}-gateway-rg-2-${var.environment}"
  location = var.location_au_east
  tags     = local.common_tags
}

#Define Gateway Vnet
module "Gateway_Vnet_AU_East" {
  source              = "../../../../modules/network_resources/virtual_network"
  name                = "${var.customer}-${var.project}-gateway-vnet-2-${var.environment}"
  location            = var.location_au_east
  resource_group_name = module.Gateway_Vnet_Resource_Group_AU_East.name
  address_space       = var.gateway_vnet_address_space
  tags                = local.common_tags
}

#Define Vnet Peering Between Gateway Vnet And Application Vnet
module "Gateway_Application_Vnet_Peering_AU_East" {
  source                       = "../../../../modules/network_resources/virtual_network_peering"
  name                         = "Gateway-to-Application-2-${var.environment}"
  resource_group_name          = module.Gateway_Vnet_Resource_Group_AU_East.name
  virtual_network_name         = module.Gateway_Vnet_AU_East.name
  remote_virtual_network_id    = module.Application_Vnet_AU_East.id
  allow_virtual_network_access = "true"
  allow_forwarded_traffic      = "true"
}

#Define Vnet Peering Between Gateway Vnet And Application Vnet
module "Application_Gateway_Vnet_Peering_AU_East" {
  source                       = "../../../../modules/network_resources/virtual_network_peering"
  name                         = "Application-to-Gateway-2-${var.environment}"
  resource_group_name          = module.Application_Vnet_Resource_Group_AU_East.name
  virtual_network_name         = module.Application_Vnet_AU_East.name
  remote_virtual_network_id    = module.Gateway_Vnet_AU_East.id
  allow_virtual_network_access = "true"
  allow_forwarded_traffic      = "true"
}

#Access Existing Shared Services resource group
data "azurerm_resource_group" "Management_Vnet_Resource_Group_AU_East" {
  name = "${var.customer}-${var.project}-shared-services-rg-2-${var.environment}"
}

#Define Management Vnet
module "Management_Vnet_AU_East" {
  source              = "../../../../modules/network_resources/virtual_network"
  name                = "${var.customer}-${var.project}-management-vnet-2-${var.environment}"
  location            = var.location_au_east
  resource_group_name = data.azurerm_resource_group.Management_Vnet_Resource_Group_AU_East.name
  address_space       = var.management_vnet_address_space
  tags                = local.common_tags
}

#Define Vnet Peering Between Management Vnet And Application Vnet
module "Management_Application_Vnet_Peering_AU_East" {
  source                       = "../../../../modules/network_resources/virtual_network_peering"
  name                         = "Management-to-Application-2-${var.environment}"
  resource_group_name          = data.azurerm_resource_group.Management_Vnet_Resource_Group_AU_East.name
  virtual_network_name         = module.Management_Vnet_AU_East.name
  remote_virtual_network_id    = module.Application_Vnet_AU_East.id
  allow_virtual_network_access = "true"
  allow_forwarded_traffic      = "true"
}

#Define Vnet Peering Between Management Vnet And Application Vnet
module "Application_Management_Vnet_Peering_AU_East" {
  source                       = "../../../../modules/network_resources/virtual_network_peering"
  name                         = "Application-to-Management-2-${var.environment}"
  resource_group_name          = module.Application_Vnet_Resource_Group_AU_East.name
  virtual_network_name         = module.Application_Vnet_AU_East.name
  remote_virtual_network_id    = module.Management_Vnet_AU_East.id
  allow_virtual_network_access = "true"
  allow_forwarded_traffic      = "true"
}
