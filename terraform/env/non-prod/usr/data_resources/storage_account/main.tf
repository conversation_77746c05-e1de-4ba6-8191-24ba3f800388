provider "azurerm" {
  tenant_id       = var.tenant_id
  features {}
}

terraform {
  backend "azurerm" {
  }
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "=3.102.0"
    }
  }
}

#Access Existing Azure resource group
data "azurerm_resource_group" "WAPol_Resource_Group" {
  name = var.resource_group_name
}

#Access Existing Azure Virtual Network
data "azurerm_virtual_network" "WAPol_App_Virtual_Network" {
  name                = "wapol-wpm-app-vnet-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Resource_Group.name
}

#Access Existing Azure Subnet
data "azurerm_subnet" "WAPol_Aks_Subnet" {
  name                 = "wapol-wpm-aks-subnet-${var.environment}"
  virtual_network_name = data.azurerm_virtual_network.WAPol_App_Virtual_Network.name
  resource_group_name  = data.azurerm_resource_group.WAPol_Resource_Group.name
}

#Define Storage Account to host application data
module "Storage_Account_App_Data" {
  source                           = "../../../../../modules/storage_resources/storage_account"
  name                             = "wapolwpmappdata${var.environment}"
  resource_group_name              = data.azurerm_resource_group.WAPol_Resource_Group.name
  location                         = var.location
  allow_nested_items_to_be_public  = false
  cross_tenant_replication_enabled = false
  virtual_network_subnet_ids       = [data.azurerm_subnet.WAPol_Aks_Subnet.id]
  ip_rules = concat(
    local.motorola_ip_ranges
  )
  tags = local.common_tags
  private_link_access = [{
    endpoint_resource_id = "/subscriptions/d0304bbb-bb3d-4d7c-acc3-cf396dd16ba8/resourcegroups/*/providers/Microsoft.DataFactory/factories/*"
    endpoint_tenant_id   = "988004e3-b844-4b61-a48e-4fa2e005ab70"
  },{
    endpoint_resource_id = "/subscriptions/d0304bbb-bb3d-4d7c-acc3-cf396dd16ba8/resourcegroups/wapol-wpm-shared-services-rg-dev/providers/Microsoft.DataFactory/factories/wapol-wpm-adf-usr"
    endpoint_tenant_id   = "988004e3-b844-4b61-a48e-4fa2e005ab70"
  }]
}

#Define Storage Container for User Preference API
module "User_Persistence_Storage_Container" {
  source               = "../../../../../modules/storage_resources/storage_container"
  name                 = "user-persistence-${var.environment}"
  storage_account_name = module.Storage_Account_App_Data.name
}

#Define Storage Container for User Preference API - Training
module "User_Persistence_Storage_Container_Training" {
  source               = "../../../../../modules/storage_resources/storage_container"
  name                 = "user-persistence-${var.training_environment}"
  storage_account_name = module.Storage_Account_App_Data.name
}


# Declare Resource in AU East Region
#Access Existing Azure resource group
data "azurerm_resource_group" "WAPol_Resource_Group_AU_East" {
  name = "${var.customer}-${var.project}-rg-2-${var.environment}"
}

#Access Existing Azure Virtual Network
data "azurerm_virtual_network" "WAPol_App_Virtual_Network_AU_East" {
  name                = "${var.customer}-${var.project}-app-vnet-2-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Resource_Group_AU_East.name
}

#Access Existing Azure Subnet
data "azurerm_subnet" "WAPol_Aks_Subnet_AU_East" {
  name                 = "${var.customer}-${var.project}-aks-subnet-2-${var.environment}"
  virtual_network_name = data.azurerm_virtual_network.WAPol_App_Virtual_Network_AU_East.name
  resource_group_name  = data.azurerm_resource_group.WAPol_Resource_Group_AU_East.name
}

#Define Storage Account to host application data
module "Storage_Account_App_Data_AU_East" {
  source                           = "../../../../../modules/storage_resources/storage_account"
  name                             = "wapolwpmappdata2${var.environment}"
  resource_group_name              = data.azurerm_resource_group.WAPol_Resource_Group_AU_East.name
  location                         = var.location_au_east
  allow_nested_items_to_be_public  = false
  cross_tenant_replication_enabled = false
  virtual_network_subnet_ids       = [data.azurerm_subnet.WAPol_Aks_Subnet_AU_East.id]
  ip_rules = concat(
    local.motorola_ip_ranges
  )
  tags = local.common_tags
}

#Define Storage Container for User Preference API
module "User_Persistence_Storage_Container_AU_East" {
  source               = "../../../../../modules/storage_resources/storage_container"
  name                 = "user-persistence-${var.environment}"
  storage_account_name = module.Storage_Account_App_Data_AU_East.name
}

#Define Storage Container for User Preference API - Training
module "User_Persistence_Storage_Container_Training_AU_East" {
  source               = "../../../../../modules/storage_resources/storage_container"
  name                 = "user-persistence-${var.training_environment}"
  storage_account_name = module.Storage_Account_App_Data_AU_East.name
}
