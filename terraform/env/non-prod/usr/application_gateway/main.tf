provider "azurerm" {
  subscription_id = var.subscription_id
  client_id       = var.client_id
  client_secret   = var.client_secret
  tenant_id       = var.tenant_id
  features {}
}

terraform {
  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "=4.6.0"
    }
  }
}

#Azure Backend
terraform {
  backend "azurerm" {
  }
}

#Access Existing App Usr resource group
data "azurerm_resource_group" "WAPol_Gateway_Resource_Group" {
  name = "${var.customer}-${var.project}-gateway-rg-${var.environment}"
}

#Access Existing Azure Virtual Network
data "azurerm_virtual_network" "WAPol_Gateway_Virtual_Network" {
  name                = "${var.customer}-${var.project}-gateway-vnet-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Gateway_Resource_Group.name
}

#Create API GW Subnet
module "Application_Gateway_Subnet" {
  source               = "../../../../modules/network_resources/subnet"
  name                 = "${var.customer}-${var.project}-agw-subnet-${var.environment}"
  resource_group_name  = data.azurerm_resource_group.WAPol_Gateway_Resource_Group.name
  virtual_network_name = data.azurerm_virtual_network.WAPol_Gateway_Virtual_Network.name
  address_prefixes     = var.application_gateway_subnet_cidr
  service_endpoints    = ["Microsoft.KeyVault"]
}

#Create Network Security Group
module "Application_Gateway_Network_Security_Group" {
  source              = "../../../../modules/network_resources/network_security_group"
  name                = "${var.customer}-${var.project}-agw-nsg-${var.environment}"
  location            = var.location
  resource_group_name = data.azurerm_resource_group.WAPol_Gateway_Resource_Group.name
  tags                = local.common_tags
}

#Create Network Security Group Association
module "Application_Gateway_Network_Security_Group_Association" {
  source                    = "../../../../modules/network_resources/network_security_group_association"
  subnet_id                 = module.Application_Gateway_Subnet.id
  network_security_group_id = module.Application_Gateway_Network_Security_Group.id
}

#Add rule for ssh access from motorola ip range
module "Application_Gateway_NSG_Rule_SSH_From_Mot" {
  source                      = "../../../../modules/network_resources/network_security_rule"
  name                        = "${var.customer}-${var.project}-data-nsg-rule-1-${var.environment}"
  resource_group_name         = data.azurerm_resource_group.WAPol_Gateway_Resource_Group.name
  network_security_group_name = module.Application_Gateway_Network_Security_Group.name
  protocol                    = "Tcp"
  priority                    = "100"
  direction                   = "Inbound"
  access                      = "Allow"
  source_port_range           = "*"
  destination_port_ranges     = ["443"]
  destination_address_prefix  = "*"
  source_address_prefix       = "*"
}

#Add rule for default ports 65200 - 65535
module "Application_Gateway_NSG_Rule_Default_Ports" {
  source                      = "../../../../modules/network_resources/network_security_rule"
  name                        = "${var.customer}-${var.project}-data-nsg-rule-2-${var.environment}"
  resource_group_name         = data.azurerm_resource_group.WAPol_Gateway_Resource_Group.name
  network_security_group_name = module.Application_Gateway_Network_Security_Group.name
  protocol                    = "Tcp"
  priority                    = "110"
  direction                   = "Inbound"
  access                      = "Allow"
  source_port_range           = "*"
  destination_port_ranges     = ["65200-65535"]
  destination_address_prefix  = "*"
  source_address_prefix       = "*"
}

#Create a public ip to assign to application gateway
module "Application_Gateway_Public_IP" {
  source              = "../../../../modules/network_resources/public_ip"
  name                = "${var.customer}-${var.project}-agw-public-ip-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Gateway_Resource_Group.name
  location            = var.location
  sku                 = "Standard"
  domain_name_label   = "${var.customer}-${var.project}-agw-${var.environment}"
  tags                = local.common_tags
}

#&nbsp;since these variables are re-used - a locals block makes this more maintainable
#

locals {
  # --- Existing Locals (I've kept them for reference but they are now replaced by the structured map below) ---
  # backend_address_pool_name      = "${var.customer}-${var.project}-agw-beap-${var.environment}"
  # frontend_port_name             = "${var.customer}-${var.project}-agw-feport-${var.environment}"
  # frontend_ip_configuration_name = "${var.customer}-${var.project}-agw-feip-${var.environment}"
  # http_setting_name              = "${var.customer}-${var.project}-agw-be-htst-${var.environment}"
  # listener_name                  = "${var.customer}-${var.project}-agw-httplstn-${var.environment}"
  # request_routing_rule_name      = "${var.customer}-${var.project}-agw-rqrt-${var.environment}"
  # health_probe_name              = "${var.customer}-${var.project}-agw-probe-${var.environment}"
  # rewrite_rule_set_name          = "${var.customer}-${var.project}-agw-rrs-${var.environment}"
  # ... and so on

  # --- NEW: Structured configuration for dynamic blocks ---
  app_gateway_frontend_port_name             = "${var.customer}-${var.project}-agw-feport-${var.environment}"
  app_gateway_frontend_ip_configuration_name = "${var.customer}-${var.project}-agw-feip-${var.environment}"
  app_gateway_rewrite_rule_set_name          = "${var.customer}-${var.project}-agw-rrs-${var.environment}"

  backend_address_pools = {
    api = {
      name  = "${var.customer}-${var.project}-agw-beap-${var.environment}"
      fqdns = ["${var.customer}-api-internal-${var.environment}.pscore.cloud"]
    }
    kibana = {
      name  = "${var.customer}-${var.project}-kibana-beap-${var.environment}"
      fqdns = ["${var.customer}-kibana-internal-${var.environment}.pscore.cloud"]
    }
  }

  # Backend pools for AU East AGW pointing to AU East AKS cluster
  backend_address_pools_au_east = {
    api = {
      name  = "${var.customer}-${var.project}-agw-beap-${var.environment}"
      fqdns = ["${var.customer}-api-internal-${var.environment}.pscore.cloud"]
    }
    kibana = {
      name  = "${var.customer}-${var.project}-kibana-beap-${var.environment}"
      fqdns = ["${var.customer}-kibana-internal-${var.environment}.pscore.cloud"]
    }
  }

  backend_http_settings = {
    api = {
      name                  = "${var.customer}-${var.project}-agw-be-htst-${var.environment}"
      cookie_based_affinity = "Disabled"
      port                  = 80
      protocol              = "Http"
      request_timeout       = 60
      probe_name            = "${var.customer}-${var.project}-agw-probe-${var.environment}"
    }
    kibana = {
      name                  = "${var.customer}-${var.project}-kibana-be-htst-${var.environment}"
      cookie_based_affinity = "Disabled"
      port                  = 5601
      protocol              = "Http"
      request_timeout       = 60
      probe_name            = "${var.customer}-${var.project}-kibana-probe-${var.environment}"
    }
  }

  probes = {
    api = {
      name                = "${var.customer}-${var.project}-agw-probe-${var.environment}"
      protocol            = "Http"
      host                = "127.0.0.1"
      path                = "/healthz/ready"
      interval            = 30
      timeout             = 30
      unhealthy_threshold = 3
      port                = 15021
      match = { # Note: Added explicit match block to prevent diffs from API-added defaults
        status_code = ["200-399"]
      }
    }
    kibana = {
      name                = "${var.customer}-${var.project}-kibana-probe-${var.environment}"
      protocol            = "Http"
      host                = "127.0.0.1"
      path                = "/"
      interval            = 30
      timeout             = 30
      unhealthy_threshold = 3
      port                = 5601
      match = {
        status_code = ["200-399"]
      }
    }
  }

  http_listeners = {
    api = {
      name                           = "${var.customer}-${var.project}-agw-httplstn-${var.environment}"
      frontend_ip_configuration_name = local.app_gateway_frontend_ip_configuration_name
      frontend_port_name             = local.app_gateway_frontend_port_name
      protocol                       = "Https"
      ssl_certificate_name           = "pscore.cloud.crt"
      host_names                     = ["wapol-agw-usr.pscore.cloud", "wapol-agw-training.pscore.cloud"]
      firewall_policy_id             = azurerm_web_application_firewall_policy.web_application_firewall_policy_api.id
    }
    kibana = {
      name                           = "${var.customer}-${var.project}-kibana-httplstn-${var.environment}"
      frontend_ip_configuration_name = local.app_gateway_frontend_ip_configuration_name
      frontend_port_name             = local.app_gateway_frontend_port_name
      protocol                       = "Https"
      ssl_certificate_name           = "pscore.cloud.crt"
      host_names                     = ["wapol-kibana-usr.pscore.cloud"]
      firewall_policy_id             = azurerm_web_application_firewall_policy.web_application_firewall_policy_kibana.id
    }
  }

  request_routing_rules = {
    api = {
      name                       = "${var.customer}-${var.project}-agw-rqrt-${var.environment}"
      rule_type                  = "Basic"
      priority                   = 1
      http_listener_name         = local.http_listeners.api.name
      backend_address_pool_name  = local.backend_address_pools.api.name
      backend_http_settings_name = local.backend_http_settings.api.name
      rewrite_rule_set_name      = local.app_gateway_rewrite_rule_set_name
    }
    kibana = {
      name                       = "${var.customer}-${var.project}-kibana-rqrt-${var.environment}"
      rule_type                  = "Basic"
      priority                   = 11
      http_listener_name         = local.http_listeners.kibana.name
      backend_address_pool_name  = local.backend_address_pools.kibana.name
      backend_http_settings_name = local.backend_http_settings.kibana.name
      rewrite_rule_set_name      = null # No rewrite for kibana
    }
  }

  # --- ADD THIS NEW MAP FOR THE AU EAST GATEWAY ---
  http_listeners_au_east = {
    api = {
      name                           = "${var.customer}-${var.project}-agw-httplstn-${var.environment}"
      frontend_ip_configuration_name = local.app_gateway_frontend_ip_configuration_name
      frontend_port_name             = local.app_gateway_frontend_port_name
      protocol                       = "Https"
      ssl_certificate_name           = "pscore.cloud.crt"
      host_names                     = ["wapol-agw-usr.pscore.cloud", "wapol-agw-training.pscore.cloud", "wapol-agw-usr-2.pscore.cloud", "wapol-agw-training-2.pscore.cloud"]
      # Reference the AU EAST WAF policy
      firewall_policy_id             = azurerm_web_application_firewall_policy.web_application_firewall_policy_api_au_east.id
      require_sni                    = false
    }
    kibana = {
      name                           = "${var.customer}-${var.project}-kibana-httplstn-${var.environment}"
      frontend_ip_configuration_name = local.app_gateway_frontend_ip_configuration_name
      frontend_port_name             = local.app_gateway_frontend_port_name
      protocol                       = "Https"
      ssl_certificate_name           = "pscore.cloud.crt"
      host_names                     = ["wapol-kibana2-usr.pscore.cloud"]
      # Reference the AU EAST WAF policy
      firewall_policy_id             = azurerm_web_application_firewall_policy.web_application_firewall_policy_kibana_au_east.id
      require_sni                    = true
    }
  }
}

#Create application gateway
resource "azurerm_application_gateway" "application_gateway_usr" {
  name                = "${var.customer}-${var.project}-agw-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Gateway_Resource_Group.name
  location            = var.location

  sku {
    name     = "WAF_v2"
    tier     = "WAF_v2"
    capacity = 1
  }

  gateway_ip_configuration {
    name      = "${var.customer}-${var.project}-agw-ip-configuration-${var.environment}"
    subnet_id = module.Application_Gateway_Subnet.id
  }

  frontend_port {
    name = local.app_gateway_frontend_port_name
    port = 443
  }

  frontend_ip_configuration {
    name                 = local.app_gateway_frontend_ip_configuration_name
    public_ip_address_id = module.Application_Gateway_Public_IP.id
  }

  dynamic "backend_address_pool" {
    for_each = local.backend_address_pools
    content {
      name  = backend_address_pool.value.name
      fqdns = backend_address_pool.value.fqdns
    }
  }

  dynamic "backend_http_settings" {
    for_each = local.backend_http_settings
    content {
      name                  = backend_http_settings.value.name
      cookie_based_affinity = backend_http_settings.value.cookie_based_affinity
      port                  = backend_http_settings.value.port
      protocol              = backend_http_settings.value.protocol
      request_timeout       = backend_http_settings.value.request_timeout
      probe_name            = backend_http_settings.value.probe_name
    }
  }

  dynamic "probe" {
    for_each = local.probes
    content {
      name                = probe.value.name
      protocol            = probe.value.protocol
      host                = probe.value.host
      path                = probe.value.path
      interval            = probe.value.interval
      timeout             = probe.value.timeout
      unhealthy_threshold = probe.value.unhealthy_threshold
      port                = probe.value.port
      dynamic "match" {
        for_each = probe.value.match != null ? [probe.value.match] : []
        content {
          status_code = match.value.status_code
        }
      }
    }
  }

  dynamic "http_listener" {
    for_each = local.http_listeners
    content {
      name                           = http_listener.value.name
      frontend_ip_configuration_name = http_listener.value.frontend_ip_configuration_name
      frontend_port_name             = http_listener.value.frontend_port_name
      protocol                       = http_listener.value.protocol
      ssl_certificate_name           = http_listener.value.ssl_certificate_name
      host_names                     = http_listener.value.host_names
      firewall_policy_id             = http_listener.value.firewall_policy_id
    }
  }

  dynamic "request_routing_rule" {
    for_each = local.request_routing_rules
    content {
      name                       = request_routing_rule.value.name
      rule_type                  = request_routing_rule.value.rule_type
      priority                   = request_routing_rule.value.priority
      http_listener_name         = request_routing_rule.value.http_listener_name
      backend_address_pool_name  = request_routing_rule.value.backend_address_pool_name
      backend_http_settings_name = request_routing_rule.value.backend_http_settings_name
      rewrite_rule_set_name      = request_routing_rule.value.rewrite_rule_set_name
    }
  }

  ssl_certificate {
    name                = "pscore.cloud.crt"
    key_vault_secret_id = "https://wapol-wpm-kv-usr.vault.azure.net/secrets/pscoreCloudCrt"
  }

  identity {
    type         = "UserAssigned"
    identity_ids = [azurerm_user_assigned_identity.application_gateway_identity.id]
  }

  rewrite_rule_set {
    name = local.app_gateway_rewrite_rule_set_name

    rewrite_rule {
      name          = "rewrite-rule-1"
      rule_sequence = 100

      condition {
        variable = "var_host"
        pattern  = "wapol-agw-${var.environment}.pscore.cloud"
      }
      condition {
        variable = "var_uri_path"
        pattern  = "/referencedata/(.*)/item"
      }

      condition {
        variable = "var_query_string"
        pattern  = "lastUpdated=(.*)&skip=0&take=10000"
      }
      url {
        path         = "/referencedata/{var_uri_path_1}/item"
        query_string = "lastUpdated={var_query_string_1}"
      }
    }

    rewrite_rule {
      name          = "rewrite-rule-2"
      rule_sequence = 101

      condition {
        variable = "var_host"
        pattern  = "wapol-agw-${var.environment}.pscore.cloud"
      }
      condition {
        variable = "var_uri_path"
        pattern  = "/referencedata/(.*)/item"
      }

      condition {
        variable = "var_query_string"
        pattern  = "skip=0&take=10000"
      }
      url {
        path         = "/referencedata/{var_uri_path_1}/item"
        query_string = ""
      }
    }
  }

  firewall_policy_id = azurerm_web_application_firewall_policy.web_application_firewall_policy.id

  depends_on = [module.Application_Gateway_NSG_Rule_Default_Ports]
  tags       = local.common_tags
}


resource "azurerm_user_assigned_identity" "application_gateway_identity" {
  location            = var.location
  name                = "${var.customer}-${var.project}-agw-id-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Gateway_Resource_Group.name
  tags                = local.common_tags
}

#create WAF policy
#global waf rule
resource "azurerm_web_application_firewall_policy" "web_application_firewall_policy" {
  name                = "${var.customer}-${var.project}-agw-waf-policy-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Gateway_Resource_Group.name
  location            = var.location

  policy_settings {
    enabled                     = true
    mode                        = "Prevention"
    request_body_check          = false
    file_upload_limit_in_mb     = 100
    max_request_body_size_in_kb = 128
  }

  managed_rules {
    managed_rule_set {
      type    = "OWASP"
      version = "3.2"
    }
  }

  tags = local.common_tags
}

#per-site waf rule - api
resource "azurerm_web_application_firewall_policy" "web_application_firewall_policy_api" {
  name                = "${var.customer}-${var.project}-agw-waf-policy-api-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Gateway_Resource_Group.name
  location            = var.location

  custom_rules {
    name      = "allowSpecificIpForStarlink"
    priority  = 1
    rule_type = "MatchRule"
    match_conditions {
      match_variables {
        variable_name = "RemoteAddr"
      }
      operator           = "IPMatch"
      negation_condition = false
      match_values       = ["*************/32", "************/32", "*************/32", "************/32", "*************/32", "*************/32"]
    }

    action = "Allow"
  }

  custom_rules {
    name      = "allowSpecificIpForSituationalAlertExternalApi"
    priority  = 2
    rule_type = "MatchRule"
    match_conditions {
      match_variables {
        variable_name = "RemoteAddr"
      }
      operator           = "IPMatch"
      negation_condition = false
      match_values       = ["***********/32", "***********/32"]
    }
    match_conditions {
      match_variables {
        variable_name = "RequestUri"
      }

      operator           = "BeginsWith"
      negation_condition = false
      match_values       = ["/situational-alert-external-api/v1/external"]
    }
    match_conditions {
      match_variables {
        variable_name = "RequestHeaders"
        selector      = "X-Api-Key"
      }

      operator           = "Regex"
      negation_condition = true
      match_values       = ["^$"]
    }

    action = "Allow"
  }

  custom_rules {
    name      = "geoAllowAU"
    priority  = 3
    rule_type = "MatchRule"

    match_conditions {
      match_variables {
        variable_name = "RemoteAddr"
      }

      operator           = "GeoMatch"
      negation_condition = true
      match_values       = ["AU"]
    }

    action = "Block"
  }

  custom_rules {
    name      = "allowBackendPath"
    priority  = 4
    rule_type = "MatchRule"

    match_conditions {
      match_variables {
        variable_name = "RequestUri"
      }

      operator           = "BeginsWith"
      negation_condition = false
      match_values       = ["/cad/", "/entity/", "/event-publisher/", "/persistence/", "/referencedata/", "/reference-data/", "/situational-alert-external-api/", "/situational-alert/"]
    }

    match_conditions {
      match_variables {
        variable_name = "RequestHeaders"
        selector      = "X-User-ID"
      }

      operator           = "Regex"
      negation_condition = false
      match_values       = ["[a-zA-Z0-9]"]
    }

    action = "Allow"
  }

  custom_rules {
    name      = "allowTransactionWithoutXUserIdHeader"
    priority  = 5
    rule_type = "MatchRule"

    match_conditions {
      match_variables {
        variable_name = "RequestUri"
      }

      operator           = "BeginsWith"
      negation_condition = false
      match_values       = ["/cad/p1/device/deregister", "/persistence/v1/data"]
    }

    action = "Allow"
  }

  custom_rules {
    name      = "blockAllMethodAsDefault"
    enabled   = false
    priority  = 6
    rule_type = "MatchRule"

    match_conditions {
      match_variables {
        variable_name = "RequestMethod"
      }

      operator           = "Regex"
      negation_condition = false
      match_values       = ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD", "TRACE", "CONNECT"]
    }

    action = "Block"
  }

  policy_settings {
    enabled                     = true
    mode                        = "Prevention"
    request_body_check          = false
    file_upload_limit_in_mb     = 100
    max_request_body_size_in_kb = 128
  }

  managed_rules {
    managed_rule_set {
      type    = "OWASP"
      version = "3.2"
    }
  }

  tags = local.common_tags
}

#per-site waf rule - kibana
resource "azurerm_web_application_firewall_policy" "web_application_firewall_policy_kibana" {
  name                = "${var.customer}-${var.project}-agw-waf-policy-kibana-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Gateway_Resource_Group.name
  location            = var.location


  custom_rules {
    name      = "allowSpecificIpAndDomain"
    priority  = 1
    rule_type = "MatchRule"
    match_conditions {
      match_variables {
        variable_name = "RemoteAddr"
      }
      operator           = "IPMatch"
      negation_condition = false
      match_values       = ["***********/16"]
    }
    match_conditions {
      match_variables {
        variable_name = "RequestHeaders"
        selector      = "host"
      }

      operator           = "Contains"
      negation_condition = false
      match_values       = ["wapol-kibana-usr.pscore.cloud"]
    }

    action = "Allow"
  }

  custom_rules {
    name      = "blockAllMethodAsDefault"
    priority  = 2
    rule_type = "MatchRule"

    match_conditions {
      match_variables {
        variable_name = "RequestMethod"
      }

      operator           = "Regex"
      negation_condition = false
      match_values       = ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD", "TRACE", "CONNECT"]
    }

    action = "Block"
  }

  policy_settings {
    enabled                     = true
    mode                        = "Prevention"
    request_body_check          = false
    file_upload_limit_in_mb     = 100
    max_request_body_size_in_kb = 128
  }

  managed_rules {
    managed_rule_set {
      type    = "OWASP"
      version = "3.2"
    }
  }

  tags = local.common_tags
}


# declare resource in AU East Region
#Access Existing App Usr resource group
data "azurerm_resource_group" "WAPol_Gateway_Resource_Group_AU_East" {
  name = "${var.customer}-${var.project}-gateway-rg-2-${var.environment}"
}

#Access Existing Azure Virtual Network
data "azurerm_virtual_network" "WAPol_Gateway_Virtual_Network_AU_East" {
  name                = "${var.customer}-${var.project}-gateway-vnet-2-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Gateway_Resource_Group_AU_East.name
}

#Create API GW Subnet
module "Application_Gateway_Subnet_AU_East" {
  source               = "../../../../modules/network_resources/subnet"
  name                 = "${var.customer}-${var.project}-agw-subnet-2-${var.environment}"
  resource_group_name  = data.azurerm_resource_group.WAPol_Gateway_Resource_Group_AU_East.name
  virtual_network_name = data.azurerm_virtual_network.WAPol_Gateway_Virtual_Network_AU_East.name
  address_prefixes     = var.application_gateway_subnet_cidr
  service_endpoints    = ["Microsoft.KeyVault"]
}

#Create Network Security Group
module "Application_Gateway_Network_Security_Group_AU_East" {
  source              = "../../../../modules/network_resources/network_security_group"
  name                = "${var.customer}-${var.project}-agw-nsg-2-${var.environment}"
  location            = var.location_au_east
  resource_group_name = data.azurerm_resource_group.WAPol_Gateway_Resource_Group_AU_East.name
  tags                = local.common_tags
}

#Create Network Security Group Association
module "Application_Gateway_Network_Security_Group_Association_Group_AU_East" {
  source                    = "../../../../modules/network_resources/network_security_group_association"
  subnet_id                 = module.Application_Gateway_Subnet_AU_East.id
  network_security_group_id = module.Application_Gateway_Network_Security_Group_AU_East.id
}

#Add rule for ssh access from motorola ip range
module "Application_Gateway_NSG_Rule_SSH_From_Mot_AU_East" {
  source                      = "../../../../modules/network_resources/network_security_rule"
  name                        = "${var.customer}-${var.project}-data-nsg-rule-1-aueast-${var.environment}"
  resource_group_name         = data.azurerm_resource_group.WAPol_Gateway_Resource_Group_AU_East.name
  network_security_group_name = module.Application_Gateway_Network_Security_Group_AU_East.name
  protocol                    = "Tcp"
  priority                    = "100"
  direction                   = "Inbound"
  access                      = "Allow"
  source_port_range           = "*"
  destination_port_ranges     = ["443"]
  destination_address_prefix  = "*"
  source_address_prefix       = "*"
}

#Add rule for default ports 65200 - 65535
module "Application_Gateway_NSG_Rule_Default_Ports_AU_East" {
  source                      = "../../../../modules/network_resources/network_security_rule"
  name                        = "${var.customer}-${var.project}-data-nsg-rule-2-aueast-${var.environment}"
  resource_group_name         = data.azurerm_resource_group.WAPol_Gateway_Resource_Group_AU_East.name
  network_security_group_name = module.Application_Gateway_Network_Security_Group_AU_East.name
  protocol                    = "Tcp"
  priority                    = "110"
  direction                   = "Inbound"
  access                      = "Allow"
  source_port_range           = "*"
  destination_port_ranges     = ["65200-65535"]
  destination_address_prefix  = "*"
  source_address_prefix       = "*"
}

#Create a public ip to assign to application gateway
module "Application_Gateway_Public_IP_AU_East" {
  source              = "../../../../modules/network_resources/public_ip"
  name                = "${var.customer}-${var.project}-agw-public-ip-2-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Gateway_Resource_Group_AU_East.name
  location            = var.location_au_east
  sku                 = "Standard"
  domain_name_label   = "${var.customer}-${var.project}-agw-2-${var.environment}"
  tags                = local.common_tags
}


#Create application gateway
resource "azurerm_application_gateway" "application_gateway_usr_au_east" {
  name                = "${var.customer}-${var.project}-agw-2-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Gateway_Resource_Group_AU_East.name
  location            = var.location_au_east

  sku {
    name     = "WAF_v2"
    tier     = "WAF_v2"
    capacity = 1
  }

  gateway_ip_configuration {
    name      = "${var.customer}-${var.project}-agw-ip-configuration-${var.environment}"
    subnet_id = module.Application_Gateway_Subnet_AU_East.id
  }

  frontend_port {
    name = local.app_gateway_frontend_port_name
    port = 443
  }

  frontend_ip_configuration {
    name                 = local.app_gateway_frontend_ip_configuration_name
    public_ip_address_id = module.Application_Gateway_Public_IP_AU_East.id
  }

  dynamic "backend_address_pool" {
    for_each = local.backend_address_pools_au_east
    content {
      name  = backend_address_pool.value.name
      fqdns = backend_address_pool.value.fqdns
    }
  }

  dynamic "backend_http_settings" {
    for_each = local.backend_http_settings
    content {
      name                  = backend_http_settings.value.name
      cookie_based_affinity = backend_http_settings.value.cookie_based_affinity
      port                  = backend_http_settings.value.port
      protocol              = backend_http_settings.value.protocol
      request_timeout       = backend_http_settings.value.request_timeout
      probe_name            = backend_http_settings.value.probe_name
    }
  }

  dynamic "probe" {
    for_each = local.probes
    content {
      name                = probe.value.name
      protocol            = probe.value.protocol
      host                = probe.value.host
      path                = probe.value.path
      interval            = probe.value.interval
      timeout             = probe.value.timeout
      unhealthy_threshold = probe.value.unhealthy_threshold
      port                = probe.value.port
      dynamic "match" {
        for_each = probe.value.match != null ? [probe.value.match] : []
        content {
          status_code = match.value.status_code
        }
      }
    }
  }

  dynamic "http_listener" {
    for_each = local.http_listeners_au_east
    content {
      name                           = http_listener.value.name
      frontend_ip_configuration_name = http_listener.value.frontend_ip_configuration_name
      frontend_port_name             = http_listener.value.frontend_port_name
      protocol                       = http_listener.value.protocol
      ssl_certificate_name           = http_listener.value.ssl_certificate_name
      host_names                     = http_listener.value.host_names
      firewall_policy_id             = http_listener.value.firewall_policy_id
      require_sni                    = http_listener.value.require_sni
    }
  }

  dynamic "request_routing_rule" {
    for_each = local.request_routing_rules
    content {
      name                       = request_routing_rule.value.name
      rule_type                  = request_routing_rule.value.rule_type
      priority                   = request_routing_rule.value.priority
      http_listener_name         = request_routing_rule.value.http_listener_name
      backend_address_pool_name  = request_routing_rule.value.backend_address_pool_name
      backend_http_settings_name = request_routing_rule.value.backend_http_settings_name
      rewrite_rule_set_name      = request_routing_rule.value.rewrite_rule_set_name
    }
  }

  ssl_certificate {
    name                = "pscore.cloud.crt"
    key_vault_secret_id = "https://wapol-wpm-kv-usr.vault.azure.net/secrets/pscoreCloudCrt"
  }

  identity {
    type         = "UserAssigned"
    identity_ids = [azurerm_user_assigned_identity.application_gateway_identity_AU_East.id]
  }

  rewrite_rule_set {
    name = local.app_gateway_rewrite_rule_set_name

    rewrite_rule {
      name          = "rewrite-rule-1"
      rule_sequence = 100

      condition {
        variable = "var_host"
        pattern  = "wapol-agw-${var.environment}-2.pscore.cloud"
      }
      condition {
        variable = "var_uri_path"
        pattern  = "/referencedata/(.*)/item"
      }

      condition {
        variable = "var_query_string"
        pattern  = "lastUpdated=(.*)&skip=0&take=10000"
      }
      url {
        path         = "/referencedata/{var_uri_path_1}/item"
        query_string = "lastUpdated={var_query_string_1}"
      }
    }

    rewrite_rule {
      name          = "rewrite-rule-2"
      rule_sequence = 101

      condition {
        variable = "var_host"
        pattern  = "wapol-agw-${var.environment}-2.pscore.cloud"
      }
      condition {
        variable = "var_uri_path"
        pattern  = "/referencedata/(.*)/item"
      }

      condition {
        variable = "var_query_string"
        pattern  = "skip=0&take=10000"
      }
      url {
        path         = "/referencedata/{var_uri_path_1}/item"
        query_string = ""
      }
    }
  }

  firewall_policy_id = azurerm_web_application_firewall_policy.web_application_firewall_policy_au_east.id

  depends_on = [module.Application_Gateway_NSG_Rule_Default_Ports_AU_East]
  tags       = local.common_tags
}

resource "azurerm_user_assigned_identity" "application_gateway_identity_AU_East" {
  location            = var.location_au_east
  name                = "${var.customer}-${var.project}-agw-id-2-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Gateway_Resource_Group_AU_East.name
  tags                = local.common_tags
}

#create WAF policy
#global waf rule
resource "azurerm_web_application_firewall_policy" "web_application_firewall_policy_au_east" {
  name                = "${var.customer}-${var.project}-agw-waf-policy-2-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Gateway_Resource_Group_AU_East.name
  location            = var.location_au_east

  policy_settings {
    enabled                     = true
    mode                        = "Prevention"
    request_body_check          = false
    file_upload_limit_in_mb     = 100
    max_request_body_size_in_kb = 128
  }

  managed_rules {
    managed_rule_set {
      type    = "OWASP"
      version = "3.2"
    }
  }

  tags = local.common_tags
}

#per-site waf rule - api
resource "azurerm_web_application_firewall_policy" "web_application_firewall_policy_api_au_east" {
  name                = "${var.customer}-${var.project}-agw-waf-policy-api-2-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Gateway_Resource_Group_AU_East.name
  location            = var.location_au_east

  custom_rules {
    name      = "allowSpecificIpForStarlink"
    priority  = 1
    rule_type = "MatchRule"
    match_conditions {
      match_variables {
        variable_name = "RemoteAddr"
      }
      operator           = "IPMatch"
      negation_condition = false
      match_values       = ["*************/32", "************/32", "*************/32", "************/32", "*************/32", "*************/32"]
    }

    action = "Allow"
  }

  custom_rules {
    name      = "allowSpecificIpForSituationalAlertExternalApi"
    priority  = 2
    rule_type = "MatchRule"
    match_conditions {
      match_variables {
        variable_name = "RemoteAddr"
      }
      operator           = "IPMatch"
      negation_condition = false
      match_values       = ["***********/32", "***********/32"]
    }
    match_conditions {
      match_variables {
        variable_name = "RequestUri"
      }

      operator           = "BeginsWith"
      negation_condition = false
      match_values       = ["/situational-alert-external-api/v1/external"]
    }
    match_conditions {
      match_variables {
        variable_name = "RequestHeaders"
        selector      = "X-Api-Key"
      }

      operator           = "Regex"
      negation_condition = true
      match_values       = ["^$"]
    }

    action = "Allow"
  }

  custom_rules {
    name      = "geoAllowAU"
    priority  = 3
    rule_type = "MatchRule"

    match_conditions {
      match_variables {
        variable_name = "RemoteAddr"
      }

      operator           = "GeoMatch"
      negation_condition = true
      match_values       = ["AU"]
    }

    action = "Block"
  }

  custom_rules {
    name      = "allowBackendPath"
    priority  = 4
    rule_type = "MatchRule"

    match_conditions {
      match_variables {
        variable_name = "RequestUri"
      }

      operator           = "BeginsWith"
      negation_condition = false
      match_values       = ["/cad/", "/entity/", "/event-publisher/", "/persistence/", "/referencedata/", "/reference-data/", "/situational-alert-external-api/", "/situational-alert/"]
    }

    match_conditions {
      match_variables {
        variable_name = "RequestHeaders"
        selector      = "X-User-ID"
      }

      operator           = "Regex"
      negation_condition = false
      match_values       = ["[a-zA-Z0-9]"]
    }

    action = "Allow"
  }

  custom_rules {
    name      = "allowTransactionWithoutXUserIdHeader"
    priority  = 5
    rule_type = "MatchRule"

    match_conditions {
      match_variables {
        variable_name = "RequestUri"
      }

      operator           = "BeginsWith"
      negation_condition = false
      match_values       = ["/cad/p1/device/deregister", "/persistence/v1/data"]
    }

    action = "Allow"
  }

  custom_rules {
    name      = "blockAllMethodAsDefault"
    enabled   = false
    priority  = 6
    rule_type = "MatchRule"

    match_conditions {
      match_variables {
        variable_name = "RequestMethod"
      }

      operator           = "Regex"
      negation_condition = false
      match_values       = ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD", "TRACE", "CONNECT"]
    }

    action = "Block"
  }

  policy_settings {
    enabled                     = true
    mode                        = "Prevention"
    request_body_check          = false
    file_upload_limit_in_mb     = 100
    max_request_body_size_in_kb = 128
  }

  managed_rules {
    managed_rule_set {
      type    = "OWASP"
      version = "3.2"
    }
  }

  tags = local.common_tags
}

#per-site waf rule - kibana
resource "azurerm_web_application_firewall_policy" "web_application_firewall_policy_kibana_au_east" {
  name                = "${var.customer}-${var.project}-agw-waf-policy-kibana-2-${var.environment}"
  resource_group_name = data.azurerm_resource_group.WAPol_Gateway_Resource_Group_AU_East.name
  location            = var.location_au_east


  custom_rules {
    name      = "allowSpecificIpAndDomain"
    priority  = 1
    rule_type = "MatchRule"
    match_conditions {
      match_variables {
        variable_name = "RemoteAddr"
      }
      operator           = "IPMatch"
      negation_condition = false
      match_values       = ["***********/16"]
    }
    match_conditions {
      match_variables {
        variable_name = "RequestHeaders"
        selector      = "host"
      }

      operator           = "Contains"
      negation_condition = false
      match_values       = ["wapol-kibana2-usr.pscore.cloud"]
    }

    action = "Allow"
  }

  custom_rules {
    name      = "blockAllMethodAsDefault"
    priority  = 2
    rule_type = "MatchRule"

    match_conditions {
      match_variables {
        variable_name = "RequestMethod"
      }

      operator           = "Regex"
      negation_condition = false
      match_values       = ["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS", "HEAD", "TRACE", "CONNECT"]
    }

    action = "Block"
  }

  policy_settings {
    enabled                     = true
    mode                        = "Prevention"
    request_body_check          = false
    file_upload_limit_in_mb     = 100
    max_request_body_size_in_kb = 128
  }

  managed_rules {
    managed_rule_set {
      type    = "OWASP"
      version = "3.2"
    }
  }

  tags = local.common_tags
}
