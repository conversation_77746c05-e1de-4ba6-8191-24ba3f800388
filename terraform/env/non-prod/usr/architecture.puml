@startuml
!theme vibrant

title WAPol USR Environment Architecture

skinparam rectangle {
  BorderColor #5D6D7E
  BackgroundColor #A9CCE3
}

skinparam cloud {
  BorderColor #17A589
  BackgroundColor #D1F2EB
}

skinparam database {
  BorderColor #AF7AC5
  BackgroundColor #E8DAEF
}

skinparam node {
  BorderColor #E67E22
  BackgroundColor #FAD7A0
}

skinparam agent {
    BorderColor #3498DB
    BackgroundColor #D6EAF8
}


actor "External User/Service" as User

cloud "Azure Cloud" {

  package "Gateway VNet (********/16)\n<size:10>[network]</size>" {
    node "Application Gateway (WAF_v2)\n<size:10>[application_gateway]</size>" as AGW {
      agent "Public IP" as AGW_PIP
      agent "Listener (api, kibana)" as Listener
      agent "WAF Policy" as WAF
    }
    rectangle "App Gateway Subnet (********/24)" as AGW_Subnet
    AGW -- AGW_Subnet
  }

  package "Application VNet (********/16)\n<size:10>[network]</size>" {
    node "Azure Kubernetes Service (AKS)\n<size:10>[aks/aks_cluster]</size>" as AKS {
        agent "Istio Ingress Gateway" as Ingress
        agent "Public IP (Ingress)" as AKS_PIP_Ingress
        agent "Public IP (Egress)" as AKS_PIP_Egress
        rectangle "Workload Pods" as Pods
    }
    rectangle "AKS Subnet (********/24)" as AKS_Subnet
    AKS -- AKS_Subnet

    node "Virtual Appliance\n(Firewall/Proxy)" as VA
    database "Data Resources\n(SQL, Storage, etc)\n<size:10>[data_resources]</size>" as Data
    node "Service Bus\n<size:10>[servicebus]</size>" as SB
  }

  package "Management VNet (********/16)\n<size:10>[network]</size>" {
      node "Management VM\n<size:10>[management_vm]</size>" as MgmtVM
      node "Automation Account\n<size:10>[automation_account]</size>" as AA
  }

  package "Shared Services\n<size:10>[shared_services]</size>" {
    node "Log Analytics Workspace" as LAW
    node "Azure Key Vault" as AKV
  }

}

' --- Relationships ---

User --> AGW_PIP : HTTPS
AGW_PIP --> Listener
Listener --> WAF
WAF --> Ingress : Forwards valid traffic

' VNet Peerings
AGW_Subnet <--> AKS_Subnet : VNet Peering
AKS_Subnet <--> MgmtVM : VNet Peering

' AKS Internal and Egress
Ingress --> Pods
AKS_Subnet --> VA : Custom Route (**********/24, etc.)
Pods --> AKS_PIP_Egress : Default Egress

' Service Connections
Pods --> Data
Pods --> SB
Pods --> LAW : Logs & Metrics
AKS --> LAW : Cluster Logs

' Management & Secrets
MgmtVM --> AKS : Management (SSH, kubectl)
AGW --> AKV : SSL Certificate
Pods --> AKV : Application Secrets

@enduml
